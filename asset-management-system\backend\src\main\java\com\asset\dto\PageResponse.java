package com.asset.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageResponse<T> {
    
    private List<T> records;
    private long total;
    private long size;
    private long current;
    private long pages;
    
    public static <T> PageResponse<T> of(List<T> records, long total, long size, long current) {
        long pages = (total + size - 1) / size;
        return new PageResponse<>(records, total, size, current, pages);
    }
}