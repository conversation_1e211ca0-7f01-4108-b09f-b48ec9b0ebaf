<template>
  <div class="install-container">
    <div class="page-header">
      <h2>安装管理</h2>
      <p>管理资产的安装操作和位置记录</p>
    </div>

    <!-- 搜索筛选区域 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="资产编号">
          <el-input
            v-model="searchForm.assetCode"
            placeholder="请输入资产编号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="安装位置">
          <el-input
            v-model="searchForm.installLocation"
            placeholder="请输入安装位置"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="已入库" value="RECEIVED" />
            <el-option label="已安装" value="INSTALLED" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>资产安装管理</span>
          <el-button type="primary" @click="handleBatchInstall">
            <el-icon><Tools /></el-icon>
            批量安装
          </el-button>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="tableData"
        @selection-change="handleSelectionChange"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="assetCode" label="资产编号" width="120" />
        <el-table-column prop="serialNumber" label="序列号" width="150" />
        <el-table-column prop="productModel" label="产品型号" width="150" />
        <el-table-column prop="productType" label="产品类型" width="120" />
        <el-table-column prop="currentLocation" label="当前位置" width="180" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="installDate" label="安装日期" width="120">
          <template #default="{ row }">
            {{ row.installDate || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="installerName" label="安装人员" width="120">
          <template #default="{ row }">
            {{ row.installerName || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="row.status === 'RECEIVED'"
              type="primary"
              size="small"
              @click="handleInstall(row)"
            >
              安装
            </el-button>
            <el-button
              v-if="row.status === 'INSTALLED'"
              type="warning"
              size="small"
              @click="handleRelocate(row)"
            >
              迁移
            </el-button>
            <el-button
              type="info"
              size="small"
              @click="handleViewDetail(row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 安装对话框 -->
    <el-dialog
      v-model="installDialogVisible"
      title="资产安装"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="installFormRef"
        :model="installForm"
        :rules="installRules"
        label-width="100px"
      >
        <el-form-item label="资产编号">
          <el-input v-model="installForm.assetCode" disabled />
        </el-form-item>
        <el-form-item label="产品型号">
          <el-input v-model="installForm.productModel" disabled />
        </el-form-item>
        <el-form-item label="安装位置" prop="installLocation">
          <el-input
            v-model="installForm.installLocation"
            placeholder="请输入安装位置"
          />
        </el-form-item>
        <el-form-item label="安装日期" prop="installDate">
          <el-date-picker
            v-model="installForm.installDate"
            type="date"
            placeholder="选择安装日期"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="安装人员" prop="installerName">
          <el-input
            v-model="installForm.installerName"
            placeholder="请输入安装人员姓名"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="installForm.remarks"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="installDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmInstall" :loading="submitting">
          确认安装
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Tools } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const tableData = ref([])
const selectedRows = ref([])
const installDialogVisible = ref(false)
const installFormRef = ref()

// 搜索表单
const searchForm = reactive({
  assetCode: '',
  installLocation: '',
  status: ''
})

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

// 安装表单
const installForm = reactive({
  assetId: null,
  assetCode: '',
  productModel: '',
  installLocation: '',
  installDate: new Date(),
  installerName: '',
  remarks: ''
})

// 表单验证规则
const installRules = {
  installLocation: [
    { required: true, message: '请输入安装位置', trigger: 'blur' }
  ],
  installDate: [
    { required: true, message: '请选择安装日期', trigger: 'change' }
  ],
  installerName: [
    { required: true, message: '请输入安装人员姓名', trigger: 'blur' }
  ]
}

// 方法
const loadData = async () => {
  loading.value = true
  try {
    // TODO: 调用真实API获取安装数据
    // const response = await getInstallAssets(searchForm, pagination)
    // tableData.value = response.data.records
    // pagination.total = response.data.total

    tableData.value = []
    pagination.total = 0
  } catch (error) {
    ElMessage.error('加载数据失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    assetCode: '',
    installLocation: '',
    status: ''
  })
  handleSearch()
}

const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

const handleInstall = (row) => {
  Object.assign(installForm, {
    assetId: row.id,
    assetCode: row.assetCode,
    productModel: row.productModel,
    installLocation: '',
    installDate: new Date(),
    installerName: '',
    remarks: ''
  })
  installDialogVisible.value = true
}

const handleConfirmInstall = async () => {
  try {
    await installFormRef.value.validate()
    submitting.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('安装成功')
    installDialogVisible.value = false
    loadData()
  } catch (error) {
    if (error.message) {
      ElMessage.error('安装失败: ' + error.message)
    }
  } finally {
    submitting.value = false
  }
}

const handleBatchInstall = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要安装的资产')
    return
  }
  
  ElMessageBox.confirm(
    `确定要批量安装选中的 ${selectedRows.value.length} 个资产吗？`,
    '批量安装确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('批量安装成功')
    loadData()
  })
}

const handleRelocate = (row) => {
  // 跳转到位置变更页面
  console.log('位置迁移:', row)
}

const handleViewDetail = (row) => {
  // 跳转到资产详情页
  console.log('查看详情:', row)
}

const handleDialogClose = () => {
  installFormRef.value?.resetFields()
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  loadData()
}

const handleCurrentChange = (page) => {
  pagination.current = page
  loadData()
}

// 工具方法
const getStatusType = (status) => {
  const statusMap = {
    'PENDING': 'warning',
    'RECEIVED': 'success',
    'INSTALLED': 'info',
    'OUTBOUND': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'PENDING': '待入库',
    'RECEIVED': '已入库',
    'INSTALLED': '已安装',
    'OUTBOUND': '已出库'
  }
  return statusMap[status] || status
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.install-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 0;
}

.table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 响应式设计 */
@media (min-width: 1920px) {
  .install-container {
    padding: 24px;
  }
  
  .search-form .el-form-item {
    margin-right: 24px;
  }
}

@media (min-width: 2560px) {
  .install-container {
    padding: 32px;
  }
  
  .page-header h2 {
    font-size: 28px;
  }
  
  .page-header p {
    font-size: 16px;
  }
}
</style>
