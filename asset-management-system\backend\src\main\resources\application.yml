server:
  port: 8080
  # 移除context-path，让前端页面在根路径访问

spring:
  application:
    name: asset-management-system
  
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************************************
    username: root
    password: 123456
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: DatebookHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true

  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  # 明确指定不使用XML映射文件
  mapper-locations: ""
  type-aliases-package: com.asset.entity

jwt:
  secret: assetManagementSystemSecretKey2024
  expiration: 86400000 # 24 hours

logging:
  level:
    com.asset: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"