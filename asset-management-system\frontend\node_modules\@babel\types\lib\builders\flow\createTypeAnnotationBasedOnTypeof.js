"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _index = require("../generated/index.js");
var _default = exports.default = createTypeAnnotationBasedOnTypeof;
function createTypeAnnotationBasedOnTypeof(type) {
  switch (type) {
    case "string":
      return (0, _index.stringTypeAnnotation)();
    case "number":
      return (0, _index.numberTypeAnnotation)();
    case "undefined":
      return (0, _index.voidTypeAnnotation)();
    case "boolean":
      return (0, _index.booleanTypeAnnotation)();
    case "function":
      return (0, _index.genericTypeAnnotation)((0, _index.identifier)("Function"));
    case "object":
      return (0, _index.genericTypeAnnotation)((0, _index.identifier)("Object"));
    case "symbol":
      return (0, _index.genericTypeAnnotation)((0, _index.identifier)("Symbol"));
    case "bigint":
      return (0, _index.anyTypeAnnotation)();
  }
  throw new Error("Invalid typeof value: " + type);
}

//# sourceMappingURL=createTypeAnnotationBasedOnTypeof.js.map
