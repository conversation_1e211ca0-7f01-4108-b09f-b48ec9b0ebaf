<template>
  <div class="user-form-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <el-page-header @back="handleBack">
        <template #content>
          <span class="page-title">{{ isEdit ? '编辑用户' : '创建用户' }}</span>
        </template>
      </el-page-header>
    </div>

    <!-- 用户表单 -->
    <el-card>
      <el-form
        ref="formRef"
        :model="userForm"
        :rules="formRules"
        label-width="120px"
        class="user-form"
      >
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="用户名" prop="username">
              <el-input
                v-model="userForm.username"
                placeholder="请输入用户名"
                :disabled="isEdit"
                maxlength="50"
                show-word-limit
                @blur="checkUsernameAvailable"
              />
              <div v-if="usernameCheck.loading" class="form-tip">
                <el-icon class="loading"><Loading /></el-icon>
                正在检查用户名...
              </div>
              <div v-else-if="usernameCheck.message" class="form-tip" :class="usernameCheck.type">
                {{ usernameCheck.message }}
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="真实姓名" prop="realName">
              <el-input
                v-model="userForm.realName"
                placeholder="请输入真实姓名"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input
                v-model="userForm.email"
                placeholder="请输入邮箱"
                type="email"
                maxlength="100"
                @blur="checkEmailAvailable"
              />
              <div v-if="emailCheck.loading" class="form-tip">
                <el-icon class="loading"><Loading /></el-icon>
                正在检查邮箱...
              </div>
              <div v-else-if="emailCheck.message" class="form-tip" :class="emailCheck.type">
                {{ emailCheck.message }}
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="电话" prop="phone">
              <el-input
                v-model="userForm.phone"
                placeholder="请输入电话号码"
                maxlength="20"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="部门" prop="department">
              <el-input
                v-model="userForm.department"
                placeholder="请输入部门"
                maxlength="100"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="角色" prop="role">
              <el-select v-model="userForm.role" placeholder="请选择角色" style="width: 100%">
                <el-option
                  v-for="role in roleOptions"
                  :key="role.value"
                  :label="role.label"
                  :value="role.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-switch
                v-model="userForm.status"
                active-text="正常"
                inactive-text="禁用"
                :active-value="'ACTIVE'"
                :inactive-value="'INACTIVE'"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="密码" prop="password">
              <el-input
                v-model="userForm.password"
                type="password"
                placeholder="请输入密码"
                show-password
                maxlength="50"
              />
              <div class="form-tip info">
                {{ isEdit ? '留空表示不修改密码' : '密码长度至少6位' }}
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input
                v-model="userForm.confirmPassword"
                type="password"
                placeholder="请再次输入密码"
                show-password
                maxlength="50"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            {{ isEdit ? '更新用户' : '创建用户' }}
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button @click="handleBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import { 
  createUser, 
  updateUser, 
  getUserDetail, 
  checkUsername, 
  checkEmail 
} from '@/api/user'

const route = useRoute()
const router = useRouter()

// 是否为编辑模式
const isEdit = computed(() => !!route.params.id)

// 表单引用
const formRef = ref()

// 表单数据
const userForm = reactive({
  username: '',
  realName: '',
  email: '',
  phone: '',
  department: '',
  role: 'USER',
  status: 'ACTIVE',
  password: '',
  confirmPassword: ''
})

// 角色选项
const roleOptions = [
  { label: '普通用户', value: 'USER' },
  { label: '管理员', value: 'ADMIN' }
]

// 用户名检查状态
const usernameCheck = reactive({
  loading: false,
  message: '',
  type: ''
})

// 邮箱检查状态
const emailCheck = reactive({
  loading: false,
  message: '',
  type: ''
})

// 提交加载状态
const submitLoading = ref(false)

// 表单验证规则
const formRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在3-50个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' },
    { max: 50, message: '姓名长度不能超过50个字符', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  password: [
    ...(isEdit.value ? [] : [{ required: true, message: '请输入密码', trigger: 'blur' }]),
    { min: 6, message: '密码长度至少6位', trigger: 'blur' }
  ],
  confirmPassword: [
    {
      validator: (rule, value, callback) => {
        if (userForm.password && value !== userForm.password) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 检查用户名是否可用
const checkUsernameAvailable = async () => {
  if (!userForm.username || isEdit.value) return
  
  usernameCheck.loading = true
  usernameCheck.message = ''
  
  try {
    const response = await checkUsername(userForm.username)
    if (response.data) {
      usernameCheck.message = '用户名可用'
      usernameCheck.type = 'success'
    } else {
      usernameCheck.message = '用户名已存在'
      usernameCheck.type = 'error'
    }
  } catch (error) {
    usernameCheck.message = '检查失败'
    usernameCheck.type = 'error'
  } finally {
    usernameCheck.loading = false
  }
}

// 检查邮箱是否可用
const checkEmailAvailable = async () => {
  if (!userForm.email) return
  
  emailCheck.loading = true
  emailCheck.message = ''
  
  try {
    const response = await checkEmail(userForm.email)
    if (response.data) {
      emailCheck.message = '邮箱可用'
      emailCheck.type = 'success'
    } else {
      emailCheck.message = '邮箱已存在'
      emailCheck.type = 'error'
    }
  } catch (error) {
    emailCheck.message = '检查失败'
    emailCheck.type = 'error'
  } finally {
    emailCheck.loading = false
  }
}

// 加载用户数据
const loadUserData = async () => {
  if (!isEdit.value) return
  
  try {
    const response = await getUserDetail(route.params.id)
    Object.assign(userForm, response.data)
    // 清空密码字段
    userForm.password = ''
    userForm.confirmPassword = ''
  } catch (error) {
    ElMessage.error('加载用户数据失败')
    handleBack()
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    submitLoading.value = true
    
    const submitData = { ...userForm }
    delete submitData.confirmPassword
    
    // 如果是编辑模式且密码为空，则不提交密码
    if (isEdit.value && !submitData.password) {
      delete submitData.password
    }
    
    if (isEdit.value) {
      await updateUser(route.params.id, submitData)
      ElMessage.success('用户更新成功')
    } else {
      await createUser(submitData)
      ElMessage.success('用户创建成功')
    }
    
    handleBack()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitLoading.value = false
  }
}

// 重置表单
const handleReset = () => {
  formRef.value.resetFields()
  usernameCheck.message = ''
  emailCheck.message = ''
}

// 返回列表页
const handleBack = () => {
  router.push('/users')
}

onMounted(() => {
  loadUserData()
})
</script>

<style scoped lang="scss">
.user-form-container {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
}

.user-form {
  max-width: 800px;
}

.form-tip {
  font-size: 12px;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
  
  &.success {
    color: #67c23a;
  }
  
  &.error {
    color: #f56c6c;
  }
  
  &.info {
    color: #909399;
  }
  
  .loading {
    animation: rotating 2s linear infinite;
  }
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>