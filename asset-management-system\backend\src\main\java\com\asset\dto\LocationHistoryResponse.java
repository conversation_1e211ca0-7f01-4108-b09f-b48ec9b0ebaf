package com.asset.dto;

import com.asset.entity.LocationHistory;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class LocationHistoryResponse {
    
    private Long id;
    private Long assetId;
    private String assetCode;
    private String productModel;
    private String fromLocation;
    private String toLocation;
    private String moveReason;
    private String operator;
    private LocalDateTime movedAt;
    private String username;
    private String userRealName;
    
    public static LocationHistoryResponse fromLocationHistory(LocationHistory locationHistory) {
        LocationHistoryResponse response = new LocationHistoryResponse();
        response.setId(locationHistory.getId());
        response.setAssetId(locationHistory.getAssetId());
        response.setFromLocation(locationHistory.getFromLocation());
        response.setToLocation(locationHistory.getToLocation());
        response.setMoveReason(locationHistory.getMoveReason());
        response.setOperator(locationHistory.getOperator());
        response.setMovedAt(locationHistory.getMovedAt());
        
        // 如果包含资产信息
        if (locationHistory.getAsset() != null) {
            response.setAssetCode(locationHistory.getAsset().getAssetCode());
            response.setProductModel(locationHistory.getAsset().getProductModel());
            
            if (locationHistory.getAsset().getUser() != null) {
                response.setUsername(locationHistory.getAsset().getUser().getUsername());
                response.setUserRealName(locationHistory.getAsset().getUser().getRealName());
            }
        }
        
        return response;
    }
}