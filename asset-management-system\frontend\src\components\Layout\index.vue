<template>
  <div class="layout-container">
    <!-- 侧边栏 -->
    <aside class="sidebar" :class="{ collapsed: isCollapsed }">
      <div class="sidebar-header">
        <div class="logo">
          <el-icon size="32" color="#409eff">
            <Box />
          </el-icon>
          <transition name="fade">
            <span v-show="!isCollapsed" class="logo-text">资产管理</span>
          </transition>
        </div>
      </div>
      
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapsed"
        :unique-opened="true"
        router
        class="sidebar-menu"
        background-color="#001529"
        text-color="#ffffff"
        active-text-color="#409eff"
      >
        <!-- 首页 -->
        <el-menu-item index="/" class="menu-item">
          <el-icon>
            <Box />
          </el-icon>
          <template #title>首页</template>
        </el-menu-item>

        <!-- 资产管理 -->
        <el-sub-menu index="assets" class="menu-group">
          <template #title>
            <el-icon>
              <Box />
            </el-icon>
            <span>资产管理</span>
          </template>
          <el-menu-item index="/assets" class="sub-menu-item">
            <el-icon><List /></el-icon>
            <template #title>资产列表</template>
          </el-menu-item>
          <el-menu-item index="/assets/create" class="sub-menu-item">
            <el-icon><Plus /></el-icon>
            <template #title>新增资产</template>
          </el-menu-item>
          <el-menu-item index="/import-export" class="sub-menu-item">
            <el-icon><Upload /></el-icon>
            <template #title>批量导入</template>
          </el-menu-item>
          <el-menu-item index="/assets/search" class="sub-menu-item">
            <el-icon><Search /></el-icon>
            <template #title>高级搜索</template>
          </el-menu-item>
        </el-sub-menu>

        <!-- 资产操作 -->
        <el-sub-menu index="operations" class="menu-group">
          <template #title>
            <el-icon>
              <Operation />
            </el-icon>
            <span>资产操作</span>
          </template>
          <el-menu-item index="/operations/inbound" class="sub-menu-item">
            <el-icon><Download /></el-icon>
            <template #title>入库管理</template>
          </el-menu-item>
          <el-menu-item index="/operations/install" class="sub-menu-item">
            <el-icon><Tools /></el-icon>
            <template #title>安装管理</template>
          </el-menu-item>
          <el-menu-item index="/location-tracking" class="sub-menu-item">
            <el-icon><LocationFilled /></el-icon>
            <template #title>位置变更</template>
          </el-menu-item>
          <el-menu-item index="/operations/outbound" class="sub-menu-item">
            <el-icon><Upload /></el-icon>
            <template #title>出库管理</template>
          </el-menu-item>
        </el-sub-menu>

        <!-- 库存统计 -->
        <el-sub-menu index="statistics" class="menu-group">
          <template #title>
            <el-icon>
              <DataAnalysis />
            </el-icon>
            <span>库存统计</span>
          </template>
          <el-menu-item index="/inventory/statistics" class="sub-menu-item">
            <el-icon><DataBoard /></el-icon>
            <template #title>统计概览</template>
          </el-menu-item>
          <el-menu-item index="/statistics/by-type" class="sub-menu-item">
            <el-icon><PieChart /></el-icon>
            <template #title>分类统计</template>
          </el-menu-item>
          <el-menu-item index="/statistics/by-location" class="sub-menu-item">
            <el-icon><LocationFilled /></el-icon>
            <template #title>位置统计</template>
          </el-menu-item>
          <el-menu-item index="/statistics" class="sub-menu-item">
            <el-icon><TrendCharts /></el-icon>
            <template #title>状态统计</template>
          </el-menu-item>
        </el-sub-menu>

        <!-- 用户管理 (仅管理员可见) -->
        <el-sub-menu
          v-if="authStore.user?.role === 'ADMIN'"
          index="users"
          class="menu-group"
        >
          <template #title>
            <el-icon>
              <User />
            </el-icon>
            <span>用户管理</span>
          </template>
          <el-menu-item index="/users" class="sub-menu-item">
            <el-icon><UserFilled /></el-icon>
            <template #title>用户列表</template>
          </el-menu-item>
          <el-menu-item index="/users/create" class="sub-menu-item">
            <el-icon><Plus /></el-icon>
            <template #title>创建用户</template>
          </el-menu-item>
          <el-menu-item index="/users/permissions" class="sub-menu-item">
            <el-icon><Key /></el-icon>
            <template #title>权限管理</template>
          </el-menu-item>
        </el-sub-menu>
      </el-menu>
    </aside>

    <!-- 主要内容区域 -->
    <div class="main-container">
      <!-- 顶部导航 -->
      <header class="main-header">
        <div class="header-left">
          <el-button
            link
            @click="toggleSidebar"
            class="collapse-btn"
          >
            <el-icon size="20">
              <Expand v-if="isCollapsed" />
              <Fold v-else />
            </el-icon>
          </el-button>
          
          <!-- 面包屑导航 -->
          <el-breadcrumb separator="/" class="breadcrumb">
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item 
              v-for="item in breadcrumbList" 
              :key="item.path"
              :to="item.path"
            >
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>

        <div class="header-right">
          <!-- 消息通知 -->
          <el-badge :value="12" class="header-badge">
            <el-button link class="header-btn">
              <el-icon size="18">
                <Bell />
              </el-icon>
            </el-button>
          </el-badge>

          <!-- 全屏切换 -->
          <el-button link @click="toggleFullscreen" class="header-btn">
            <el-icon size="18">
              <FullScreen />
            </el-icon>
          </el-button>

          <!-- 用户菜单 -->
          <el-dropdown @command="handleUserMenuCommand" class="user-dropdown">
            <div class="user-info">
              <el-avatar :size="32" class="user-avatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <span class="username">{{ authStore.userName }}</span>
              <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人中心
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>
                  系统设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </header>

      <!-- 主要内容 -->
      <main class="main-content">
        <div class="content-wrapper">
          <router-view v-slot="{ Component }">
            <transition name="fade-transform" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import {
  Box,
  Menu,
  Fold,
  Expand,
  User,
  ArrowDown,
  Setting,
  SwitchButton,
  List,
  Plus,
  Upload,
  Search,
  Operation,
  Download,
  Tools,
  LocationFilled,
  DataAnalysis,
  DataBoard,
  PieChart,
  TrendCharts,
  UserFilled,
  Key,
  Bell,
  FullScreen,
  Aim
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

const isCollapsed = ref(false)

// 当前激活的菜单项
const activeMenu = computed(() => {
  const path = route.path
  // 根据路径匹配对应的菜单项
  if (path === '/') return '/'
  if (path.startsWith('/assets')) return path
  if (path.startsWith('/operations')) return path
  if (path.startsWith('/location-tracking')) return '/location-tracking'
  if (path.startsWith('/import-export')) return '/import-export'
  if (path.startsWith('/inventory') || path.startsWith('/statistics')) return path
  if (path.startsWith('/users')) return path
  return path
})

// 菜单路由
const menuRoutes = computed(() => {
  return router.options.routes
    .find(r => r.path === '/')
    ?.children || []
})

// 面包屑导航
const breadcrumbList = computed(() => {
  const matched = route.matched.filter(item => item.meta?.title)
  return matched.map(item => ({
    path: item.path,
    title: item.meta.title
  }))
})

// 权限检查
const hasPermission = (route) => {
  if (!route.meta?.roles) return true
  return route.meta.roles.includes(authStore.user?.role)
}

// 切换侧边栏
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}

// 全屏切换
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
  } else {
    document.exitFullscreen()
  }
}

// 用户菜单操作
const handleUserMenuCommand = async (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人中心功能开发中...')
      break
    case 'settings':
      ElMessage.info('系统设置功能开发中...')
      break
    case 'logout':
      await handleLogout()
      break
  }
}

// 退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await authStore.logout()
    router.push('/login')
    ElMessage.success('已退出登录')
  } catch (error) {
    // 用户取消了操作
  }
}

onMounted(() => {
  // 初始化侧边栏状态
  const savedCollapsed = localStorage.getItem('sidebarCollapsed')
  if (savedCollapsed !== null) {
    isCollapsed.value = JSON.parse(savedCollapsed)
  }
})

// 保存侧边栏状态
watch(() => isCollapsed.value, (newVal) => {
  localStorage.setItem('sidebarCollapsed', JSON.stringify(newVal))
})
</script>

<style scoped lang="scss">
.layout-container {
  display: flex;
  height: 100vh;
  background: #f0f2f5;
}

.sidebar {
  width: 240px;
  background: #001529;
  transition: width 0.3s ease;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 1000;

  &.collapsed {
    width: 64px;
  }
}

.sidebar-header {
  height: 64px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  border-bottom: 1px solid #1f2328;
}

.logo {
  display: flex;
  align-items: center;
  color: white;
  font-size: 18px;
  font-weight: 600;
}

.logo-text {
  margin-left: 12px;
  white-space: nowrap;
}

.sidebar-menu {
  height: calc(100vh - 64px);
  border-right: none;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #4a4a4a;
    border-radius: 2px;
  }
}

.menu-item {
  margin: 4px 8px;
  border-radius: 6px;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(64, 158, 255, 0.1) !important;
  }

  &.is-active {
    background: linear-gradient(135deg, #409eff 0%, #36a3f7 100%) !important;
    color: white !important;
  }
}

/* 菜单组样式 */
.menu-group {
  margin: 4px 8px;
  border-radius: 6px;
  overflow: hidden;

  :deep(.el-sub-menu__title) {
    height: 56px;
    line-height: 56px;
    color: #ffffff;
    font-weight: 500;
    border-radius: 6px;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(64, 158, 255, 0.1) !important;
    }

    .el-icon {
      margin-right: 8px;
      color: #409eff;
    }
  }

  :deep(.el-menu) {
    background-color: transparent;
  }
}

/* 子菜单项样式 */
.sub-menu-item {
  margin: 2px 16px 2px 24px;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;

  &:hover {
    background: rgba(64, 158, 255, 0.15) !important;
    transform: translateX(4px);
  }

  &.is-active {
    background: linear-gradient(135deg, #409eff 0%, #36a3f7 100%) !important;
    color: white !important;
    transform: translateX(4px);
  }

  :deep(.el-menu-item) {
    height: 44px;
    line-height: 44px;
    font-size: 14px;
  }

  .el-icon {
    margin-right: 8px;
    font-size: 14px;
  }
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.main-header {
  height: 64px;
  background: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 999;
}

.header-left {
  display: flex;
  align-items: center;
}

.collapse-btn {
  margin-right: 16px;
  color: #666;
  
  &:hover {
    color: #409eff;
  }
}

.breadcrumb {
  font-size: 14px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-badge {
  .header-btn {
    color: #666;
    
    &:hover {
      color: #409eff;
    }
  }
}

.header-btn {
  color: #666;
  
  &:hover {
    color: #409eff;
  }
}

.user-dropdown {
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background 0.3s ease;

  &:hover {
    background: #f5f5f5;
  }
}

.username {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.dropdown-icon {
  color: #999;
  font-size: 12px;
}

.main-content {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.content-wrapper {
  height: 100%;
  padding: 24px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-thumb {
    background: #d9d9d9;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #bfbfbf;
  }
}

// 过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s ease;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

// Element Plus 样式覆盖
:deep(.el-menu-item) {
  &.is-active {
    background: linear-gradient(135deg, #409eff 0%, #36a3f7 100%) !important;
    
    &::before {
      display: none;
    }
  }
}

:deep(.el-breadcrumb__item) {
  .el-breadcrumb__inner {
    color: #666;
    font-weight: 400;
    
    &.is-link:hover {
      color: #409eff;
    }
  }
  
  &:last-child .el-breadcrumb__inner {
    color: #409eff;
    font-weight: 500;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: -240px;
    z-index: 2000;
    
    &.collapsed {
      left: 0;
      width: 240px;
    }
  }
  
  .main-container {
    margin-left: 0;
  }
  
  .header-left {
    .breadcrumb {
      display: none;
    }
  }
  
  .user-info .username {
    display: none;
  }
}

/* 1920×1080分辨率优化 */
@media (min-width: 1920px) {
  .sidebar {
    width: 280px;

    &.collapsed {
      width: 72px;
    }
  }

  .sidebar-header {
    height: 72px;
    padding: 0 20px;
  }

  .logo {
    font-size: 20px;
  }

  .logo-text {
    margin-left: 16px;
  }

  .sidebar-menu {
    height: calc(100vh - 72px);
  }

  .menu-item {
    margin: 6px 12px;

    :deep(.el-menu-item) {
      height: 60px;
      line-height: 60px;
      font-size: 16px;
    }
  }

  .menu-group {
    margin: 6px 12px;

    :deep(.el-sub-menu__title) {
      height: 60px;
      line-height: 60px;
      font-size: 16px;
    }
  }

  .sub-menu-item {
    margin: 3px 20px 3px 32px;

    :deep(.el-menu-item) {
      height: 48px;
      line-height: 48px;
      font-size: 15px;
    }
  }

  .main-header {
    height: 72px;
    padding: 0 32px;
  }

  .breadcrumb {
    font-size: 16px;
  }
}

/* 2K分辨率优化 */
@media (min-width: 2560px) {
  .sidebar {
    width: 320px;

    &.collapsed {
      width: 80px;
    }
  }

  .sidebar-header {
    height: 80px;
    padding: 0 24px;
  }

  .logo {
    font-size: 24px;
  }

  .logo-text {
    margin-left: 20px;
  }

  .sidebar-menu {
    height: calc(100vh - 80px);
  }

  .menu-item {
    margin: 8px 16px;

    :deep(.el-menu-item) {
      height: 64px;
      line-height: 64px;
      font-size: 18px;
    }
  }

  .menu-group {
    margin: 8px 16px;

    :deep(.el-sub-menu__title) {
      height: 64px;
      line-height: 64px;
      font-size: 18px;
    }
  }

  .sub-menu-item {
    margin: 4px 24px 4px 40px;

    :deep(.el-menu-item) {
      height: 52px;
      line-height: 52px;
      font-size: 16px;
    }
  }

  .main-header {
    height: 80px;
    padding: 0 40px;
  }

  .breadcrumb {
    font-size: 18px;
  }

  .header-badge {
    .el-badge__content {
      font-size: 14px;
    }
  }
}
</style>