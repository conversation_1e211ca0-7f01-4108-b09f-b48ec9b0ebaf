<template>
  <div class="layout-container">
    <!-- 侧边栏 -->
    <aside class="sidebar" :class="{ collapsed: isCollapsed }">
      <div class="sidebar-header">
        <div class="logo">
          <el-icon size="32" color="#409eff">
            <Box />
          </el-icon>
          <transition name="fade">
            <span v-show="!isCollapsed" class="logo-text">资产管理</span>
          </transition>
        </div>
      </div>
      
      <el-menu
        :default-active="$route.path"
        :collapse="isCollapsed"
        :unique-opened="true"
        router
        class="sidebar-menu"
        background-color="#001529"
        text-color="#ffffff"
        active-text-color="#409eff"
      >
        <template v-for="route in menuRoutes" :key="route.path">
          <el-menu-item
            v-if="!route.meta?.hidden && hasPermission(route)"
            :index="route.path"
            class="menu-item"
          >
            <el-icon>
              <component :is="route.meta.icon" />
            </el-icon>
            <template #title>{{ route.meta.title }}</template>
          </el-menu-item>
        </template>
      </el-menu>
    </aside>

    <!-- 主要内容区域 -->
    <div class="main-container">
      <!-- 顶部导航 -->
      <header class="main-header">
        <div class="header-left">
          <el-button
            link
            @click="toggleSidebar"
            class="collapse-btn"
          >
            <el-icon size="20">
              <Expand v-if="isCollapsed" />
              <Fold v-else />
            </el-icon>
          </el-button>
          
          <!-- 面包屑导航 -->
          <el-breadcrumb separator="/" class="breadcrumb">
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item 
              v-for="item in breadcrumbList" 
              :key="item.path"
              :to="item.path"
            >
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>

        <div class="header-right">
          <!-- 消息通知 -->
          <el-badge :value="12" class="header-badge">
            <el-button link class="header-btn">
              <el-icon size="18">
                <Bell />
              </el-icon>
            </el-button>
          </el-badge>

          <!-- 全屏切换 -->
          <el-button link @click="toggleFullscreen" class="header-btn">
            <el-icon size="18">
              <FullScreen />
            </el-icon>
          </el-button>

          <!-- 用户菜单 -->
          <el-dropdown @command="handleUserMenuCommand" class="user-dropdown">
            <div class="user-info">
              <el-avatar :size="32" class="user-avatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <span class="username">{{ authStore.userName }}</span>
              <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人中心
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>
                  系统设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </header>

      <!-- 主要内容 -->
      <main class="main-content">
        <div class="content-wrapper">
          <router-view v-slot="{ Component }">
            <transition name="fade-transform" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import {
  Box,
  Menu,
  Fold,
  Expand,
  User,
  ArrowDown,
  Setting,
  SwitchButton
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

const isCollapsed = ref(false)

// 菜单路由
const menuRoutes = computed(() => {
  return router.options.routes
    .find(r => r.path === '/')
    ?.children || []
})

// 面包屑导航
const breadcrumbList = computed(() => {
  const matched = route.matched.filter(item => item.meta?.title)
  return matched.map(item => ({
    path: item.path,
    title: item.meta.title
  }))
})

// 权限检查
const hasPermission = (route) => {
  if (!route.meta?.roles) return true
  return route.meta.roles.includes(authStore.user?.role)
}

// 切换侧边栏
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}

// 全屏切换
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
  } else {
    document.exitFullscreen()
  }
}

// 用户菜单操作
const handleUserMenuCommand = async (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人中心功能开发中...')
      break
    case 'settings':
      ElMessage.info('系统设置功能开发中...')
      break
    case 'logout':
      await handleLogout()
      break
  }
}

// 退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await authStore.logout()
    router.push('/login')
    ElMessage.success('已退出登录')
  } catch (error) {
    // 用户取消了操作
  }
}

onMounted(() => {
  // 初始化侧边栏状态
  const savedCollapsed = localStorage.getItem('sidebarCollapsed')
  if (savedCollapsed !== null) {
    isCollapsed.value = JSON.parse(savedCollapsed)
  }
})

// 保存侧边栏状态
watch(() => isCollapsed.value, (newVal) => {
  localStorage.setItem('sidebarCollapsed', JSON.stringify(newVal))
})
</script>

<style scoped lang="scss">
.layout-container {
  display: flex;
  height: 100vh;
  background: #f0f2f5;
}

.sidebar {
  width: 240px;
  background: #001529;
  transition: width 0.3s ease;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 1000;

  &.collapsed {
    width: 64px;
  }
}

.sidebar-header {
  height: 64px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  border-bottom: 1px solid #1f2328;
}

.logo {
  display: flex;
  align-items: center;
  color: white;
  font-size: 18px;
  font-weight: 600;
}

.logo-text {
  margin-left: 12px;
  white-space: nowrap;
}

.sidebar-menu {
  height: calc(100vh - 64px);
  border-right: none;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #4a4a4a;
    border-radius: 2px;
  }
}

.menu-item {
  margin: 4px 8px;
  border-radius: 6px;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(64, 158, 255, 0.1) !important;
  }

  &.is-active {
    background: linear-gradient(135deg, #409eff 0%, #36a3f7 100%) !important;
    color: white !important;
  }
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.main-header {
  height: 64px;
  background: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 999;
}

.header-left {
  display: flex;
  align-items: center;
}

.collapse-btn {
  margin-right: 16px;
  color: #666;
  
  &:hover {
    color: #409eff;
  }
}

.breadcrumb {
  font-size: 14px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-badge {
  .header-btn {
    color: #666;
    
    &:hover {
      color: #409eff;
    }
  }
}

.header-btn {
  color: #666;
  
  &:hover {
    color: #409eff;
  }
}

.user-dropdown {
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background 0.3s ease;

  &:hover {
    background: #f5f5f5;
  }
}

.username {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.dropdown-icon {
  color: #999;
  font-size: 12px;
}

.main-content {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.content-wrapper {
  height: 100%;
  padding: 24px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-thumb {
    background: #d9d9d9;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #bfbfbf;
  }
}

// 过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s ease;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

// Element Plus 样式覆盖
:deep(.el-menu-item) {
  &.is-active {
    background: linear-gradient(135deg, #409eff 0%, #36a3f7 100%) !important;
    
    &::before {
      display: none;
    }
  }
}

:deep(.el-breadcrumb__item) {
  .el-breadcrumb__inner {
    color: #666;
    font-weight: 400;
    
    &.is-link:hover {
      color: #409eff;
    }
  }
  
  &:last-child .el-breadcrumb__inner {
    color: #409eff;
    font-weight: 500;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: -240px;
    z-index: 2000;
    
    &.collapsed {
      left: 0;
      width: 240px;
    }
  }
  
  .main-container {
    margin-left: 0;
  }
  
  .header-left {
    .breadcrumb {
      display: none;
    }
  }
  
  .user-info .username {
    display: none;
  }
}
</style>