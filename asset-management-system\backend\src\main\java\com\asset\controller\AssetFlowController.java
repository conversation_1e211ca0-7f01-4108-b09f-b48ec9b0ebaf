package com.asset.controller;

import com.asset.dto.ApiResponse;
import com.asset.dto.AssetOperationRequest;
import com.asset.dto.AssetResponse;
import com.asset.service.AssetFlowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/assets")
@Validated
public class AssetFlowController extends BaseController {

    @Autowired
    private AssetFlowService assetFlowService;

    /**
     * 资产入库
     */
    @PostMapping("/{id}/receive")
    public ApiResponse<AssetResponse> receiveAsset(@PathVariable Long id,
                                                  @Valid @RequestBody AssetOperationRequest request) {
        try {
            AssetResponse result = assetFlowService.receiveAsset(id, request);
            return ApiResponse.success("资产入库成功", result);
        } catch (Exception e) {
            log.error("资产入库失败: {}", e.getMessage());
            return ApiResponse.error("资产入库失败: " + e.getMessage());
        }
    }

    /**
     * 资产安装
     */
    @PostMapping("/{id}/install")
    public ApiResponse<AssetResponse> installAsset(@PathVariable Long id,
                                                  @Valid @RequestBody AssetOperationRequest request) {
        try {
            AssetResponse result = assetFlowService.installAsset(id, request);
            return ApiResponse.success("资产安装成功", result);
        } catch (Exception e) {
            log.error("资产安装失败: {}", e.getMessage());
            return ApiResponse.error("资产安装失败: " + e.getMessage());
        }
    }

    /**
     * 资产位置变更
     */
    @PostMapping("/{id}/move")
    public ApiResponse<AssetResponse> moveAsset(@PathVariable Long id,
                                               @Valid @RequestBody AssetOperationRequest request) {
        try {
            AssetResponse result = assetFlowService.moveAsset(id, request);
            return ApiResponse.success("资产位置变更成功", result);
        } catch (Exception e) {
            log.error("资产位置变更失败: {}", e.getMessage());
            return ApiResponse.error("资产位置变更失败: " + e.getMessage());
        }
    }

    /**
     * 资产出库
     */
    @PostMapping("/{id}/outbound")
    public ApiResponse<AssetResponse> outboundAsset(@PathVariable Long id,
                                                   @Valid @RequestBody AssetOperationRequest request) {
        try {
            AssetResponse result = assetFlowService.outboundAsset(id, request);
            return ApiResponse.success("资产出库成功", result);
        } catch (Exception e) {
            log.error("资产出库失败: {}", e.getMessage());
            return ApiResponse.error("资产出库失败: " + e.getMessage());
        }
    }

    /**
     * 重新激活资产
     */
    @PostMapping("/{id}/reactivate")
    public ApiResponse<AssetResponse> reactivateAsset(@PathVariable Long id,
                                                     @Valid @RequestBody AssetOperationRequest request) {
        try {
            AssetResponse result = assetFlowService.reactivateAsset(id, request);
            return ApiResponse.success("资产重新激活成功", result);
        } catch (Exception e) {
            log.error("资产重新激活失败: {}", e.getMessage());
            return ApiResponse.error("资产重新激活失败: " + e.getMessage());
        }
    }

    /**
     * 批量操作
     */
    @PostMapping("/batch/{operation}")
    public ApiResponse<Void> batchOperation(@PathVariable String operation,
                                           @RequestBody BatchOperationRequest batchRequest) {
        try {
            assetFlowService.batchOperation(batchRequest.getAssetIds(), 
                                          operation, batchRequest.getRequest());
            return ApiResponse.success("批量操作完成");
        } catch (Exception e) {
            log.error("批量操作失败: {}", e.getMessage());
            return ApiResponse.error("批量操作失败: " + e.getMessage());
        }
    }

    /**
     * 批量操作请求对象
     */
    public static class BatchOperationRequest {
        private List<Long> assetIds;
        private AssetOperationRequest request;

        public List<Long> getAssetIds() {
            return assetIds;
        }

        public void setAssetIds(List<Long> assetIds) {
            this.assetIds = assetIds;
        }

        public AssetOperationRequest getRequest() {
            return request;
        }

        public void setRequest(AssetOperationRequest request) {
            this.request = request;
        }
    }
}