import { defineStore } from 'pinia'
import { login, logout, getUserInfo } from '@/api/auth'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    token: localStorage.getItem('token') || null,
    user: null,
    isAuthenticated: !!localStorage.getItem('token')
  }),

  getters: {
    isAdmin: (state) => state.user?.role === 'ADMIN',
    userName: (state) => state.user?.username || '',
    userId: (state) => state.user?.id
  },

  actions: {
    async login(credentials) {
      try {
        const response = await login(credentials)
        const { token, user } = response.data
        
        this.token = token
        this.user = user
        this.isAuthenticated = true
        
        localStorage.setItem('token', token)
        
        return { success: true }
      } catch (error) {
        return { 
          success: false, 
          message: error.response?.data?.message || '登录失败' 
        }
      }
    },

    async logout() {
      try {
        // 只有在有有效token的情况下才调用后端logout接口
        if (this.token && this.isAuthenticated) {
          await logout()
        }
      } catch (error) {
        console.error('Logout error:', error)
      } finally {
        this.token = null
        this.user = null
        this.isAuthenticated = false
        localStorage.removeItem('token')
      }
    },

    async checkAuth() {
      if (this.token) {
        try {
          const response = await getUserInfo()
          this.user = response.data
          this.isAuthenticated = true
          return true
        } catch (error) {
          // token无效时直接清除本地状态，不调用后端logout接口
          console.warn('Token验证失败，清除认证状态:', error.message)
          this.token = null
          this.user = null
          this.isAuthenticated = false
          localStorage.removeItem('token')
          return false
        }
      } else {
        this.isAuthenticated = false
        return false
      }
    },

    setUser(user) {
      this.user = user
    }
  }
})