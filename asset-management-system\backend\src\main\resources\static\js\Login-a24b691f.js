import{_ as I}from"./_plugin-vue_export-helper-62491c14.js";/* empty css                *//* empty css                 *//* empty css                    */import{u as E,a as S,r as c,b as U,o as L,c as u,d as _,e as R,f as s,g as a,w as t,E as F,h as y,i as M,j as p,k as A,l as B,m as C,n as N,p as K}from"./index-2733c819.js";const q="/logo.ico";const z={class:"login-container"},j={class:"login-content"},P={class:"login-form-container animate__animated animate__fadeInUp animate__delay-1s"},T={key:0},D={key:1},G={__name:"Login",setup(H){const g=E(),f=S(),i=c(),n=c(!1),d=c(!1),o=U({username:"",password:""}),b={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度在 3 到 20 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"密码长度在 6 到 20 个字符",trigger:"blur"}]},m=async()=>{i.value&&await i.value.validate(async v=>{if(v){n.value=!0;try{const e=await f.login(o);e.success?(p.success("登录成功"),g.push("/")):p.error(e.message)}catch{p.error("登录失败")}finally{n.value=!1}}})};return L(()=>{f.isAuthenticated&&g.push("/"),localStorage.getItem("rememberMe")==="true"&&(d.value=!0,o.username=localStorage.getItem("rememberedUsername")||"",o.password=localStorage.getItem("rememberedPassword")||"")}),(v,e)=>{const h=A,r=B,w=C,V=N,x=K,k=F;return u(),_("div",z,[e[5]||(e[5]=R('<div class="background-animation" data-v-539cea2f><div class="floating-shapes" data-v-539cea2f><div class="shape shape-1" data-v-539cea2f></div><div class="shape shape-2" data-v-539cea2f></div><div class="shape shape-3" data-v-539cea2f></div><div class="shape shape-4" data-v-539cea2f></div><div class="shape shape-5" data-v-539cea2f></div></div></div>',1)),s("div",j,[s("div",P,[a(k,{class:"login-card",shadow:"always"},{header:t(()=>e[3]||(e[3]=[s("div",{class:"card-header"},[s("div",{class:"logo-container"},[s("img",{src:q,alt:"Logo",class:"login-logo"})]),s("h1",{class:"system-title animate__animated animate__slideInRight animate__delay-1s"},"资产管理系统"),s("p",{class:"system-subtitle animate__animated animate__slideInRight animate__delay-1-2s"},"Asset Management System")],-1)])),default:t(()=>[a(x,{ref_key:"loginFormRef",ref:i,model:o,rules:b,size:"large",class:"login-form"},{default:t(()=>[a(r,{prop:"username"},{default:t(()=>[a(h,{modelValue:o.username,"onUpdate:modelValue":e[0]||(e[0]=l=>o.username=l),placeholder:"请输入用户名","prefix-icon":"User",clearable:"",onKeyup:y(m,["enter"])},null,8,["modelValue"])]),_:1}),a(r,{prop:"password"},{default:t(()=>[a(h,{modelValue:o.password,"onUpdate:modelValue":e[1]||(e[1]=l=>o.password=l),type:"password",placeholder:"请输入密码","prefix-icon":"Lock","show-password":"",clearable:"",onKeyup:y(m,["enter"])},null,8,["modelValue"])]),_:1}),a(r,null,{default:t(()=>[a(w,{modelValue:d.value,"onUpdate:modelValue":e[2]||(e[2]=l=>d.value=l)},{default:t(()=>e[4]||(e[4]=[M("记住密码",-1)])),_:1,__:[4]},8,["modelValue"])]),_:1}),a(r,null,{default:t(()=>[a(V,{type:"primary",size:"large",loading:n.value,onClick:m,class:"login-button"},{default:t(()=>[n.value?(u(),_("span",D,"登录中...")):(u(),_("span",T,"登 录"))]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1})])]),e[6]||(e[6]=s("div",{class:"login-footer animate__animated animate__fadeInUp animate__delay-2s"},[s("p",null,"© 2024 资产管理系统. All rights reserved.")],-1))])}}},Y=I(G,[["__scopeId","data-v-539cea2f"]]);export{Y as default};
