<template>
  <div class="login-container">
    <!-- 背景动画 -->
    <div class="background-animation">
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
        <div class="shape shape-5"></div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="login-content">
      <!-- Logo和标题 -->
      <div class="login-header animate__animated animate__fadeInDown">
        <div class="logo-container">
          <el-icon class="logo-icon" size="48">
            <Box />
          </el-icon>
        </div>
        <h1 class="system-title">资产管理系统</h1>
        <p class="system-subtitle">Asset Management System</p>
      </div>

      <!-- 登录表单 -->
      <div class="login-form-container animate__animated animate__fadeInUp animate__delay-1s">
        <el-card class="login-card" shadow="always">
          <template #header>
            <div class="card-header">
              <div class="logo-container">
                <img src="/logo.ico" alt="Logo" class="login-logo" />
              </div>
              <h2>用户登录</h2>
              <span class="welcome-text">欢迎回来！</span>
            </div>
          </template>

          <el-form 
            ref="loginFormRef" 
            :model="loginForm" 
            :rules="loginRules"
            size="large"
            class="login-form"
          >
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                placeholder="请输入用户名"
                prefix-icon="User"
                clearable
                @keyup.enter="handleLogin"
              />
            </el-form-item>

            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="请输入密码"
                prefix-icon="Lock"
                show-password
                clearable
                @keyup.enter="handleLogin"
              />
            </el-form-item>

            <el-form-item>
              <el-checkbox v-model="rememberMe">记住密码</el-checkbox>
            </el-form-item>

            <el-form-item>
              <el-button 
                type="primary" 
                size="large"
                :loading="loading"
                @click="handleLogin"
                class="login-button"
              >
                <span v-if="!loading">登 录</span>
                <span v-else>登录中...</span>
              </el-button>
            </el-form-item>
          </el-form>

          <!-- 演示账号信息 -->
          <div class="demo-accounts">
            <el-divider content-position="center">演示账号</el-divider>
            <div class="account-info">
              <div class="account-row">
                <span class="account-label">管理员：</span>
                <span class="account-value">admin / admin123</span>
              </div>
              <div class="account-row">
                <span class="account-label">普通用户：</span>
                <span class="account-value">user / user123</span>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 页面底部 -->
    <div class="login-footer animate__animated animate__fadeInUp animate__delay-2s">
      <p>&copy; 2024 资产管理系统. All rights reserved.</p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { Box } from '@element-plus/icons-vue'

const router = useRouter()
const authStore = useAuthStore()

const loginFormRef = ref()
const loading = ref(false)
const rememberMe = ref(false)

const loginForm = reactive({
  username: '',
  password: ''
})

const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  await loginFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      
      try {
        const result = await authStore.login(loginForm)
        
        if (result.success) {
          ElMessage.success('登录成功')
          router.push('/')
        } else {
          ElMessage.error(result.message)
        }
      } catch (error) {
        ElMessage.error('登录失败')
      } finally {
        loading.value = false
      }
    }
  })
}

onMounted(() => {
  // 如果已经登录，直接跳转到首页
  if (authStore.isAuthenticated) {
    router.push('/')
  }
  
  // 恢复记住的密码
  if (localStorage.getItem('rememberMe') === 'true') {
    rememberMe.value = true
    loginForm.username = localStorage.getItem('rememberedUsername') || ''
    loginForm.password = localStorage.getItem('rememberedPassword') || ''
  }
})
</script>

<style scoped lang="scss">
.login-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.background-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.floating-shapes {
  position: relative;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  left: 80%;
  animation-delay: 2s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  top: 80%;
  left: 20%;
  animation-delay: 4s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  top: 30%;
  left: 70%;
  animation-delay: 1s;
}

.shape-5 {
  width: 140px;
  height: 140px;
  top: 10%;
  left: 50%;
  animation-delay: 3s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.login-content {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 480px;
  padding: 0 20px;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
  color: white;
}

.logo-container {
  margin-bottom: 20px;
}

.logo-icon {
  background: rgba(255, 255, 255, 0.2);
  padding: 12px;
  border-radius: 50%;
  backdrop-filter: blur(10px);
}

.system-title {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.system-subtitle {
  font-size: 16px;
  opacity: 0.9;
  font-weight: 300;
  letter-spacing: 1px;
}

.login-form-container {
  margin-bottom: 30px;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.card-header {
  text-align: center;
  padding: 8px 0;
}

.card-header h2 {
  margin: 0 0 4px 0;
  color: #333;
  font-size: 22px;
  font-weight: 600;
}

.welcome-text {
  color: #666;
  font-size: 13px;
}

.login-form {
  padding: 16px 0;
}

.login-button {
  width: 100%;
  height: 44px;
  font-size: 15px;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.demo-accounts {
  margin-top: 16px;
}

.account-info {
  background: #f8f9fa;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 12px;
}

.account-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 6px 0;
}

.account-label {
  color: #666;
  font-weight: 500;
}

.account-value {
  color: #333;
  font-family: 'Courier New', monospace;
  font-size: 11px;
}

.login-footer {
  position: relative;
  z-index: 1;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  margin-top: 20px;
}

// 响应式设计
@media (max-width: 768px) {
  .login-container {
    padding: 20px;
  }

  .system-title {
    font-size: 28px;
  }

  .login-content {
    max-width: 100%;
  }

  .login-card {
    margin: 0 -10px;
  }
}

@media (min-width: 1920px) {
  .login-content {
    max-width: 520px;
  }

  .card-header h2 {
    font-size: 24px;
  }

  .login-button {
    height: 48px;
    font-size: 16px;
  }
}

@media (min-width: 2560px) {
  .login-content {
    max-width: 580px;
  }

  .system-title {
    font-size: 36px;
  }

  .card-header h2 {
    font-size: 26px;
  }
}

// Element Plus 样式覆盖
:deep(.el-card__header) {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-card__body) {
  padding: 20px 24px;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-checkbox__label) {
  color: #666;
  font-size: 14px;
}

:deep(.el-divider__text) {
  font-size: 12px;
  color: #999;
}
</style>