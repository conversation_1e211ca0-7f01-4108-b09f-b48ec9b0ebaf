import{_ as re}from"./_plugin-vue_export-helper-62491c14.js";/* empty css               *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                 */import{aT as L,r,b as ie,o as ue,c as i,d as p,f as t,g as l,w as s,aC as ce,F as v,L as z,P as I,i as x,a_ as de,X as O,$ as b,a8 as m,a2 as A,a3 as j,aH as pe,j as u,z as me,a$ as _e,b0 as fe,n as ve,b1 as xe,aG as ye,b2 as ge,aN as be,aQ as he,E as we,aD as Ee,k as ke,l as Ce,aL as Te,aM as De,p as Ve}from"./index-2733c819.js";import{a as Fe}from"./asset-f5b5b286.js";function Ie(E){const k=new FormData;return k.append("file",E),L({url:"/import-export/import",method:"post",data:k,headers:{"Content-Type":"multipart/form-data"},timeout:12e4})}function Le(E){return L({url:"/import-export/export",method:"get",params:E,responseType:"blob"})}function Re(){return L({url:"/import-export/template",method:"get",responseType:"blob"})}const Se={class:"import-export-page"},Ue={class:"card-header"},Be={class:"import-content"},Ne={class:"steps-guide"},Pe={class:"import-actions"},$e={key:0,class:"file-info"},Me={key:1,class:"import-progress"},ze={key:2,class:"import-result"},Oe={class:"result-summary"},Ae={key:0,class:"error-details"},je={key:0,class:"error-list"},He={class:"card-header"},Ge={class:"export-content"},We={class:"export-filters"},qe={class:"export-actions"},Ke={class:"export-history"},Qe={class:"history-list"},Xe={class:"history-info"},Je={class:"history-name"},Ye={class:"history-time"},Ze={__name:"index",setup(E){const k=r(),C=r(0),_=r(null),h=r(!1),w=r(0),n=r(null),T=r(!1),V=r(!1),D=r(!1),R=r([]),d=ie({keyword:"",status:"",productType:""}),S=r([{id:1,name:"全部资产数据_20240115.xlsx",time:"2024-01-15 14:30:00"},{id:2,name:"笔记本电脑类型_20240114.xlsx",time:"2024-01-14 09:15:00"}]),H=async()=>{V.value=!0;try{const o=await Re();U(o,"资产导入模板.xlsx"),C.value=1,u.success("模板下载成功")}catch{u.error("模板下载失败")}finally{V.value=!1}},G=o=>o.type==="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"||o.type==="application/vnd.ms-excel"||o.name.endsWith(".xlsx")||o.name.endsWith(".xls")?o.size/1024/1024<50?(_.value=o,C.value=2,n.value=null,!1):(u.error("上传文件大小不能超过50MB!"),!1):(u.error("只能上传Excel文件!"),!1),W=async()=>{if(!_.value){u.error("请先选择文件");return}h.value=!0,w.value=0;const o=setInterval(()=>{w.value<90&&(w.value+=Math.random()*20)},500);try{const{data:e}=await Ie(_.value);clearInterval(o),w.value=100,setTimeout(()=>{n.value=e,C.value=3,h.value=!1,e.errorCount===0?u.success(`导入成功！共导入 ${e.successCount} 条数据`):u.warning(`导入完成！成功 ${e.successCount} 条，失败 ${e.errorCount} 条`)},1e3)}catch(e){clearInterval(o),h.value=!1,u.error("导入失败: "+(e.message||"未知错误"))}},q=async()=>{D.value=!0;try{const o=await Le(d),e=`资产数据_${new Date().toISOString().slice(0,19).replace(/[:-]/g,"")}.xlsx`;U(o,e),S.value.unshift({id:Date.now(),name:e,time:new Date().toLocaleString()}),u.success("导出成功")}catch{u.error("导出失败")}finally{D.value=!1}},K=()=>{Object.assign(d,{keyword:"",status:"",productType:""})},Q=o=>{u.info("重新下载功能开发中...")},X=o=>o<1024?o+" B":o<1024*1024?(o/1024).toFixed(1)+" KB":(o/1024/1024).toFixed(1)+" MB",U=(o,e)=>{const y=window.URL.createObjectURL(o),c=document.createElement("a");c.href=y,c.download=e,document.body.appendChild(c),c.click(),document.body.removeChild(c),window.URL.revokeObjectURL(y)},J=()=>n.value?n.value.errorCount===0?"导入成功！":n.value.successCount===0?"导入失败！":"导入部分成功！":"",Y=()=>n.value?n.value.errorCount===0?"success":n.value.successCount===0?"error":"warning":"info",Z=async()=>{try{const{data:o}=await Fe();R.value=o}catch(o){console.error("加载产品类型失败:",o)}};return ue(()=>{Z()}),(o,e)=>{const y=me,c=_e,ee=fe,f=ve,te=xe,B=ye,le=ge,N=be,oe=he,P=we,$=Ee,se=ke,F=Ce,g=Te,M=De,ae=Ve,ne=ce;return i(),p("div",Se,[e[16]||(e[16]=t("div",{class:"page-header"},[t("div",{class:"header-title"},[t("h2",null,"导入导出"),t("p",null,"批量导入资产信息或导出现有数据")])],-1)),l(ne,{gutter:24},{default:s(()=>[l($,{lg:12,md:24},{default:s(()=>[l(P,{class:"import-card",shadow:"hover"},{header:s(()=>[t("div",Ue,[l(y,{size:"24",color:"#409eff"},{default:s(()=>[l(v(z))]),_:1}),e[4]||(e[4]=t("span",null,"批量导入",-1))])]),default:s(()=>[t("div",Be,[e[9]||(e[9]=t("div",{class:"import-description"},[t("p",null,"支持Excel格式文件(.xlsx, .xls)批量导入资产信息"),t("ul",{class:"feature-list"},[t("li",null,"支持数万条数据批量导入"),t("li",null,"自动验证数据格式和完整性"),t("li",null,"重复数据检测和提示"),t("li",null,"详细的导入结果报告")])],-1)),t("div",Ne,[l(ee,{active:C.value,direction:"vertical",size:"small"},{default:s(()=>[l(c,{title:"下载模板",description:"下载标准导入模板"}),l(c,{title:"填写数据",description:"按模板格式填写资产信息"}),l(c,{title:"上传文件",description:"选择并上传Excel文件"}),l(c,{title:"查看结果",description:"确认导入结果"})]),_:1},8,["active"])]),t("div",Pe,[l(f,{type:"primary",icon:v(I),onClick:H,loading:V.value,class:"action-btn"},{default:s(()=>e[5]||(e[5]=[x(" 下载模板 ",-1)])),_:1,__:[5]},8,["icon","loading"]),l(te,{ref_key:"uploadRef",ref:k,"before-upload":G,"show-file-list":!1,"auto-upload":!1,accept:".xlsx,.xls",class:"upload-component"},{default:s(()=>[l(f,{type:"success",icon:v(de),class:"action-btn"},{default:s(()=>e[6]||(e[6]=[x(" 选择文件 ",-1)])),_:1,__:[6]},8,["icon"])]),_:1},512),_.value?(i(),O(f,{key:0,type:"warning",icon:v(z),onClick:W,loading:h.value,class:"action-btn"},{default:s(()=>e[7]||(e[7]=[x(" 开始导入 ",-1)])),_:1,__:[7]},8,["icon","loading"])):b("",!0)]),_.value?(i(),p("div",$e,[l(B,{title:`已选择文件: ${_.value.name}`,type:"info","show-icon":"",closable:!1},{default:s(()=>[t("p",null,"文件大小: "+m(X(_.value.size)),1),t("p",null,"文件类型: "+m(_.value.type||"未知"),1)]),_:1},8,["title"])])):b("",!0),h.value?(i(),p("div",Me,[l(le,{percentage:w.value,"stroke-width":8,status:"success"},null,8,["percentage"]),e[8]||(e[8]=t("p",{class:"progress-text"},"正在导入数据，请稍候...",-1))])):b("",!0),n.value?(i(),p("div",ze,[l(B,{title:J(),type:Y(),"show-icon":"",closable:!1},{default:s(()=>[t("div",Oe,[t("p",null,"总数据量: "+m(n.value.totalCount),1),t("p",null,"成功导入: "+m(n.value.successCount),1),t("p",null,"失败数量: "+m(n.value.errorCount),1)]),n.value.errorCount>0?(i(),p("div",Ae,[l(f,{link:"",onClick:e[0]||(e[0]=a=>T.value=!T.value),class:"error-toggle"},{default:s(()=>[x(m(T.value?"隐藏":"查看")+"错误详情 ",1)]),_:1}),T.value?(i(),p("div",je,[l(oe,{data:n.value.errorList,size:"small","max-height":"300"},{default:s(()=>[l(N,{prop:"row",label:"行号",width:"80"}),l(N,{prop:"error",label:"错误信息"})]),_:1},8,["data"])])):b("",!0)])):b("",!0)]),_:1},8,["title","type"])])):b("",!0)])]),_:1})]),_:1}),l($,{lg:12,md:24},{default:s(()=>[l(P,{class:"export-card",shadow:"hover"},{header:s(()=>[t("div",He,[l(y,{size:"24",color:"#67c23a"},{default:s(()=>[l(v(I))]),_:1}),e[10]||(e[10]=t("span",null,"数据导出",-1))])]),default:s(()=>[t("div",Ge,[e[15]||(e[15]=t("div",{class:"export-description"},[t("p",null,"将现有资产数据导出为Excel文件"),t("ul",{class:"feature-list"},[t("li",null,"支持筛选条件导出"),t("li",null,"包含完整的资产信息"),t("li",null,"标准Excel格式"),t("li",null,"支持大数据量导出")])],-1)),t("div",We,[e[11]||(e[11]=t("h4",null,"导出筛选",-1)),l(ae,{model:d,size:"default",class:"filter-form"},{default:s(()=>[l(F,{label:"关键词"},{default:s(()=>[l(se,{modelValue:d.keyword,"onUpdate:modelValue":e[1]||(e[1]=a=>d.keyword=a),placeholder:"资产编号、型号、品牌...",clearable:""},null,8,["modelValue"])]),_:1}),l(F,{label:"状态"},{default:s(()=>[l(M,{modelValue:d.status,"onUpdate:modelValue":e[2]||(e[2]=a=>d.status=a),placeholder:"选择状态",clearable:""},{default:s(()=>[l(g,{label:"待处理",value:"PENDING"}),l(g,{label:"已入库",value:"RECEIVED"}),l(g,{label:"已安装",value:"INSTALLED"}),l(g,{label:"已出库",value:"OUTBOUND"}),l(g,{label:"已报废",value:"SCRAPPED"})]),_:1},8,["modelValue"])]),_:1}),l(F,{label:"产品类型"},{default:s(()=>[l(M,{modelValue:d.productType,"onUpdate:modelValue":e[3]||(e[3]=a=>d.productType=a),placeholder:"选择类型",clearable:""},{default:s(()=>[(i(!0),p(A,null,j(R.value,a=>(i(),O(g,{key:a,label:a,value:a},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),t("div",qe,[l(f,{type:"success",icon:v(I),onClick:q,loading:D.value,size:"large",class:"export-btn"},{default:s(()=>[x(m(D.value?"导出中...":"导出Excel"),1)]),_:1},8,["icon","loading"]),l(f,{onClick:K,icon:v(pe)},{default:s(()=>e[12]||(e[12]=[x(" 重置筛选 ",-1)])),_:1,__:[12]},8,["icon"])]),t("div",Ke,[e[14]||(e[14]=t("h4",null,"最近导出",-1)),t("div",Qe,[(i(!0),p(A,null,j(S.value,a=>(i(),p("div",{key:a.id,class:"history-item"},[t("div",Xe,[t("p",Je,m(a.name),1),t("span",Ye,m(a.time),1)]),l(f,{link:"",size:"small",onClick:et=>Q(a)},{default:s(()=>e[13]||(e[13]=[x(" 重新下载 ",-1)])),_:2,__:[13]},1032,["onClick"])]))),128))])])])]),_:1})]),_:1})]),_:1})])}}},_t=re(Ze,[["__scopeId","data-v-d066d32a"]]);export{_t as default};
