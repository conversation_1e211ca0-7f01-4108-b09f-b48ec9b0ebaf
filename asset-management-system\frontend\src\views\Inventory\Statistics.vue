<template>
  <div class="inventory-statistics">
    <!-- 概览卡片 -->
    <el-row :gutter="20" class="summary-cards">
      <el-col :span="6">
        <el-card class="summary-card">
          <div class="summary-content">
            <div class="summary-icon total">
              <el-icon><Box /></el-icon>
            </div>
            <div class="summary-info">
              <div class="summary-title">总资产数</div>
              <div class="summary-value">{{ summary.totalAssets || 0 }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="summary-card">
          <div class="summary-content">
            <div class="summary-icon locations">
              <el-icon><LocationFilled /></el-icon>
            </div>
            <div class="summary-info">
              <div class="summary-title">位置数量</div>
              <div class="summary-value">{{ summary.locationCount || 0 }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="summary-card">
          <div class="summary-content">
            <div class="summary-icon types">
              <el-icon><Collection /></el-icon>
            </div>
            <div class="summary-info">
              <div class="summary-title">类型数量</div>
              <div class="summary-value">{{ summary.typeCount || 0 }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="summary-card">
          <div class="summary-content">
            <div class="summary-icon value">
              <el-icon><Money /></el-icon>
            </div>
            <div class="summary-info">
              <div class="summary-title">总价值</div>
              <div class="summary-value">¥{{ formatMoney(valueStatistics.totalValue) }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <!-- 状态分布图 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>资产状态分布</span>
          </template>
          <div ref="statusChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>

      <!-- 位置分布图 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>位置分布统计</span>
          </template>
          <div ref="locationChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 类型分布表格 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>产品类型统计</span>
          </template>
          <el-table :data="typeStatistics" stripe max-height="400">
            <el-table-column prop="type" label="产品类型" />
            <el-table-column prop="totalCount" label="总数量" width="80" />
            <el-table-column prop="installedCount" label="已安装" width="80" />
            <el-table-column prop="receivedCount" label="已入库" width="80" />
            <el-table-column prop="totalValue" label="总价值" width="120">
              <template #default="{ row }">
                ¥{{ formatMoney(row.totalValue) }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>

      <!-- 位置详细统计 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>位置详细统计</span>
          </template>
          <el-table :data="locationStatistics" stripe max-height="400">
            <el-table-column prop="location" label="位置" />
            <el-table-column prop="totalCount" label="总数量" width="80" />
            <el-table-column prop="installedCount" label="已安装" width="80" />
            <el-table-column prop="receivedCount" label="已入库" width="80" />
            <el-table-column prop="totalValue" label="总价值" width="120">
              <template #default="{ row }">
                ¥{{ formatMoney(row.totalValue) }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 即将过保资产 -->
    <el-row style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>即将过保资产 (30天内)</span>
              <el-tag type="warning" v-if="warrantyExpiry.length > 0">
                {{ warrantyExpiry.length }} 项
              </el-tag>
            </div>
          </template>
          <el-table :data="warrantyExpiry" stripe v-if="warrantyExpiry.length > 0">
            <el-table-column prop="assetCode" label="资产编号" width="120" />
            <el-table-column prop="productModel" label="产品型号" width="150" />
            <el-table-column prop="brand" label="品牌" width="100" />
            <el-table-column prop="purchaseDate" label="采购日期" width="120" />
            <el-table-column prop="warrantyPeriod" label="保修期(月)" width="100" />
            <el-table-column label="过保日期" width="120">
              <template #default="{ row }">
                {{ calculateWarrantyExpiry(row.purchaseDate, row.warrantyPeriod) }}
              </template>
            </el-table-column>
            <el-table-column prop="currentLocation" label="当前位置" width="120" />
            <el-table-column label="剩余天数" width="100">
              <template #default="{ row }">
                <el-tag :type="getRemainingDaysType(row)">
                  {{ getRemainingDays(row.purchaseDate, row.warrantyPeriod) }} 天
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="viewAssetDetail(row.id)">
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-empty v-else description="暂无即将过保的资产" />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { init } from 'echarts'
import { getCompleteInventoryReport, getInventorySummary } from '@/api/inventory'
import { getAssetsNearWarrantyExpiry } from '@/api/asset'
import dayjs from 'dayjs'

const router = useRouter()
const statusChartRef = ref()
const locationChartRef = ref()
let statusChart = null
let locationChart = null

const summary = reactive({
  totalAssets: 0,
  locationCount: 0,
  typeCount: 0
})

const valueStatistics = reactive({
  totalValue: 0,
  avgValue: 0,
  minValue: 0,
  maxValue: 0
})

const statusDistribution = ref([])
const locationStatistics = ref([])
const typeStatistics = ref([])
const warrantyExpiry = ref([])

const statusLabels = {
  PENDING: '待处理',
  RECEIVED: '已入库',
  INSTALLED: '已安装',
  OUTBOUND: '已出库',
  SCRAPPED: '已报废'
}

const formatMoney = (value) => {
  if (!value) return '0.00'
  return Number(value).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

const calculateWarrantyExpiry = (purchaseDate, warrantyPeriod) => {
  if (!purchaseDate || !warrantyPeriod) return '-'
  return dayjs(purchaseDate).add(warrantyPeriod, 'month').format('YYYY-MM-DD')
}

const getRemainingDays = (purchaseDate, warrantyPeriod) => {
  if (!purchaseDate || !warrantyPeriod) return 0
  const expiryDate = dayjs(purchaseDate).add(warrantyPeriod, 'month')
  return Math.max(0, expiryDate.diff(dayjs(), 'day'))
}

const getRemainingDaysType = (row) => {
  const days = getRemainingDays(row.purchaseDate, row.warrantyPeriod)
  if (days <= 7) return 'danger'
  if (days <= 15) return 'warning'
  return 'success'
}

const initStatusChart = () => {
  if (!statusChartRef.value) return
  
  statusChart = init(statusChartRef.value)
  
  const data = statusDistribution.value.map(item => ({
    name: statusLabels[item.status] || item.status,
    value: item.count
  }))
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '资产状态',
        type: 'pie',
        radius: '50%',
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  statusChart.setOption(option)
}

const initLocationChart = () => {
  if (!locationChartRef.value) return
  
  locationChart = init(locationChartRef.value)
  
  const data = locationStatistics.value.slice(0, 10).map(item => ({
    name: item.location || '未知位置',
    value: item.totalCount
  }))
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: data.map(item => item.name)
    },
    series: [
      {
        name: '资产数量',
        type: 'bar',
        data: data.map(item => item.value),
        itemStyle: {
          color: '#409EFF'
        }
      }
    ]
  }
  
  locationChart.setOption(option)
}

const loadData = async () => {
  try {
    // 加载库存摘要
    const summaryResponse = await getInventorySummary()
    Object.assign(summary, summaryResponse.data)
    
    if (summaryResponse.data.statusDistribution) {
      statusDistribution.value = summaryResponse.data.statusDistribution
    }
    
    if (summaryResponse.data.valueStatistics) {
      Object.assign(valueStatistics, summaryResponse.data.valueStatistics)
    }
    
    // 加载完整报告
    const reportResponse = await getCompleteInventoryReport()
    const reportData = reportResponse.data
    
    if (reportData.locationInventory) {
      locationStatistics.value = reportData.locationInventory
    }
    
    if (reportData.typeInventory) {
      typeStatistics.value = reportData.typeInventory
    }
    
    // 加载即将过保资产
    const warrantyResponse = await getAssetsNearWarrantyExpiry()
    warrantyExpiry.value = warrantyResponse.data
    
    // 初始化图表
    await nextTick()
    initStatusChart()
    initLocationChart()
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  }
}

const viewAssetDetail = (assetId) => {
  router.push(`/assets/${assetId}`)
}

onMounted(() => {
  loadData()
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    statusChart?.resize()
    locationChart?.resize()
  })
})
</script>

<style scoped>
.inventory-statistics {
  padding: 20px;
}

.summary-cards {
  margin-bottom: 20px;
}

.summary-card {
  height: 120px;
}

.summary-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.summary-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  margin-right: 20px;
}

.summary-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.summary-icon.locations {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.summary-icon.types {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.summary-icon.value {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.summary-info {
  flex: 1;
}

.summary-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 5px;
}

.summary-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>