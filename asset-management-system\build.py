#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资产管理系统 - 自动化编译和启动脚本
Asset Management System - Automated Build and Start Script

功能特性:
- 自动检测环境依赖
- 清理和编译后端项目
- 启动后端Spring Boot服务
- 启动前端Vue.js开发服务器
- 健康检查和状态监控
- 优雅的进程管理

作者: 全栈设计大师
版本: 1.0.0
"""

import os
import sys
import subprocess
import time
import signal
import threading
import requests
from pathlib import Path
from datetime import datetime
import json

class Colors:
    """终端颜色常量"""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

class AssetManagementBuilder:
    """资产管理系统构建器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.backend_dir = self.project_root / "backend"
        self.frontend_dir = self.project_root / "frontend"
        self.backend_process = None
        self.frontend_process = None
        self.backend_port = 8080
        self.frontend_port = 3000

        # 自定义工具路径（根据您的安装路径）
        self.custom_paths = {
            "maven": r"D:\cursor\apache-maven-3.9.9\bin\mvn.cmd",
            "npm": r"D:\cursor\nodejs\npm.cmd",
            "node": r"D:\cursor\nodejs\node.exe"
        }

        # 初始化命令（默认使用系统PATH中的命令）
        self.maven_cmd = "mvn"
        self.npm_cmd = "npm"
        self.node_cmd = "node"
        
    def print_banner(self):
        """打印启动横幅"""
        banner = f"""
{Colors.HEADER}{'='*80}{Colors.ENDC}
{Colors.BOLD}{Colors.OKCYAN}🏢 资产管理系统 - 自动化构建工具{Colors.ENDC}
{Colors.OKBLUE}Asset Management System - Automated Build Tool{Colors.ENDC}

{Colors.OKGREEN}📁 项目路径: {self.project_root}{Colors.ENDC}
{Colors.OKGREEN}🔧 后端路径: {self.backend_dir}{Colors.ENDC}
{Colors.OKGREEN}🎨 前端路径: {self.frontend_dir}{Colors.ENDC}
{Colors.OKGREEN}⏰ 构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}{Colors.ENDC}
{Colors.HEADER}{'='*80}{Colors.ENDC}
        """
        print(banner)
    
    def log(self, message, level="INFO"):
        """日志输出"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        colors = {
            "INFO": Colors.OKBLUE,
            "SUCCESS": Colors.OKGREEN,
            "WARNING": Colors.WARNING,
            "ERROR": Colors.FAIL
        }
        color = colors.get(level, Colors.OKBLUE)
        print(f"{color}[{timestamp}] [{level}] {message}{Colors.ENDC}")
    
    def check_dependencies(self):
        """检查环境依赖"""
        self.log("🔍 检查环境依赖...")

        dependencies = {
            "java": ["java", "-version"],
            "maven": ["mvn", "-version"],
            "node": ["node", "--version"],
            "npm": ["npm", "--version"]
        }

        missing_deps = []

        for name, cmd in dependencies.items():
            success = False

            # 首先尝试系统PATH中的命令
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    version = result.stderr.split('\n')[0] if name == "java" else result.stdout.split('\n')[0]
                    self.log(f"✅ {name.upper()}: {version.strip()}", "SUCCESS")
                    success = True
            except (subprocess.TimeoutExpired, FileNotFoundError):
                pass

            # 如果系统PATH中找不到，尝试自定义路径
            if not success and name in self.custom_paths:
                try:
                    custom_cmd = [self.custom_paths[name]]
                    if name == "maven":
                        custom_cmd.append("-version")
                    elif name == "npm":
                        custom_cmd.append("--version")
                    elif name == "node":
                        custom_cmd.append("--version")

                    result = subprocess.run(custom_cmd, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        version = result.stderr.split('\n')[0] if name == "java" else result.stdout.split('\n')[0]
                        self.log(f"✅ {name.upper()}: {version.strip()} (自定义路径)", "SUCCESS")
                        success = True
                        # 更新命令为自定义路径
                        if name == "maven":
                            self.maven_cmd = self.custom_paths[name]
                        elif name == "npm":
                            self.npm_cmd = self.custom_paths[name]
                        elif name == "node":
                            self.node_cmd = self.custom_paths[name]
                except (subprocess.TimeoutExpired, FileNotFoundError):
                    pass

            if not success:
                missing_deps.append(name)

        if missing_deps:
            self.log(f"❌ 缺少依赖: {', '.join(missing_deps)}", "ERROR")
            self.log("请检查以下路径是否正确:", "ERROR")
            for dep in missing_deps:
                if dep in self.custom_paths:
                    self.log(f"  {dep}: {self.custom_paths[dep]}", "ERROR")
            return False

        self.log("✅ 所有依赖检查通过", "SUCCESS")
        return True

    def is_port_in_use(self, port):
        """检查端口是否被占用"""
        import socket
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            try:
                s.bind(('localhost', port))
                return False
            except socket.error:
                return True

    def kill_process_on_port(self, port):
        """终止占用指定端口的进程"""
        try:
            if os.name == 'nt':  # Windows
                result = subprocess.run(
                    ['netstat', '-ano'],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                for line in result.stdout.split('\n'):
                    if f':{port}' in line and 'LISTENING' in line:
                        parts = line.split()
                        if len(parts) >= 5:
                            pid = parts[-1]
                            subprocess.run(['taskkill', '/F', '/PID', pid],
                                         capture_output=True, timeout=5)
                            self.log(f"✅ 已终止占用端口 {port} 的进程 (PID: {pid})", "SUCCESS")
                            break
            else:  # Linux/Mac
                result = subprocess.run(
                    ['lsof', '-ti', f':{port}'],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                if result.stdout.strip():
                    pid = result.stdout.strip()
                    subprocess.run(['kill', '-9', pid], timeout=5)
                    self.log(f"✅ 已终止占用端口 {port} 的进程 (PID: {pid})", "SUCCESS")
        except Exception as e:
            self.log(f"⚠️ 无法终止占用端口的进程: {str(e)}", "WARNING")
    
    def clean_backend(self):
        """清理后端项目"""
        self.log("🧹 清理后端项目...")
        try:
            result = subprocess.run(
                [self.maven_cmd, "clean"],
                cwd=self.backend_dir,
                capture_output=True,
                text=True,
                timeout=60
            )
            if result.returncode == 0:
                self.log("✅ 后端项目清理完成", "SUCCESS")
                return True
            else:
                self.log(f"❌ 后端清理失败: {result.stderr}", "ERROR")
                return False
        except subprocess.TimeoutExpired:
            self.log("❌ 后端清理超时", "ERROR")
            return False
        except Exception as e:
            self.log(f"❌ 后端清理异常: {str(e)}", "ERROR")
            return False
    
    def compile_backend(self):
        """编译后端项目"""
        self.log("🔨 编译后端项目...")
        try:
            result = subprocess.run(
                [self.maven_cmd, "compile"],
                cwd=self.backend_dir,
                capture_output=True,
                text=True,
                timeout=180
            )
            if result.returncode == 0:
                self.log("✅ 后端编译成功", "SUCCESS")
                return True
            else:
                self.log(f"❌ 后端编译失败:", "ERROR")
                # 输出编译错误的关键信息
                error_lines = result.stdout.split('\n')
                for line in error_lines:
                    if "ERROR" in line or "COMPILATION ERROR" in line:
                        self.log(f"   {line}", "ERROR")
                return False
        except subprocess.TimeoutExpired:
            self.log("❌ 后端编译超时", "ERROR")
            return False
        except Exception as e:
            self.log(f"❌ 后端编译异常: {str(e)}", "ERROR")
            return False
    
    def start_backend(self):
        """启动后端服务 - 优化版本"""
        self.log("🚀 启动后端服务...")

        # 检查端口是否被占用
        if self.is_port_in_use(self.backend_port):
            self.log(f"⚠️ 端口 {self.backend_port} 已被占用，尝试终止占用进程...", "WARNING")
            self.kill_process_on_port(self.backend_port)
            time.sleep(2)

        try:
            # 设置环境变量以大幅优化启动速度
            env = os.environ.copy()

            # Maven优化参数
            maven_opts = [
                '-Xmx2048m',  # 增加堆内存
                '-Xms512m',   # 设置初始堆大小
                '-XX:+UseG1GC',  # 使用G1垃圾收集器
                '-XX:+UseStringDeduplication',  # 字符串去重
                '-XX:+UseCompressedOops',  # 压缩对象指针
                '-XX:MaxGCPauseMillis=200',  # 最大GC暂停时间
                '-Djava.awt.headless=true',  # 无头模式
                '-Dfile.encoding=UTF-8',  # 文件编码
                '-Duser.timezone=Asia/Shanghai'  # 时区设置
            ]
            env['MAVEN_OPTS'] = ' '.join(maven_opts)

            # Spring Boot优化参数
            spring_opts = [
                '-Dspring.output.ansi.enabled=NEVER',  # 禁用ANSI颜色
                '-Dspring.jpa.show-sql=false',  # 禁用SQL日志（加快启动）
                '-Dspring.jpa.hibernate.ddl-auto=none',  # 禁用DDL自动执行
                '-Dlogging.level.org.springframework=WARN',  # 减少Spring日志
                '-Dlogging.level.org.hibernate=WARN',  # 减少Hibernate日志
                '-Dlogging.level.com.zaxxer.hikari=WARN',  # 减少连接池日志
                '-Dspring.devtools.restart.enabled=false',  # 禁用热重启
                '-Dspring.devtools.livereload.enabled=false'  # 禁用LiveReload
            ]

            # Maven命令参数
            maven_args = [
                self.maven_cmd,
                "spring-boot:run",
                "-Dspring-boot.run.fork=false",  # 不fork新进程
                "-Dspring-boot.run.jvmArguments=" + " ".join(spring_opts),
                "-q"  # 安静模式，减少输出
            ]

            self.backend_process = subprocess.Popen(
                maven_args,
                cwd=self.backend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                env=env,
                bufsize=1,
                universal_newlines=True
            )
            self.log(f"✅ 后端服务启动中 (PID: {self.backend_process.pid})", "SUCCESS")
            self.log("💡 提示: 首次启动可能需要下载依赖，请耐心等待...", "INFO")
            return True
        except Exception as e:
            self.log(f"❌ 后端启动失败: {str(e)}", "ERROR")
            return False
    
    def start_frontend(self):
        """启动前端服务 - 优化版本"""
        self.log("🎨 启动前端服务...")

        # 检查端口是否被占用
        if self.is_port_in_use(self.frontend_port):
            self.log(f"⚠️ 端口 {self.frontend_port} 已被占用，尝试终止占用进程...", "WARNING")
            self.kill_process_on_port(self.frontend_port)
            time.sleep(2)

        try:
            # 设置环境变量
            env = os.environ.copy()
            env['NODE_ENV'] = 'development'
            env['FORCE_COLOR'] = '0'  # 禁用颜色输出

            self.frontend_process = subprocess.Popen(
                [self.npm_cmd, "run", "dev"],
                cwd=self.frontend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                env=env,
                bufsize=1,
                universal_newlines=True
            )
            self.log(f"✅ 前端服务启动中 (PID: {self.frontend_process.pid})", "SUCCESS")
            return True
        except Exception as e:
            self.log(f"❌ 前端启动失败: {str(e)}", "ERROR")
            return False
    
    def wait_for_backend(self, timeout=180):
        """等待后端服务就绪 - 优化版本"""
        self.log("⏳ 等待后端服务就绪...")
        start_time = time.time()

        # 多个检查端点，按优先级排序
        check_endpoints = [
            "/api/auth/profile",  # 最轻量的端点
            "/api/inventory/summary",  # 备用端点
            "/api/users"  # 用户端点
        ]

        last_error = None
        check_interval = 3  # 初始检查间隔
        max_interval = 8    # 最大检查间隔

        while time.time() - start_time < timeout:
            # 检查进程是否还在运行
            if self.backend_process and self.backend_process.poll() is not None:
                self.log("❌ 后端进程意外退出", "ERROR")
                if self.backend_process.stderr:
                    try:
                        error_output = self.backend_process.stderr.read()
                        if error_output:
                            self.log(f"错误信息: {error_output[:200]}...", "ERROR")
                    except:
                        pass
                return False

            # 尝试多个端点
            for endpoint in check_endpoints:
                try:
                    response = requests.get(
                        f"http://localhost:{self.backend_port}{endpoint}",
                        timeout=3,
                        headers={'Accept': 'application/json'}
                    )
                    # 200成功、401未授权、403禁止访问都表示服务已启动
                    if response.status_code in [200, 401, 403]:
                        elapsed = time.time() - start_time
                        self.log(f"✅ 后端服务就绪 (耗时: {elapsed:.1f}秒)", "SUCCESS")
                        return True
                except requests.exceptions.ConnectionError:
                    last_error = "连接被拒绝，服务可能还在启动中"
                except requests.exceptions.Timeout:
                    last_error = "请求超时"
                except requests.exceptions.RequestException as e:
                    last_error = str(e)

            # 显示进度
            elapsed = time.time() - start_time
            remaining = timeout - elapsed
            if elapsed > 30 and elapsed % 15 < check_interval:  # 每15秒显示一次进度
                self.log(f"⏳ 仍在等待... (已等待: {elapsed:.0f}秒, 剩余: {remaining:.0f}秒)", "INFO")
                if last_error:
                    self.log(f"   最后错误: {last_error}", "WARNING")

            # 动态调整检查间隔
            if elapsed > 60:
                check_interval = min(max_interval, check_interval + 1)

            time.sleep(check_interval)

        self.log(f"❌ 后端服务启动超时 (等待了{timeout}秒)", "ERROR")
        if last_error:
            self.log(f"最后错误: {last_error}", "ERROR")
        self.log("💡 建议: 检查数据库连接、端口占用或查看后端日志", "WARNING")
        return False
    
    def wait_for_frontend(self, timeout=90):
        """等待前端服务就绪 - 优化版本"""
        self.log("⏳ 等待前端服务就绪...")
        start_time = time.time()

        while time.time() - start_time < timeout:
            # 检查进程是否还在运行
            if self.frontend_process and self.frontend_process.poll() is not None:
                self.log("❌ 前端进程意外退出", "ERROR")
                return False

            try:
                response = requests.get(
                    f"http://localhost:{self.frontend_port}",
                    timeout=3,
                    headers={'User-Agent': 'Build-Script/1.0'}
                )
                if response.status_code == 200:
                    elapsed = time.time() - start_time
                    self.log(f"✅ 前端服务就绪 (耗时: {elapsed:.1f}秒)", "SUCCESS")
                    return True
            except requests.exceptions.ConnectionError:
                pass  # 连接被拒绝是正常的，继续等待
            except requests.exceptions.RequestException:
                pass

            # 显示进度（前端通常启动较快）
            elapsed = time.time() - start_time
            if elapsed > 20 and elapsed % 10 < 3:
                remaining = timeout - elapsed
                self.log(f"⏳ 前端仍在启动... (已等待: {elapsed:.0f}秒)", "INFO")

            time.sleep(3)

        self.log(f"❌ 前端服务启动超时 (等待了{timeout}秒)", "ERROR")
        self.log("💡 建议: 检查Node.js版本、NPM依赖或查看前端日志", "WARNING")
        return False
    
    def print_success_info(self):
        """打印成功信息"""
        success_info = f"""
{Colors.OKGREEN}{'='*80}{Colors.ENDC}
{Colors.BOLD}{Colors.OKGREEN}🎉 资产管理系统启动成功！{Colors.ENDC}

{Colors.OKCYAN}📱 访问地址:{Colors.ENDC}
  🏠 系统首页: {Colors.UNDERLINE}http://localhost:{self.frontend_port}{Colors.ENDC}
  📊 库存统计: {Colors.UNDERLINE}http://localhost:{self.frontend_port}/statistics{Colors.ENDC}
  👥 用户管理: {Colors.UNDERLINE}http://localhost:{self.frontend_port}/users{Colors.ENDC}
  📦 资产管理: {Colors.UNDERLINE}http://localhost:{self.frontend_port}/assets{Colors.ENDC}

{Colors.OKCYAN}🔧 服务状态:{Colors.ENDC}
  🔙 后端服务: {Colors.OKGREEN}运行中{Colors.ENDC} (端口: {self.backend_port})
  🎨 前端服务: {Colors.OKGREEN}运行中{Colors.ENDC} (端口: {self.frontend_port})

{Colors.WARNING}💡 使用提示:{Colors.ENDC}
  - 按 Ctrl+C 停止所有服务
  - 后端API地址: http://localhost:{self.backend_port}/api
  - 前端开发服务器支持热重载

{Colors.OKGREEN}{'='*80}{Colors.ENDC}
        """
        print(success_info)

    def stop_services(self):
        """停止所有服务"""
        self.log("🛑 正在停止服务...")

        if self.backend_process:
            try:
                self.backend_process.terminate()
                self.backend_process.wait(timeout=10)
                self.log("✅ 后端服务已停止", "SUCCESS")
            except subprocess.TimeoutExpired:
                self.backend_process.kill()
                self.log("⚠️ 强制终止后端服务", "WARNING")
            except Exception as e:
                self.log(f"❌ 停止后端服务失败: {str(e)}", "ERROR")

        if self.frontend_process:
            try:
                self.frontend_process.terminate()
                self.frontend_process.wait(timeout=10)
                self.log("✅ 前端服务已停止", "SUCCESS")
            except subprocess.TimeoutExpired:
                self.frontend_process.kill()
                self.log("⚠️ 强制终止前端服务", "WARNING")
            except Exception as e:
                self.log(f"❌ 停止前端服务失败: {str(e)}", "ERROR")

    def signal_handler(self, signum, frame):
        """信号处理器"""
        self.log("🔄 接收到停止信号，正在优雅关闭...", "WARNING")
        self.stop_services()
        sys.exit(0)

    def build_and_start(self, fast_mode=False):
        """构建并启动系统"""
        try:
            # 打印横幅
            self.print_banner()

            if fast_mode:
                self.log("🚀 快速模式启动 - 跳过部分检查", "INFO")

            # 检查依赖
            if not self.check_dependencies():
                return False

            # 清理后端（快速模式跳过）
            if not fast_mode:
                if not self.clean_backend():
                    return False
            else:
                self.log("⚡ 快速模式: 跳过清理步骤", "INFO")

            # 编译后端
            if not self.compile_backend():
                return False

            # 启动后端
            if not self.start_backend():
                return False

            # 启动前端
            if not self.start_frontend():
                return False

            # 等待服务就绪
            if not self.wait_for_backend():
                self.stop_services()
                return False

            if not self.wait_for_frontend():
                self.stop_services()
                return False

            # 打印成功信息
            self.print_success_info()

            # 注册信号处理器
            signal.signal(signal.SIGINT, self.signal_handler)
            signal.signal(signal.SIGTERM, self.signal_handler)

            # 保持运行
            self.log("🔄 系统运行中，按 Ctrl+C 停止服务", "INFO")

            try:
                while True:
                    time.sleep(1)
                    # 检查进程状态
                    if self.backend_process and self.backend_process.poll() is not None:
                        self.log("❌ 后端进程意外退出", "ERROR")
                        break
                    if self.frontend_process and self.frontend_process.poll() is not None:
                        self.log("❌ 前端进程意外退出", "ERROR")
                        break
            except KeyboardInterrupt:
                self.log("🔄 用户中断，正在停止服务...", "WARNING")

            self.stop_services()
            return True

        except Exception as e:
            self.log(f"❌ 构建过程异常: {str(e)}", "ERROR")
            self.stop_services()
            return False

def main():
    """主函数"""
    fast_mode = False

    if len(sys.argv) > 1:
        command = sys.argv[1].lower()

        # 检查是否有快速模式参数
        if '--fast' in sys.argv or '-f' in sys.argv:
            fast_mode = True

        if command in ['-h', '--help', 'help']:
            help_text = f"""
{Colors.BOLD}{Colors.OKCYAN}资产管理系统 - 构建工具使用说明{Colors.ENDC}

{Colors.OKGREEN}用法:{Colors.ENDC}
  python build.py [命令] [选项]

{Colors.OKGREEN}命令:{Colors.ENDC}
  (无参数)     构建并启动完整系统
  help        显示此帮助信息
  version     显示版本信息
  clean       仅清理项目
  compile     仅编译后端
  backend     仅启动后端服务
  frontend    仅启动前端服务

{Colors.OKGREEN}选项:{Colors.ENDC}
  --fast, -f  快速模式（跳过清理步骤）

{Colors.OKGREEN}示例:{Colors.ENDC}
  python build.py              # 完整构建和启动
  python build.py --fast       # 快速模式启动
  python build.py clean        # 仅清理项目
  python build.py compile      # 仅编译后端
  python build.py backend      # 仅启动后端
  python build.py frontend     # 仅启动前端

{Colors.OKGREEN}系统要求:{Colors.ENDC}
  - Java 8+
  - Maven 3.6+
  - Node.js 16+
  - NPM 8+

{Colors.OKGREEN}端口配置:{Colors.ENDC}
  - 后端服务: http://localhost:8080
  - 前端服务: http://localhost:3000

{Colors.OKGREEN}性能提示:{Colors.ENDC}
  - 首次启动使用完整模式
  - 日常开发使用 --fast 快速模式
  - 确保数据库服务已启动
            """
            print(help_text)
            return

        elif command in ['-v', '--version', 'version']:
            version_info = f"""
{Colors.BOLD}{Colors.OKCYAN}资产管理系统构建工具{Colors.ENDC}
版本: 1.0.0
作者: 全栈设计大师
构建日期: {datetime.now().strftime('%Y-%m-%d')}

{Colors.OKGREEN}技术栈:{Colors.ENDC}
- 后端: Spring Boot 2.7+ + MyBatis Plus + MySQL
- 前端: Vue 3.3+ + Element Plus + Vite
- 构建: Maven + NPM
            """
            print(version_info)
            return

    # 创建构建器实例并运行
    builder = AssetManagementBuilder()
    success = builder.build_and_start(fast_mode=fast_mode)

    if success:
        print(f"\n{Colors.OKGREEN}🎉 系统已成功停止{Colors.ENDC}")
    else:
        print(f"\n{Colors.FAIL}❌ 系统启动失败{Colors.ENDC}")
        if not fast_mode:
            print(f"{Colors.WARNING}💡 提示: 尝试使用快速模式 'python build.py --fast'{Colors.ENDC}")
        sys.exit(1)

if __name__ == "__main__":
    main()
