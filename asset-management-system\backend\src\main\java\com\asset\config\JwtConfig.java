package com.asset.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "jwt")
public class JwtConfig {
    
    /**
     * JWT 密钥
     */
    private String secret = "assetManagementSystemSecretKey2024";
    
    /**
     * JWT 过期时间（毫秒）
     */
    private Long expiration = 86400000L; // 24小时
    
    /**
     * JWT Token 前缀
     */
    private String tokenPrefix = "Bearer ";
    
    /**
     * JWT Token Header名称
     */
    private String headerName = "Authorization";
}