package com.asset.service;

import com.asset.dto.AssetRequest;
import com.asset.dto.AssetResponse;
import com.asset.entity.Asset;
import com.asset.entity.AssetHistory;
import com.asset.exception.BusinessException;
import com.asset.repository.AssetRepository;
import com.asset.repository.AssetHistoryRepository;
import com.asset.utils.AssetCodeGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;

@Slf4j
@Service
public class ExcelService {

    @Autowired
    private AssetRepository assetRepository;

    @Autowired
    private AssetHistoryRepository assetHistoryRepository;

    @Autowired
    private AuthService authService;

    @Autowired
    private AssetCodeGenerator assetCodeGenerator;

    private static final String[] EXCEL_HEADERS = {
        "资产编号", "序列号", "产品型号", "产品类型", "品牌", "规格说明",
        "采购日期", "采购价格", "供应商", "保修期(月)", "状态", "当前位置", "备注"
    };

    /**
     * 导入Excel文件
     */
    @Transactional
    public Map<String, Object> importAssets(MultipartFile file) {
        if (file.isEmpty()) {
            throw new BusinessException("文件不能为空");
        }

        String filename = file.getOriginalFilename();
        if (!isExcelFile(filename)) {
            throw new BusinessException("文件格式不正确，请上传Excel文件");
        }

        Long currentUserId = authService.getCurrentUserId();
        String currentUsername = authService.getCurrentUser().getUsername();

        List<AssetRequest> successList = new ArrayList<>();
        List<Map<String, Object>> errorList = new ArrayList<>();
        int totalCount = 0;

        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = new XSSFWorkbook(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);
            Iterator<Row> rowIterator = sheet.iterator();

            // 跳过标题行
            if (rowIterator.hasNext()) {
                rowIterator.next();
            }

            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                totalCount++;

                try {
                    AssetRequest assetRequest = parseRowToAssetRequest(row);
                    
                    // 验证数据
                    validateAssetRequest(assetRequest, totalCount);
                    
                    // 检查资产编号唯一性
                    if (StringUtils.hasText(assetRequest.getAssetCode()) &&
                        assetRepository.countByAssetCodeAndIdNot(assetRequest.getAssetCode(), 0L) > 0) {
                        throw new BusinessException("资产编号已存在: " + assetRequest.getAssetCode());
                    }
                    
                    // 检查序列号唯一性
                    if (StringUtils.hasText(assetRequest.getSerialNumber()) &&
                        assetRepository.countBySerialNumberAndIdNot(assetRequest.getSerialNumber(), 0L) > 0) {
                        throw new BusinessException("序列号已存在: " + assetRequest.getSerialNumber());
                    }

                    successList.add(assetRequest);

                } catch (Exception e) {
                    Map<String, Object> errorInfo = new HashMap<>();
                    errorInfo.put("row", totalCount);
                    errorInfo.put("error", e.getMessage());
                    errorInfo.put("data", getRowData(row));
                    errorList.add(errorInfo);
                }
            }

            // 批量保存成功的数据
            int savedCount = 0;
            for (AssetRequest request : successList) {
                try {
                    Asset asset = new Asset();
                    BeanUtils.copyProperties(request, asset);
                    
                    // 设置资产编号
                    if (!StringUtils.hasText(asset.getAssetCode())) {
                        asset.setAssetCode(assetCodeGenerator.generateAssetCode());
                    }
                    
                    // 设置默认状态
                    if (asset.getStatus() == null) {
                        asset.setStatus(Asset.AssetStatus.PENDING);
                    }
                    
                    asset.setUserId(currentUserId);
                    asset.setCreatedAt(LocalDateTime.now());
                    asset.setUpdatedAt(LocalDateTime.now());
                    
                    assetRepository.insert(asset);
                    
                    // 记录历史
                    recordAssetHistory(asset.getId(), AssetHistory.OperationType.CREATE,
                                      null, assetToJson(asset), currentUsername, "批量导入创建");
                    
                    savedCount++;
                    
                } catch (Exception e) {
                    log.error("保存资产失败: {}", e.getMessage());
                    Map<String, Object> errorInfo = new HashMap<>();
                    errorInfo.put("row", savedCount + errorList.size() + 1);
                    errorInfo.put("error", "保存失败: " + e.getMessage());
                    errorInfo.put("data", request);
                    errorList.add(errorInfo);
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("totalCount", totalCount);
            result.put("successCount", savedCount);
            result.put("errorCount", errorList.size());
            result.put("errorList", errorList);

            log.info("用户 {} 导入资产完成: 总数 {}, 成功 {}, 失败 {}", 
                    currentUsername, totalCount, savedCount, errorList.size());

            return result;

        } catch (IOException e) {
            log.error("读取Excel文件失败: {}", e.getMessage());
            throw new BusinessException("读取Excel文件失败: " + e.getMessage());
        }
    }

    /**
     * 导出Excel文件
     */
    public void exportAssets(HttpServletResponse response, String keyword, 
                           Asset.AssetStatus status, String productType) {
        try {
            Long currentUserId = authService.isCurrentUserAdmin() ? null : authService.getCurrentUserId();
            
            // 查询数据
            List<Asset> assets = queryAssetsForExport(currentUserId, keyword, status, productType);
            
            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("资产列表");
            
            // 创建样式
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);
            
            // 创建标题行
            Row headerRow = sheet.createRow(0);
            for (int i = 0; i < EXCEL_HEADERS.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(EXCEL_HEADERS[i]);
                cell.setCellStyle(headerStyle);
                sheet.autoSizeColumn(i);
            }
            
            // 填充数据
            int rowNum = 1;
            for (Asset asset : assets) {
                Row row = sheet.createRow(rowNum++);
                fillAssetRow(row, asset, dataStyle);
            }
            
            // 自动调整列宽
            for (int i = 0; i < EXCEL_HEADERS.length; i++) {
                sheet.autoSizeColumn(i);
            }
            
            // 设置响应头
            String filename = "资产列表_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";
            String encodedFilename = URLEncoder.encode(filename, StandardCharsets.UTF_8.toString());
            
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFilename + "\"");
            
            // 写入响应流
            try (OutputStream outputStream = response.getOutputStream()) {
                workbook.write(outputStream);
                outputStream.flush();
            }
            
            workbook.close();
            
            log.info("用户 {} 导出 {} 条资产数据", authService.getCurrentUser().getUsername(), assets.size());
            
        } catch (Exception e) {
            log.error("导出Excel失败: {}", e.getMessage());
            throw new BusinessException("导出Excel失败: " + e.getMessage());
        }
    }

    /**
     * 下载导入模板
     */
    public void downloadTemplate(HttpServletResponse response) {
        try {
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("资产导入模板");
            
            // 创建样式
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle exampleStyle = createDataStyle(workbook);
            
            // 创建标题行
            Row headerRow = sheet.createRow(0);
            for (int i = 0; i < EXCEL_HEADERS.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(EXCEL_HEADERS[i]);
                cell.setCellStyle(headerStyle);
            }
            
            // 创建示例数据行
            Row exampleRow = sheet.createRow(1);
            String[] exampleData = {
                "IT-2024-001", "SN001234567", "ThinkPad X1 Carbon", "笔记本电脑", "联想",
                "14英寸，i7处理器，16GB内存，512GB SSD", "2024-01-15", "8999", "联想官方",
                "36", "PENDING", "仓库A区", "测试设备"
            };
            
            for (int i = 0; i < exampleData.length; i++) {
                Cell cell = exampleRow.createCell(i);
                cell.setCellValue(exampleData[i]);
                cell.setCellStyle(exampleStyle);
            }
            
            // 自动调整列宽
            for (int i = 0; i < EXCEL_HEADERS.length; i++) {
                sheet.autoSizeColumn(i);
            }
            
            // 设置响应头
            String filename = "资产导入模板.xlsx";
            String encodedFilename = URLEncoder.encode(filename, StandardCharsets.UTF_8.toString());
            
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFilename + "\"");
            
            // 写入响应流
            try (OutputStream outputStream = response.getOutputStream()) {
                workbook.write(outputStream);
                outputStream.flush();
            }
            
            workbook.close();
            
        } catch (Exception e) {
            log.error("下载模板失败: {}", e.getMessage());
            throw new BusinessException("下载模板失败: " + e.getMessage());
        }
    }

    // 私有方法

    private boolean isExcelFile(String filename) {
        return filename != null && 
               (filename.toLowerCase().endsWith(".xlsx") || filename.toLowerCase().endsWith(".xls"));
    }

    private AssetRequest parseRowToAssetRequest(Row row) {
        AssetRequest request = new AssetRequest();
        
        request.setAssetCode(getCellStringValue(row.getCell(0)));
        request.setSerialNumber(getCellStringValue(row.getCell(1)));
        request.setProductModel(getCellStringValue(row.getCell(2)));
        request.setProductType(getCellStringValue(row.getCell(3)));
        request.setBrand(getCellStringValue(row.getCell(4)));
        request.setSpecification(getCellStringValue(row.getCell(5)));
        
        // 采购日期
        String purchaseDateStr = getCellStringValue(row.getCell(6));
        if (StringUtils.hasText(purchaseDateStr)) {
            try {
                request.setPurchaseDate(LocalDate.parse(purchaseDateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            } catch (DateTimeParseException e) {
                try {
                    request.setPurchaseDate(LocalDate.parse(purchaseDateStr, DateTimeFormatter.ofPattern("yyyy/MM/dd")));
                } catch (DateTimeParseException ex) {
                    throw new BusinessException("采购日期格式错误，请使用 yyyy-MM-dd 或 yyyy/MM/dd 格式");
                }
            }
        }
        
        // 采购价格
        String priceStr = getCellStringValue(row.getCell(7));
        if (StringUtils.hasText(priceStr)) {
            try {
                request.setPurchasePrice(new BigDecimal(priceStr));
            } catch (NumberFormatException e) {
                throw new BusinessException("采购价格格式错误");
            }
        }
        
        request.setSupplier(getCellStringValue(row.getCell(8)));
        
        // 保修期
        String warrantyStr = getCellStringValue(row.getCell(9));
        if (StringUtils.hasText(warrantyStr)) {
            try {
                request.setWarrantyPeriod(Integer.parseInt(warrantyStr));
            } catch (NumberFormatException e) {
                throw new BusinessException("保修期格式错误");
            }
        }
        
        // 状态
        String statusStr = getCellStringValue(row.getCell(10));
        if (StringUtils.hasText(statusStr)) {
            try {
                Asset.AssetStatus status = parseStatus(statusStr);
                request.setStatus(status);
            } catch (Exception e) {
                throw new BusinessException("状态值无效: " + statusStr);
            }
        }
        
        request.setCurrentLocation(getCellStringValue(row.getCell(11)));
        request.setNotes(getCellStringValue(row.getCell(12)));
        
        return request;
    }

    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return DateTimeFormatter.ofPattern("yyyy-MM-dd").format(
                        cell.getLocalDateTimeCellValue().toLocalDate());
                } else {
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    private Asset.AssetStatus parseStatus(String statusStr) {
        Map<String, Asset.AssetStatus> statusMap = new HashMap<>();
        statusMap.put("待处理", Asset.AssetStatus.PENDING);
        statusMap.put("PENDING", Asset.AssetStatus.PENDING);
        statusMap.put("已入库", Asset.AssetStatus.RECEIVED);
        statusMap.put("RECEIVED", Asset.AssetStatus.RECEIVED);
        statusMap.put("已安装", Asset.AssetStatus.INSTALLED);
        statusMap.put("INSTALLED", Asset.AssetStatus.INSTALLED);
        statusMap.put("已出库", Asset.AssetStatus.OUTBOUND);
        statusMap.put("OUTBOUND", Asset.AssetStatus.OUTBOUND);
        statusMap.put("已报废", Asset.AssetStatus.SCRAPPED);
        statusMap.put("SCRAPPED", Asset.AssetStatus.SCRAPPED);
        
        Asset.AssetStatus status = statusMap.get(statusStr);
        if (status == null) {
            throw new BusinessException("无效的状态值: " + statusStr);
        }
        
        return status;
    }

    private void validateAssetRequest(AssetRequest request, int rowNum) {
        if (!StringUtils.hasText(request.getProductModel())) {
            throw new BusinessException("第" + rowNum + "行：产品型号不能为空");
        }
        
        if (!StringUtils.hasText(request.getProductType())) {
            throw new BusinessException("第" + rowNum + "行：产品类型不能为空");
        }
        
        if (request.getPurchasePrice() != null && request.getPurchasePrice().compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException("第" + rowNum + "行：采购价格不能为负数");
        }
        
        if (request.getWarrantyPeriod() != null && request.getWarrantyPeriod() < 0) {
            throw new BusinessException("第" + rowNum + "行：保修期不能为负数");
        }
    }

    private Map<String, Object> getRowData(Row row) {
        Map<String, Object> data = new HashMap<>();
        for (int i = 0; i < EXCEL_HEADERS.length && i < row.getLastCellNum(); i++) {
            data.put(EXCEL_HEADERS[i], getCellStringValue(row.getCell(i)));
        }
        return data;
    }

    private List<Asset> queryAssetsForExport(Long userId, String keyword, 
                                           Asset.AssetStatus status, String productType) {
        // 这里可以根据需要实现复杂的查询逻辑
        // 为了简化，直接查询所有数据
        return assetRepository.selectList(null);
    }

    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 12);
        style.setFont(font);
        
        return style;
    }

    private CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        
        return style;
    }

    private void fillAssetRow(Row row, Asset asset, CellStyle style) {
        int cellNum = 0;
        
        createCell(row, cellNum++, asset.getAssetCode(), style);
        createCell(row, cellNum++, asset.getSerialNumber(), style);
        createCell(row, cellNum++, asset.getProductModel(), style);
        createCell(row, cellNum++, asset.getProductType(), style);
        createCell(row, cellNum++, asset.getBrand(), style);
        createCell(row, cellNum++, asset.getSpecification(), style);
        createCell(row, cellNum++, asset.getPurchaseDate() != null ? asset.getPurchaseDate().toString() : "", style);
        createCell(row, cellNum++, asset.getPurchasePrice() != null ? asset.getPurchasePrice().toString() : "", style);
        createCell(row, cellNum++, asset.getSupplier(), style);
        createCell(row, cellNum++, asset.getWarrantyPeriod() != null ? asset.getWarrantyPeriod().toString() : "", style);
        createCell(row, cellNum++, asset.getStatus() != null ? asset.getStatus().getDescription() : "", style);
        createCell(row, cellNum++, asset.getCurrentLocation(), style);
        createCell(row, cellNum++, asset.getNotes(), style);
    }

    private void createCell(Row row, int cellNum, String value, CellStyle style) {
        Cell cell = row.createCell(cellNum);
        cell.setCellValue(value != null ? value : "");
        cell.setCellStyle(style);
    }

    private void recordAssetHistory(Long assetId, AssetHistory.OperationType operationType,
                                   String oldValue, String newValue, String operator, String description) {
        try {
            AssetHistory history = new AssetHistory();
            history.setAssetId(assetId);
            history.setOperationType(operationType);
            history.setOldValue(oldValue);
            history.setNewValue(newValue);
            history.setOperator(operator);
            history.setDescription(description);
            history.setCreatedAt(LocalDateTime.now());
            
            assetHistoryRepository.insert(history);
        } catch (Exception e) {
            log.error("记录资产历史失败: {}", e.getMessage());
        }
    }

    private String assetToJson(Asset asset) {
        try {
            return asset.toString();
        } catch (Exception e) {
            return asset.toString();
        }
    }
}