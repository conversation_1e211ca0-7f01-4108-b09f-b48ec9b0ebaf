import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/components/Layout/index.vue'),
    redirect: '/dashboard',
    meta: { requiresAuth: true },
    children: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard/index.vue'),
        meta: { title: '仪表板', icon: 'Dashboard' }
      },
      {
        path: '/assets',
        name: 'Assets',
        component: () => import('@/views/Assets/index.vue'),
        meta: { title: '资产管理', icon: 'Box' }
      },
      {
        path: '/assets/detail/:id',
        name: 'AssetDetail',
        component: () => import('@/views/Assets/Detail.vue'),
        meta: { title: '资产详情', hidden: true }
      },
      {
        path: '/assets/create',
        name: 'AssetCreate',
        component: () => import('@/views/Assets/Form.vue'),
        meta: { title: '新增资产', hidden: true }
      },
      {
        path: '/assets/edit/:id',
        name: 'AssetEdit',
        component: () => import('@/views/Assets/Form.vue'),
        meta: { title: '编辑资产', hidden: true }
      },
      {
        path: '/assets/search',
        name: 'AssetSearch',
        component: () => import('@/views/Assets/Search.vue'),
        meta: { title: '高级搜索', icon: 'Search' }
      },
      {
        path: '/location-tracking',
        name: 'LocationTracking',
        component: () => import('@/views/Assets/LocationTracking.vue'),
        meta: { title: '位置跟踪', icon: 'LocationFilled' }
      },
      {
        path: '/import-export',
        name: 'ImportExport',
        component: () => import('@/views/ImportExport/index.vue'),
        meta: { title: '导入导出', icon: 'Upload' }
      },
      // 资产操作路由
      {
        path: '/operations/inbound',
        name: 'OperationsInbound',
        component: () => import('@/views/Operations/Inbound.vue'),
        meta: { title: '入库管理', icon: 'Download' }
      },
      {
        path: '/operations/install',
        name: 'OperationsInstall',
        component: () => import('@/views/Operations/Install.vue'),
        meta: { title: '安装管理', icon: 'Tools' }
      },
      {
        path: '/operations/outbound',
        name: 'OperationsOutbound',
        component: () => import('@/views/Operations/Outbound.vue'),
        meta: { title: '出库管理', icon: 'Upload' }
      },
      {
        path: '/statistics',
        name: 'Statistics',
        component: () => import('@/views/Statistics/index.vue'),
        meta: { title: '统计报表', icon: 'DataAnalysis' }
      },
      {
        path: '/inventory/statistics',
        name: 'InventoryStatistics',
        component: () => import('@/views/Inventory/Statistics.vue'),
        meta: { title: '库存统计', icon: 'DataBoard' }
      },
      // 统计分析子页面
      {
        path: '/statistics/by-type',
        name: 'StatisticsByType',
        component: () => import('@/views/Statistics/ByType.vue'),
        meta: { title: '分类统计', icon: 'PieChart' }
      },
      {
        path: '/statistics/by-location',
        name: 'StatisticsByLocation',
        component: () => import('@/views/Statistics/ByLocation.vue'),
        meta: { title: '位置统计', icon: 'LocationFilled' }
      },
      {
        path: '/users',
        name: 'Users',
        component: () => import('@/views/Users/<USER>'),
        meta: { title: '用户管理', icon: 'User', roles: ['ADMIN'] }
      },
      {
        path: '/users/create',
        name: 'UserCreate',
        component: () => import('@/views/Users/<USER>'),
        meta: { title: '创建用户', hidden: true, roles: ['ADMIN'] }
      },
      {
        path: '/users/edit/:id',
        name: 'UserEdit',
        component: () => import('@/views/Users/<USER>'),
        meta: { title: '编辑用户', hidden: true, roles: ['ADMIN'] }
      },
      {
        path: '/users/permissions',
        name: 'UserPermissions',
        component: () => import('@/views/Users/<USER>'),
        meta: { title: '权限管理', hidden: true, roles: ['ADMIN'] }
      },
      {
        path: '/settings/product-types',
        name: 'ProductTypes',
        component: () => import('@/views/Settings/ProductTypes.vue'),
        meta: { title: '产品类型管理', icon: 'Collection' }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // 如果有token但用户信息为空，先检查认证状态
  if (authStore.token && !authStore.user) {
    await authStore.checkAuth()
  }

  // 需要认证的页面
  if (to.meta.requiresAuth) {
    if (!authStore.isAuthenticated) {
      next('/login')
      return
    }

    // 检查角色权限
    if (to.meta.roles && !to.meta.roles.includes(authStore.user?.role)) {
      next('/403')
      return
    }
  }

  // 已登录用户访问登录页，重定向到首页
  if (to.path === '/login' && authStore.isAuthenticated) {
    next('/')
    return
  }

  next()
})

export default router