<template>
  <div class="asset-search">
    <el-card class="search-card">
      <template #header>
        <div class="card-header">
          <span>高级搜索</span>
          <el-button type="primary" @click="resetForm">重置</el-button>
        </div>
      </template>
      
      <el-form :model="searchForm" label-width="120px" :inline="false">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="资产编号">
              <el-input v-model="searchForm.assetCode" placeholder="输入资产编号" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="序列号">
              <el-input v-model="searchForm.serialNumber" placeholder="输入序列号" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="产品型号">
              <el-input v-model="searchForm.productModel" placeholder="输入产品型号" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="产品类型">
              <el-select v-model="searchForm.productType" placeholder="选择产品类型" clearable>
                <el-option
                  v-for="type in searchOptions.productTypes"
                  :key="type"
                  :label="type"
                  :value="type"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="品牌">
              <el-select v-model="searchForm.brand" placeholder="选择品牌" clearable>
                <el-option
                  v-for="brand in searchOptions.brands"
                  :key="brand"
                  :label="brand"
                  :value="brand"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="状态">
              <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
                <el-option
                  v-for="status in searchOptions.statuses"
                  :key="status"
                  :label="getStatusLabel(status)"
                  :value="status"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="当前位置">
              <el-select v-model="searchForm.currentLocation" placeholder="选择位置" clearable>
                <el-option
                  v-for="location in searchOptions.locations"
                  :key="location"
                  :label="location"
                  :value="location"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="供应商">
              <el-select v-model="searchForm.supplier" placeholder="选择供应商" clearable>
                <el-option
                  v-for="supplier in searchOptions.suppliers"
                  :key="supplier"
                  :label="supplier"
                  :value="supplier"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="价格范围">
              <el-row :gutter="10">
                <el-col :span="12">
                  <el-input-number v-model="searchForm.minPrice" placeholder="最低价格" :min="0" style="width: 100%" />
                </el-col>
                <el-col :span="12">
                  <el-input-number v-model="searchForm.maxPrice" placeholder="最高价格" :min="0" style="width: 100%" />
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="采购日期">
              <el-date-picker
                v-model="purchaseDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="handlePurchaseDateChange"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="createdAtRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                @change="handleCreatedAtChange"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="24">
            <el-form-item>
              <el-button type="primary" @click="handleSearch" :loading="loading">
                <el-icon><Search /></el-icon>
                搜索
              </el-button>
              <el-button @click="resetForm">重置</el-button>
              <el-button @click="exportResults" :disabled="!hasResults">导出结果</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    
    <!-- 搜索结果 -->
    <el-card v-if="hasResults" class="result-card">
      <template #header>
        <div class="card-header">
          <span>搜索结果 (共 {{ pagination.total }} 条)</span>
          <div>
            <el-select v-model="searchForm.sortBy" @change="handleSearch" style="width: 120px; margin-right: 10px">
              <el-option label="创建时间" value="createdAt" />
              <el-option label="资产编号" value="assetCode" />
              <el-option label="采购日期" value="purchaseDate" />
              <el-option label="采购价格" value="purchasePrice" />
            </el-select>
            <el-select v-model="searchForm.sortOrder" @change="handleSearch" style="width: 80px">
              <el-option label="降序" value="desc" />
              <el-option label="升序" value="asc" />
            </el-select>
          </div>
        </div>
      </template>
      
      <el-table :data="searchResults" stripe>
        <el-table-column prop="assetCode" label="资产编号" width="120" />
        <el-table-column prop="serialNumber" label="序列号" width="120" />
        <el-table-column prop="productModel" label="产品型号" width="150" />
        <el-table-column prop="productType" label="产品类型" width="120" />
        <el-table-column prop="brand" label="品牌" width="100" />
        <el-table-column prop="currentLocation" label="当前位置" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="purchasePrice" label="采购价格" width="120">
          <template #default="{ row }">
            ¥{{ row.purchasePrice ? row.purchasePrice.toFixed(2) : '0.00' }}
          </template>
        </el-table-column>
        <el-table-column prop="purchaseDate" label="采购日期" width="120" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewDetail(row)">
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { advancedSearchAssets, getSearchOptions } from '@/api/asset'
import { Search } from '@element-plus/icons-vue'

const router = useRouter()
const loading = ref(false)
const hasResults = ref(false)
const searchResults = ref([])
const purchaseDateRange = ref([])
const createdAtRange = ref([])

const searchForm = reactive({
  assetCode: '',
  serialNumber: '',
  productModel: '',
  productType: '',
  brand: '',
  status: '',
  currentLocation: '',
  supplier: '',
  minPrice: null,
  maxPrice: null,
  purchaseDateStart: null,
  purchaseDateEnd: null,
  createdAtStart: null,
  createdAtEnd: null,
  sortBy: 'createdAt',
  sortOrder: 'desc',
  page: 1,
  size: 20
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const searchOptions = reactive({
  productTypes: [],
  brands: [],
  locations: [],
  suppliers: [],
  statuses: ['PENDING', 'RECEIVED', 'INSTALLED', 'OUTBOUND', 'SCRAPPED']
})

const statusLabels = {
  PENDING: '待处理',
  RECEIVED: '已入库',
  INSTALLED: '已安装',
  OUTBOUND: '已出库',
  SCRAPPED: '已报废'
}

const statusTypes = {
  PENDING: 'warning',
  RECEIVED: 'success',
  INSTALLED: 'primary',
  OUTBOUND: 'info',
  SCRAPPED: 'danger'
}

const getStatusLabel = (status) => statusLabels[status] || status
const getStatusType = (status) => statusTypes[status] || 'info'

const loadSearchOptions = async () => {
  try {
    const { data } = await getSearchOptions()
    Object.assign(searchOptions, data)
  } catch (error) {
    console.error('加载搜索选项失败:', error)
  }
}

const handlePurchaseDateChange = (dates) => {
  if (dates && dates.length === 2) {
    searchForm.purchaseDateStart = dates[0]
    searchForm.purchaseDateEnd = dates[1]
  } else {
    searchForm.purchaseDateStart = null
    searchForm.purchaseDateEnd = null
  }
}

const handleCreatedAtChange = (dates) => {
  if (dates && dates.length === 2) {
    searchForm.createdAtStart = dates[0]
    searchForm.createdAtEnd = dates[1]
  } else {
    searchForm.createdAtStart = null
    searchForm.createdAtEnd = null
  }
}

const handleSearch = async () => {
  loading.value = true
  try {
    searchForm.page = pagination.page
    searchForm.size = pagination.size
    
    const { data } = await advancedSearchAssets(searchForm)
    
    searchResults.value = data.records
    pagination.total = data.total
    hasResults.value = true
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败')
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  Object.keys(searchForm).forEach(key => {
    if (key === 'sortBy') {
      searchForm[key] = 'createdAt'
    } else if (key === 'sortOrder') {
      searchForm[key] = 'desc'
    } else if (key === 'page') {
      searchForm[key] = 1
    } else if (key === 'size') {
      searchForm[key] = 20
    } else {
      searchForm[key] = key.includes('Price') ? null : ''
    }
  })
  
  purchaseDateRange.value = []
  createdAtRange.value = []
  pagination.page = 1
  pagination.size = 20
  pagination.total = 0
  hasResults.value = false
  searchResults.value = []
}

const handlePageChange = (page) => {
  pagination.page = page
  handleSearch()
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  handleSearch()
}

const viewDetail = (row) => {
  router.push(`/assets/${row.id}`)
}

const exportResults = () => {
  // TODO: 实现导出功能
  ElMessage.info('导出功能待实现')
}

onMounted(() => {
  loadSearchOptions()
})
</script>

<style scoped>
.asset-search {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.result-card {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>