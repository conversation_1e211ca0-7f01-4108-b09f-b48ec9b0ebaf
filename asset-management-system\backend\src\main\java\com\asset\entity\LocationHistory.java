package com.asset.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "location_history")
@TableName("location_history")
public class LocationHistory {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Long id;

    @NotNull(message = "资产ID不能为空")
    @Column(name = "asset_id", nullable = false)
    private Long assetId;

    @Column(name = "from_location", length = 200)
    private String fromLocation;

    @Column(name = "to_location", length = 200)
    private String toLocation;

    @Column(name = "move_reason", length = 500)
    private String moveReason;

    @Column(length = 100)
    private String operator;

    @TableField(fill = FieldFill.INSERT)
    @Column(name = "moved_at")
    private LocalDateTime movedAt;

    // 多对一关系：多个位置历史属于一个资产
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "asset_id", insertable = false, updatable = false)
    @TableField(exist = false)
    private Asset asset;
}