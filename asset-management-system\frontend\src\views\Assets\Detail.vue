<template>
  <div class="asset-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" :icon="ArrowLeft" class="back-btn">
          返回
        </el-button>
        <div class="header-title">
          <h2>{{ assetDetail.assetCode }}</h2>
          <p>{{ assetDetail.productModel }}</p>
        </div>
      </div>
      <div class="header-actions">
        <el-button 
          type="primary" 
          @click="handleEdit"
          :icon="Edit"
        >
          编辑
        </el-button>
        <el-button 
          type="danger" 
          @click="handleDelete"
          :icon="Delete"
        >
          删除
        </el-button>
      </div>
    </div>

    <el-row :gutter="24" v-loading="loading">
      <!-- 基本信息 -->
      <el-col :lg="16" :md="24">
        <el-card title="基本信息" class="info-card">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
              <el-tag :type="getStatusType(assetDetail.status)" size="large">
                {{ getStatusText(assetDetail.status) }}
              </el-tag>
            </div>
          </template>

          <div class="info-grid">
            <div class="info-item">
              <label>资产编号</label>
              <span>{{ assetDetail.assetCode || '-' }}</span>
            </div>
            <div class="info-item">
              <label>序列号</label>
              <span>{{ assetDetail.serialNumber || '-' }}</span>
            </div>
            <div class="info-item">
              <label>产品型号</label>
              <span>{{ assetDetail.productModel || '-' }}</span>
            </div>
            <div class="info-item">
              <label>产品类型</label>
              <span>{{ assetDetail.productType || '-' }}</span>
            </div>
            <div class="info-item">
              <label>品牌</label>
              <span>{{ assetDetail.brand || '-' }}</span>
            </div>
            <div class="info-item">
              <label>供应商</label>
              <span>{{ assetDetail.supplier || '-' }}</span>
            </div>
            <div class="info-item">
              <label>采购日期</label>
              <span>{{ formatDate(assetDetail.purchaseDate) }}</span>
            </div>
            <div class="info-item">
              <label>采购价格</label>
              <span>{{ formatPrice(assetDetail.purchasePrice) }}</span>
            </div>
            <div class="info-item">
              <label>保修期</label>
              <span>{{ formatWarranty(assetDetail.warrantyPeriod) }}</span>
            </div>
            <div class="info-item">
              <label>当前位置</label>
              <span>{{ assetDetail.currentLocation || '-' }}</span>
            </div>
          </div>

          <div class="info-item full-width" v-if="assetDetail.specification">
            <label>规格说明</label>
            <div class="specification">{{ assetDetail.specification }}</div>
          </div>

          <div class="info-item full-width" v-if="assetDetail.notes">
            <label>备注信息</label>
            <div class="notes">{{ assetDetail.notes }}</div>
          </div>
        </el-card>

        <!-- 流转信息 -->
        <el-card title="流转信息" class="info-card">
          <div class="flow-timeline">
            <el-timeline>
              <el-timeline-item
                v-if="assetDetail.createdAt"
                icon="Plus"
                color="#67c23a"
              >
                <div class="timeline-content">
                  <h4>资产创建</h4>
                  <p>{{ formatDateTime(assetDetail.createdAt) }}</p>
                </div>
              </el-timeline-item>

              <el-timeline-item
                v-if="assetDetail.receivedAt"
                icon="Checked"
                color="#409eff"
              >
                <div class="timeline-content">
                  <h4>入库接收</h4>
                  <p>{{ formatDateTime(assetDetail.receivedAt) }}</p>
                  <span v-if="assetDetail.receiver">签收人: {{ assetDetail.receiver }}</span>
                </div>
              </el-timeline-item>

              <el-timeline-item
                v-if="assetDetail.installedAt"
                icon="Tools"
                color="#e6a23c"
              >
                <div class="timeline-content">
                  <h4>安装完成</h4>
                  <p>{{ formatDateTime(assetDetail.installedAt) }}</p>
                  <span v-if="assetDetail.installer">安装人员: {{ assetDetail.installer }}</span>
                  <span v-if="assetDetail.installLocation">安装位置: {{ assetDetail.installLocation }}</span>
                </div>
              </el-timeline-item>

              <el-timeline-item
                v-if="assetDetail.outboundAt"
                icon="Upload"
                color="#f56c6c"
              >
                <div class="timeline-content">
                  <h4>出库处理</h4>
                  <p>{{ formatDateTime(assetDetail.outboundAt) }}</p>
                  <span v-if="assetDetail.outboundOperator">操作人员: {{ assetDetail.outboundOperator }}</span>
                  <span v-if="assetDetail.outboundReason">出库原因: {{ assetDetail.outboundReason }}</span>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-card>
      </el-col>

      <!-- 侧边信息 -->
      <el-col :lg="8" :md="24">
        <!-- 状态卡片 -->
        <el-card class="status-card">
          <template #header>
            <span>状态信息</span>
          </template>
          <div class="status-info">
            <div class="status-item">
              <div class="status-circle" :class="getStatusClass(assetDetail.status)"></div>
              <div class="status-text">
                <h4>{{ getStatusText(assetDetail.status) }}</h4>
                <p>{{ getStatusDescription(assetDetail.status) }}</p>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 操作记录 -->
        <el-card title="最近操作" class="operations-card">
          <div class="operations-list">
            <div 
              v-for="operation in recentOperations" 
              :key="operation.id"
              class="operation-item"
            >
              <div class="operation-icon" :class="operation.type">
                <el-icon>
                  <component :is="operation.icon" />
                </el-icon>
              </div>
              <div class="operation-content">
                <p class="operation-desc">{{ operation.description }}</p>
                <span class="operation-time">{{ operation.time }}</span>
              </div>
            </div>
          </div>
          
          <div class="view-all">
            <el-link type="primary" @click="viewAllHistory">
              查看完整历史记录
            </el-link>
          </div>
        </el-card>

        <!-- 快速操作 -->
        <el-card title="快速操作" class="quick-actions-card">
          <div class="quick-actions">
            <el-button 
              v-if="assetDetail.status === 'PENDING'"
              type="success" 
              @click="handleReceive"
              :icon="Checked"
              class="action-btn"
            >
              确认入库
            </el-button>
            
            <el-button 
              v-if="assetDetail.status === 'RECEIVED'"
              type="warning" 
              @click="handleInstall"
              :icon="Tools"
              class="action-btn"
            >
              标记安装
            </el-button>
            
            <el-button 
              v-if="['RECEIVED', 'INSTALLED'].includes(assetDetail.status)"
              type="info" 
              @click="handleMove"
              :icon="Location"
              class="action-btn"
            >
              位置变更
            </el-button>
            
            <el-button 
              v-if="['RECEIVED', 'INSTALLED'].includes(assetDetail.status)"
              type="danger" 
              @click="handleOutbound"
              :icon="Upload"
              class="action-btn"
            >
              资产出库
            </el-button>
            
            <el-button 
              v-if="assetDetail.status === 'OUTBOUND'"
              type="success" 
              @click="handleReactivate"
              :icon="Refresh"
              class="action-btn"
            >
              重新激活
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 操作对话框 -->
    <AssetOperation
      v-model="operationDialogVisible"
      :operation-type="currentOperation"
      :asset-info="assetDetail"
      @success="handleOperationSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getAssetById, deleteAsset } from '@/api/asset'
import AssetOperation from '@/components/AssetOperation/index.vue'
import dayjs from 'dayjs'
import { ArrowLeft } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

const loading = ref(false)
const assetDetail = ref({})
const recentOperations = ref([
  {
    id: 1,
    type: 'create',
    icon: 'Plus',
    description: '资产创建完成',
    time: '2小时前'
  },
  {
    id: 2,
    type: 'update',
    icon: 'Edit',
    description: '更新了资产信息',
    time: '1天前'
  }
])

// 状态配置
const statusConfig = {
  PENDING: { 
    text: '待处理', 
    type: 'info', 
    class: 'pending',
    description: '资产已创建，等待入库'
  },
  RECEIVED: { 
    text: '已入库', 
    type: 'success', 
    class: 'received',
    description: '资产已签收入库'
  },
  INSTALLED: { 
    text: '已安装', 
    type: 'primary', 
    class: 'installed',
    description: '资产已安装部署'
  },
  OUTBOUND: { 
    text: '已出库', 
    type: 'warning', 
    class: 'outbound',
    description: '资产已出库处理'
  },
  SCRAPPED: { 
    text: '已报废', 
    type: 'danger', 
    class: 'scrapped',
    description: '资产已报废处理'
  }
}

const getStatusText = (status) => statusConfig[status]?.text || status
const getStatusType = (status) => statusConfig[status]?.type || 'info'
const getStatusClass = (status) => statusConfig[status]?.class || 'default'
const getStatusDescription = (status) => statusConfig[status]?.description || ''

// 格式化函数
const formatDate = (date) => {
  return date ? dayjs(date).format('YYYY-MM-DD') : '-'
}

const formatDateTime = (dateTime) => {
  return dateTime ? dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss') : '-'
}

const formatPrice = (price) => {
  return price ? `¥${price.toLocaleString()}` : '-'
}

const formatWarranty = (months) => {
  return months ? `${months}个月` : '-'
}

// 加载资产详情
const loadAssetDetail = async () => {
  loading.value = true
  try {
    const { data } = await getAssetById(route.params.id)
    assetDetail.value = data
  } catch (error) {
    ElMessage.error('加载资产详情失败')
  } finally {
    loading.value = false
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 编辑资产
const handleEdit = () => {
  router.push(`/assets/edit/${assetDetail.value.id}`)
}

// 删除资产
const handleDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除资产 "${assetDetail.value.assetCode}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteAsset(assetDetail.value.id)
    ElMessage.success('删除成功')
    router.push('/assets')
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 操作对话框
const operationDialogVisible = ref(false)
const currentOperation = ref('')

// 快速操作
const handleReceive = () => {
  currentOperation.value = 'receive'
  operationDialogVisible.value = true
}

const handleInstall = () => {
  currentOperation.value = 'install'
  operationDialogVisible.value = true
}

const handleMove = () => {
  currentOperation.value = 'move'
  operationDialogVisible.value = true
}

const handleOutbound = () => {
  currentOperation.value = 'outbound'
  operationDialogVisible.value = true
}

const handleReactivate = () => {
  currentOperation.value = 'reactivate'
  operationDialogVisible.value = true
}

// 操作成功后的回调
const handleOperationSuccess = (updatedAsset) => {
  assetDetail.value = updatedAsset
  ElMessage.success('操作完成')
}

const viewAllHistory = () => {
  ElMessage.info('历史记录功能开发中...')
}

// 初始化
onMounted(() => {
  loadAssetDetail()
})
</script>

<style scoped lang="scss">
.asset-detail {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-btn {
  border: none;
  background: #f5f5f5;
  color: #666;
  
  &:hover {
    background: #e6e6e6;
    color: #333;
  }
}

.header-title {
  h2 {
    margin: 0 0 4px 0;
    font-size: 24px;
    font-weight: 600;
    color: #333;
  }
  
  p {
    margin: 0;
    color: #666;
    font-size: 14px;
  }
}

.info-card {
  margin-bottom: 24px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.info-item {
  &.full-width {
    grid-column: 1 / -1;
  }
  
  label {
    display: block;
    font-size: 14px;
    color: #666;
    margin-bottom: 4px;
    font-weight: 500;
  }
  
  span {
    font-size: 16px;
    color: #333;
  }
  
  .specification,
  .notes {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    line-height: 1.6;
    color: #333;
  }
}

.flow-timeline {
  padding: 16px 0;
}

.timeline-content {
  h4 {
    margin: 0 0 4px 0;
    font-size: 16px;
    color: #333;
  }
  
  p {
    margin: 0 0 4px 0;
    color: #666;
    font-size: 14px;
  }
  
  span {
    display: block;
    font-size: 12px;
    color: #999;
  }
}

.status-card {
  margin-bottom: 24px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-info {
  padding: 16px 0;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-circle {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  
  &.pending { background: #909399; }
  &.received { background: #67c23a; }
  &.installed { background: #409eff; }
  &.outbound { background: #e6a23c; }
  &.scrapped { background: #f56c6c; }
}

.status-text {
  h4 {
    margin: 0 0 4px 0;
    font-size: 16px;
    color: #333;
  }
  
  p {
    margin: 0;
    font-size: 14px;
    color: #666;
  }
}

.operations-card {
  margin-bottom: 24px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.operations-list {
  margin-bottom: 16px;
}

.operation-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.operation-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  
  &.create { background: #67c23a; }
  &.update { background: #409eff; }
  &.receive { background: #e6a23c; }
  &.install { background: #909399; }
}

.operation-content {
  flex: 1;
  
  .operation-desc {
    margin: 0 0 4px 0;
    font-size: 14px;
    color: #333;
  }
  
  .operation-time {
    font-size: 12px;
    color: #999;
  }
}

.view-all {
  text-align: center;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.quick-actions-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-btn {
  width: 100%;
  height: 40px;
  font-weight: 500;
}

// Element Plus 样式覆盖
:deep(.el-card__body) {
  padding: 24px;
}

:deep(.el-timeline-item__content) {
  padding-bottom: 16px;
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .header-left {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
}
</style>