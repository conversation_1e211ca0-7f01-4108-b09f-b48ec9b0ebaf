<template>
  <div class="outbound-container">
    <div class="page-header">
      <h2>出库管理</h2>
      <p>管理资产的出库操作和记录</p>
    </div>

    <!-- 搜索筛选区域 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="资产编号">
          <el-input
            v-model="searchForm.assetCode"
            placeholder="请输入资产编号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="当前位置">
          <el-input
            v-model="searchForm.currentLocation"
            placeholder="请输入当前位置"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="已安装" value="INSTALLED" />
            <el-option label="已出库" value="OUTBOUND" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>资产出库管理</span>
          <el-button type="danger" @click="handleBatchOutbound">
            <el-icon><Upload /></el-icon>
            批量出库
          </el-button>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="tableData"
        @selection-change="handleSelectionChange"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="assetCode" label="资产编号" width="120" />
        <el-table-column prop="serialNumber" label="序列号" width="150" />
        <el-table-column prop="productModel" label="产品型号" width="150" />
        <el-table-column prop="productType" label="产品类型" width="120" />
        <el-table-column prop="currentLocation" label="当前位置" width="180" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="outboundTime" label="出库时间" width="180">
          <template #default="{ row }">
            {{ row.outboundTime ? formatDate(row.outboundTime) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="outboundPerson" label="出库人员" width="120">
          <template #default="{ row }">
            {{ row.outboundPerson || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="row.status === 'INSTALLED'"
              type="danger"
              size="small"
              @click="handleOutbound(row)"
            >
              出库
            </el-button>
            <el-button
              type="info"
              size="small"
              @click="handleViewDetail(row)"
            >
              详情
            </el-button>
            <el-button
              type="success"
              size="small"
              @click="handleViewHistory(row)"
            >
              历史
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 出库对话框 -->
    <el-dialog
      v-model="outboundDialogVisible"
      title="资产出库"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="outboundFormRef"
        :model="outboundForm"
        :rules="outboundRules"
        label-width="100px"
      >
        <el-form-item label="资产编号">
          <el-input v-model="outboundForm.assetCode" disabled />
        </el-form-item>
        <el-form-item label="产品型号">
          <el-input v-model="outboundForm.productModel" disabled />
        </el-form-item>
        <el-form-item label="当前位置">
          <el-input v-model="outboundForm.currentLocation" disabled />
        </el-form-item>
        <el-form-item label="出库原因" prop="outboundReason">
          <el-select
            v-model="outboundForm.outboundReason"
            placeholder="请选择出库原因"
            style="width: 100%"
          >
            <el-option label="设备报废" value="SCRAP" />
            <el-option label="设备维修" value="REPAIR" />
            <el-option label="设备调拨" value="TRANSFER" />
            <el-option label="设备退回" value="RETURN" />
            <el-option label="其他" value="OTHER" />
          </el-select>
        </el-form-item>
        <el-form-item label="出库人员" prop="outboundPerson">
          <el-input
            v-model="outboundForm.outboundPerson"
            placeholder="请输入出库人员姓名"
          />
        </el-form-item>
        <el-form-item label="出库时间" prop="outboundTime">
          <el-date-picker
            v-model="outboundForm.outboundTime"
            type="datetime"
            placeholder="选择出库时间"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="outboundForm.remarks"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="outboundDialogVisible = false">取消</el-button>
        <el-button type="danger" @click="handleConfirmOutbound" :loading="submitting">
          确认出库
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Upload } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const tableData = ref([])
const selectedRows = ref([])
const outboundDialogVisible = ref(false)
const outboundFormRef = ref()

// 搜索表单
const searchForm = reactive({
  assetCode: '',
  currentLocation: '',
  status: ''
})

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

// 出库表单
const outboundForm = reactive({
  assetId: null,
  assetCode: '',
  productModel: '',
  currentLocation: '',
  outboundReason: '',
  outboundPerson: '',
  outboundTime: new Date(),
  remarks: ''
})

// 表单验证规则
const outboundRules = {
  outboundReason: [
    { required: true, message: '请选择出库原因', trigger: 'change' }
  ],
  outboundPerson: [
    { required: true, message: '请输入出库人员姓名', trigger: 'blur' }
  ],
  outboundTime: [
    { required: true, message: '请选择出库时间', trigger: 'change' }
  ]
}

// 方法
const loadData = async () => {
  loading.value = true
  try {
    // TODO: 调用真实API获取出库数据
    // const response = await getOutboundAssets(searchForm, pagination)
    // tableData.value = response.data.records
    // pagination.total = response.data.total

    tableData.value = []
    pagination.total = 0
  } catch (error) {
    ElMessage.error('加载数据失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    assetCode: '',
    currentLocation: '',
    status: ''
  })
  handleSearch()
}

const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

const handleOutbound = (row) => {
  Object.assign(outboundForm, {
    assetId: row.id,
    assetCode: row.assetCode,
    productModel: row.productModel,
    currentLocation: row.currentLocation,
    outboundReason: '',
    outboundPerson: '',
    outboundTime: new Date(),
    remarks: ''
  })
  outboundDialogVisible.value = true
}

const handleConfirmOutbound = async () => {
  try {
    await outboundFormRef.value.validate()
    submitting.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('出库成功')
    outboundDialogVisible.value = false
    loadData()
  } catch (error) {
    if (error.message) {
      ElMessage.error('出库失败: ' + error.message)
    }
  } finally {
    submitting.value = false
  }
}

const handleBatchOutbound = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要出库的资产')
    return
  }
  
  ElMessageBox.confirm(
    `确定要批量出库选中的 ${selectedRows.value.length} 个资产吗？`,
    '批量出库确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('批量出库成功')
    loadData()
  })
}

const handleViewDetail = (row) => {
  // 跳转到资产详情页
  console.log('查看详情:', row)
}

const handleViewHistory = (row) => {
  // 查看操作历史
  console.log('查看历史:', row)
}

const handleDialogClose = () => {
  outboundFormRef.value?.resetFields()
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  loadData()
}

const handleCurrentChange = (page) => {
  pagination.current = page
  loadData()
}

// 工具方法
const getStatusType = (status) => {
  const statusMap = {
    'PENDING': 'warning',
    'RECEIVED': 'success',
    'INSTALLED': 'info',
    'OUTBOUND': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'PENDING': '待入库',
    'RECEIVED': '已入库',
    'INSTALLED': '已安装',
    'OUTBOUND': '已出库'
  }
  return statusMap[status] || status
}

const formatDate = (date) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.outbound-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 0;
}

.table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 响应式设计 */
@media (min-width: 1920px) {
  .outbound-container {
    padding: 24px;
  }
  
  .search-form .el-form-item {
    margin-right: 24px;
  }
}

@media (min-width: 2560px) {
  .outbound-container {
    padding: 32px;
  }
  
  .page-header h2 {
    font-size: 28px;
  }
  
  .page-header p {
    font-size: 16px;
  }
}
</style>
