package com.asset.controller;

import com.asset.dto.ApiResponse;
import com.asset.dto.LocationChangeRequest;
import com.asset.dto.LocationHistoryResponse;
import com.asset.dto.PageResponse;
import com.asset.service.LocationTrackingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/location-tracking")
@Validated
public class LocationTrackingController extends BaseController {

    @Autowired
    private LocationTrackingService locationTrackingService;

    /**
     * 变更资产位置
     */
    @PostMapping("/assets/{assetId}/change-location")
    public ApiResponse<Void> changeAssetLocation(@PathVariable Long assetId,
                                               @Valid @RequestBody LocationChangeRequest request) {
        try {
            locationTrackingService.changeAssetLocation(assetId, request);
            return ApiResponse.success("位置变更成功");
        } catch (Exception e) {
            log.error("变更资产位置失败: {}", e.getMessage());
            return ApiResponse.error("变更资产位置失败: " + e.getMessage());
        }
    }

    /**
     * 获取资产位置历史
     */
    @GetMapping("/assets/{assetId}/history")
    public ApiResponse<PageResponse<LocationHistoryResponse>> getAssetLocationHistory(
            @PathVariable Long assetId,
            @RequestParam(defaultValue = "1") @Min(1) int page,
            @RequestParam(defaultValue = "10") @Min(1) int size) {
        
        try {
            PageResponse<LocationHistoryResponse> result = 
                    locationTrackingService.getAssetLocationHistory(assetId, page, size);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取资产位置历史失败: {}", e.getMessage());
            return ApiResponse.error("获取资产位置历史失败: " + e.getMessage());
        }
    }

    /**
     * 获取位置变更历史列表
     */
    @GetMapping("/history")
    public ApiResponse<PageResponse<LocationHistoryResponse>> getLocationHistory(
            @RequestParam(defaultValue = "1") @Min(1) int page,
            @RequestParam(defaultValue = "10") @Min(1) int size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        
        try {
            PageResponse<LocationHistoryResponse> result = 
                    locationTrackingService.getLocationHistory(page, size, keyword, startTime, endTime);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取位置变更历史失败: {}", e.getMessage());
            return ApiResponse.error("获取位置变更历史失败: " + e.getMessage());
        }
    }

    /**
     * 获取最近的位置变更记录
     */
    @GetMapping("/recent")
    public ApiResponse<List<LocationHistoryResponse>> getRecentLocationChanges(
            @RequestParam(defaultValue = "10") @Min(1) int limit) {
        
        try {
            List<LocationHistoryResponse> result = locationTrackingService.getRecentLocationChanges(limit);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取最近位置变更记录失败: {}", e.getMessage());
            return ApiResponse.error("获取最近位置变更记录失败: " + e.getMessage());
        }
    }

    /**
     * 根据位置查询历史记录
     */
    @GetMapping("/by-location")
    public ApiResponse<List<LocationHistoryResponse>> getLocationHistoryByLocation(
            @RequestParam String location) {
        
        try {
            List<LocationHistoryResponse> result = 
                    locationTrackingService.getLocationHistoryByLocation(location);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("根据位置查询历史记录失败: {}", e.getMessage());
            return ApiResponse.error("根据位置查询历史记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取位置变更统计
     */
    @GetMapping("/statistics")
    public ApiResponse<List<Object>> getLocationChangeStatistics() {
        try {
            List<Object> result = locationTrackingService.getLocationChangeStatistics();
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取位置变更统计失败: {}", e.getMessage());
            return ApiResponse.error("获取位置变更统计失败: " + e.getMessage());
        }
    }

    /**
     * 根据时间范围统计位置变更数量
     */
    @GetMapping("/statistics/date-range")
    public ApiResponse<List<Object>> getLocationChangesByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        
        try {
            List<Object> result = locationTrackingService.getLocationChangesByDateRange(startTime, endTime);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("根据时间范围统计位置变更失败: {}", e.getMessage());
            return ApiResponse.error("根据时间范围统计位置变更失败: " + e.getMessage());
        }
    }
}