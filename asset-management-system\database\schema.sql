-- 创建数据库
CREATE DATABASE IF NOT EXISTS asset_management 
DEFAULT CHARACTER SET utf8mb4 
DEFAULT COLLATE utf8mb4_unicode_ci;

USE asset_management;

-- 创建用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密存储）',
    role ENUM('USER', 'ADMIN') DEFAULT 'USER' COMMENT '用户角色',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE' COMMENT '用户状态',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '电话',
    real_name VARCHAR(50) COMMENT '真实姓名',
    department VARCHAR(100) COMMENT '部门',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除标志',
    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_status (status)
) ENGINE=InnoDB COMMENT='用户表';

-- 创建资产表
CREATE TABLE assets (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    asset_code VARCHAR(100) COMMENT '资产编号',
    serial_number VARCHAR(100) COMMENT '序列号',
    product_model VARCHAR(100) COMMENT '产品型号',
    product_type VARCHAR(100) COMMENT '产品类型',
    brand VARCHAR(100) COMMENT '品牌',
    specification TEXT COMMENT '规格说明',
    purchase_date DATE COMMENT '采购日期',
    purchase_price DECIMAL(10,2) COMMENT '采购价格',
    supplier VARCHAR(200) COMMENT '供应商',
    warranty_period INT COMMENT '保修期（月）',
    status ENUM('PENDING', 'RECEIVED', 'INSTALLED', 'OUTBOUND', 'SCRAPPED') DEFAULT 'PENDING' COMMENT '资产状态',
    current_location VARCHAR(200) COMMENT '当前位置',
    receiver VARCHAR(100) COMMENT '签收人',
    received_at TIMESTAMP NULL COMMENT '入库时间',
    installer VARCHAR(100) COMMENT '安装人员',
    installed_at TIMESTAMP NULL COMMENT '安装时间',
    install_location VARCHAR(200) COMMENT '安装位置',
    outbound_reason VARCHAR(500) COMMENT '出库原因',
    outbound_operator VARCHAR(100) COMMENT '出库操作人',
    outbound_at TIMESTAMP NULL COMMENT '出库时间',
    notes TEXT COMMENT '备注信息',
    user_id BIGINT NOT NULL COMMENT '所属用户ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除标志',
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_id (user_id),
    INDEX idx_asset_code (asset_code),
    INDEX idx_serial_number (serial_number),
    INDEX idx_product_type (product_type),
    INDEX idx_status (status),
    INDEX idx_current_location (current_location),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='资产表';

-- 创建资产历史记录表
CREATE TABLE asset_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    asset_id BIGINT NOT NULL COMMENT '资产ID',
    operation_type ENUM('CREATE', 'UPDATE', 'RECEIVE', 'INSTALL', 'MOVE', 'OUTBOUND', 'NOTE') NOT NULL COMMENT '操作类型',
    old_value JSON COMMENT '变更前的值',
    new_value JSON COMMENT '变更后的值',
    operator VARCHAR(100) COMMENT '操作人员',
    reason VARCHAR(500) COMMENT '操作原因',
    description TEXT COMMENT '操作描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE CASCADE,
    INDEX idx_asset_id (asset_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_created_at (created_at),
    INDEX idx_operator (operator)
) ENGINE=InnoDB COMMENT='资产历史记录表';

-- 创建位置历史表
CREATE TABLE location_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    asset_id BIGINT NOT NULL COMMENT '资产ID',
    from_location VARCHAR(200) COMMENT '原位置',
    to_location VARCHAR(200) COMMENT '新位置',
    move_reason VARCHAR(500) COMMENT '移动原因',
    operator VARCHAR(100) COMMENT '操作人员',
    moved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '移动时间',
    FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE CASCADE,
    INDEX idx_asset_id (asset_id),
    INDEX idx_moved_at (moved_at),
    INDEX idx_operator (operator)
) ENGINE=InnoDB COMMENT='位置历史表';

-- 创建备注表
CREATE TABLE asset_notes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    asset_id BIGINT NOT NULL COMMENT '资产ID',
    content TEXT NOT NULL COMMENT '备注内容',
    operator VARCHAR(100) COMMENT '操作人员',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除标志',
    FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE CASCADE,
    INDEX idx_asset_id (asset_id),
    INDEX idx_created_at (created_at),
    INDEX idx_operator (operator)
) ENGINE=InnoDB COMMENT='资产备注表';

-- 创建系统配置表
CREATE TABLE system_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description VARCHAR(500) COMMENT '配置描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_config_key (config_key)
) ENGINE=InnoDB COMMENT='系统配置表';

-- 插入初始管理员用户（密码：admin123，使用BCrypt加密）
INSERT INTO users (username, password, role, status, real_name, email) VALUES
('admin', '$2a$10$N.kmcuVf2lvqHY/mBNKXoOUt6AJOfs8/pjKQy2ZFa7TO6KtyDgRw2', 'ADMIN', 'ACTIVE', '系统管理员', '<EMAIL>');

-- 插入测试用户（密码：user123）
INSERT INTO users (username, password, role, status, real_name, email, department) VALUES 
('user', '$2a$10$8.7T8wKKKXNKJyY2R3Q8qe8BoT9J.z4.9Y5UFK8VXYxJKGQx5YxnO', 'USER', 'ACTIVE', '测试用户', '<EMAIL>', 'IT部门');

-- 插入一些测试资产数据
INSERT INTO assets (asset_code, serial_number, product_model, product_type, brand, status, user_id) VALUES
('IT-2024-001', 'SN001234567', 'ThinkPad X1 Carbon', '笔记本电脑', '联想', 'PENDING', 2),
('IT-2024-002', 'SN001234568', 'OptiPlex 7090', '台式电脑', '戴尔', 'RECEIVED', 2),
('IT-2024-003', 'SN001234569', 'LaserJet Pro M404n', '激光打印机', 'HP', 'INSTALLED', 2),
('IT-2024-004', 'SN001234570', 'U2722DE', '显示器', '戴尔', 'INSTALLED', 2),
('IT-2024-005', 'SN001234571', 'Surface Pro 9', '平板电脑', '微软', 'PENDING', 2);

-- 插入一些历史记录
INSERT INTO asset_history (asset_id, operation_type, operator, description) VALUES
(1, 'CREATE', 'admin', '创建资产记录'),
(2, 'CREATE', 'admin', '创建资产记录'),
(2, 'RECEIVE', 'user', '确认接收资产'),
(3, 'CREATE', 'admin', '创建资产记录'),
(3, 'RECEIVE', 'user', '确认接收资产'),
(3, 'INSTALL', 'user', '完成资产安装'),
(4, 'CREATE', 'admin', '创建资产记录'),
(4, 'RECEIVE', 'user', '确认接收资产'),
(4, 'INSTALL', 'user', '完成资产安装'),
(5, 'CREATE', 'admin', '创建资产记录');

-- 插入系统配置
INSERT INTO system_config (config_key, config_value, description) VALUES
('asset_code_prefix', 'IT', '资产编号前缀'),
('asset_code_format', 'IT-YYYY-NNN', '资产编号格式'),
('default_warranty_period', '36', '默认保修期（月）'),
('max_upload_size', '50', '最大上传文件大小（MB）'),
('export_batch_size', '1000', '导出批处理大小');

-- 创建视图：资产统计视图
CREATE VIEW asset_statistics AS
SELECT 
    u.username,
    u.real_name,
    COUNT(a.id) as total_assets,
    COUNT(CASE WHEN a.status = 'PENDING' THEN 1 END) as pending_count,
    COUNT(CASE WHEN a.status = 'RECEIVED' THEN 1 END) as received_count,
    COUNT(CASE WHEN a.status = 'INSTALLED' THEN 1 END) as installed_count,
    COUNT(CASE WHEN a.status = 'OUTBOUND' THEN 1 END) as outbound_count,
    COUNT(CASE WHEN a.status = 'SCRAPPED' THEN 1 END) as scrapped_count
FROM users u
LEFT JOIN assets a ON u.id = a.user_id AND a.deleted = 0
WHERE u.deleted = 0
GROUP BY u.id, u.username, u.real_name;