import{_ as Se}from"./_plugin-vue_export-helper-62491c14.js";/* empty css                *//* empty css               *//* empty css                *//* empty css                  */import{ah as De,ai as G,aj as Q,ak as je,s as M,al as Re,t as Z,am as ge,o as ke,an as Te,ao as me,ap as Me,aq as Ue,ar as Fe,F as U,as as Be,at as He,au as Ne,av as We,aw as $e,ax as qe,ay as Ie,az as Pe,aA as Ye,u as Ve,a as Ge,r as $,j as q,v as Qe,c as y,d as I,f as s,g as c,w as l,aB as Ze,E as Je,aC as Ke,a8 as L,Y as Xe,a2 as ae,a3 as re,i as be,z as et,ae as tt,aD as nt,n as at,aE as rt,X as H,aF as it,aa as ye,x as ze,W as we}from"./index-2733c819.js";var ie=null;function st(e){return ie||(ie=(window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){return setTimeout(t,16)}).bind(window)),ie(e)}var se=null;function ot(e){se||(se=(window.cancelAnimationFrame||window.webkitCancelAnimationFrame||window.mozCancelAnimationFrame||function(t){clearTimeout(t)}).bind(window)),se(e)}function ct(e){var t=document.createElement("style");return t.styleSheet?t.styleSheet.cssText=e:t.appendChild(document.createTextNode(e)),(document.querySelector("head")||document.body).appendChild(t),t}function J(e,t){t===void 0&&(t={});var n=document.createElement(e);return Object.keys(t).forEach(function(r){n[r]=t[r]}),n}function Ae(e,t,n){var r=window.getComputedStyle(e,n||null)||{display:"none"};return r[t]}function oe(e){if(!document.documentElement.contains(e))return{detached:!0,rendered:!1};for(var t=e;t!==document;){if(Ae(t,"display")==="none")return{detached:!1,rendered:!1};t=t.parentNode}return{detached:!1,rendered:!0}}var lt='.resize-triggers{visibility:hidden;opacity:0;pointer-events:none}.resize-contract-trigger,.resize-contract-trigger:before,.resize-expand-trigger,.resize-triggers{content:"";position:absolute;top:0;left:0;height:100%;width:100%;overflow:hidden}.resize-contract-trigger,.resize-expand-trigger{background:#eee;overflow:auto}.resize-contract-trigger:before{width:200%;height:200%}',ce=0,X=null;function dt(e,t){e.__resize_mutation_handler__||(e.__resize_mutation_handler__=ft.bind(e));var n=e.__resize_listeners__;if(!n){if(e.__resize_listeners__=[],window.ResizeObserver){var r=e.offsetWidth,o=e.offsetHeight,a=new ResizeObserver(function(){!e.__resize_observer_triggered__&&(e.__resize_observer_triggered__=!0,e.offsetWidth===r&&e.offsetHeight===o)||ee(e)}),u=oe(e),z=u.detached,g=u.rendered;e.__resize_observer_triggered__=z===!1&&g===!1,e.__resize_observer__=a,a.observe(e)}else if(e.attachEvent&&e.addEventListener)e.__resize_legacy_resize_handler__=function(){ee(e)},e.attachEvent("onresize",e.__resize_legacy_resize_handler__),document.addEventListener("DOMSubtreeModified",e.__resize_mutation_handler__);else if(ce||(X=ct(lt)),vt(e),e.__resize_rendered__=oe(e).rendered,window.MutationObserver){var m=new MutationObserver(e.__resize_mutation_handler__);m.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0}),e.__resize_mutation_observer__=m}}e.__resize_listeners__.push(t),ce++}function _t(e,t){var n=e.__resize_listeners__;if(n){if(t&&n.splice(n.indexOf(t),1),!n.length||!t){if(e.detachEvent&&e.removeEventListener){e.detachEvent("onresize",e.__resize_legacy_resize_handler__),document.removeEventListener("DOMSubtreeModified",e.__resize_mutation_handler__);return}e.__resize_observer__?(e.__resize_observer__.unobserve(e),e.__resize_observer__.disconnect(),e.__resize_observer__=null):(e.__resize_mutation_observer__&&(e.__resize_mutation_observer__.disconnect(),e.__resize_mutation_observer__=null),e.removeEventListener("scroll",le),e.removeChild(e.__resize_triggers__.triggers),e.__resize_triggers__=null),e.__resize_listeners__=null}!--ce&&X&&X.parentNode.removeChild(X)}}function ut(e){var t=e.__resize_last__,n=t.width,r=t.height,o=e.offsetWidth,a=e.offsetHeight;return o!==n||a!==r?{width:o,height:a}:null}function ft(){var e=oe(this),t=e.rendered,n=e.detached;t!==this.__resize_rendered__&&(!n&&this.__resize_triggers__&&(de(this),this.addEventListener("scroll",le,!0)),this.__resize_rendered__=t,ee(this))}function le(){var e=this;de(this),this.__resize_raf__&&ot(this.__resize_raf__),this.__resize_raf__=st(function(){var t=ut(e);t&&(e.__resize_last__=t,ee(e))})}function ee(e){!e||!e.__resize_listeners__||e.__resize_listeners__.forEach(function(t){t.call(e,e)})}function vt(e){var t=Ae(e,"position");(!t||t==="static")&&(e.style.position="relative"),e.__resize_old_position__=t,e.__resize_last__={};var n=J("div",{className:"resize-triggers"}),r=J("div",{className:"resize-expand-trigger"}),o=J("div"),a=J("div",{className:"resize-contract-trigger"});r.appendChild(o),n.appendChild(r),n.appendChild(a),e.appendChild(n),e.__resize_triggers__={triggers:n,expand:r,expandChild:o,contract:a},de(e),e.addEventListener("scroll",le,!0),e.__resize_last__={width:e.offsetWidth,height:e.offsetHeight}}function de(e){var t=e.__resize_triggers__,n=t.expand,r=t.expandChild,o=t.contract,a=o.scrollWidth,u=o.scrollHeight,z=n.offsetWidth,g=n.offsetHeight,m=n.scrollWidth,v=n.scrollHeight;o.scrollLeft=a,o.scrollTop=u,r.style.width=z+1+"px",r.style.height=g+1+"px",n.scrollLeft=m,n.scrollTop=v}var x=function(){return x=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},x.apply(this,arguments)};var ht=["getWidth","getHeight","getDom","getOption","resize","dispatchAction","convertToPixel","convertFromPixel","containPixel","getDataURL","getConnectedDataURL","appendData","clear","isDisposed","dispose"];function pt(e){return t=Object.create(null),ht.forEach(function(n){t[n]=function(r){return function(){for(var o=[],a=0;a<arguments.length;a++)o[a]=arguments[a];if(!e.value)throw new Error("ECharts is not initialized yet.");return e.value[r].apply(e.value,o)}}(n)}),t;var t}var gt={autoresize:[Boolean,Object]},mt=/^on[^a-z]/,xe=function(e){return mt.test(e)};function K(e,t){var n=Fe(e)?U(e):e;return n&&typeof n=="object"&&"value"in n?n.value||t:n||t}var bt="ecLoadingOptions",yt={loading:Boolean,loadingOptions:Object},P=null,Le="x-vue-echarts",Ce=[],Y=[];(function(e,t){if(e&&typeof document<"u"){var n,r=t.prepend===!0?"prepend":"append",o=t.singleTag===!0,a=typeof t.container=="string"?document.querySelector(t.container):document.getElementsByTagName("head")[0];if(o){var u=Ce.indexOf(a);u===-1&&(u=Ce.push(a)-1,Y[u]={}),n=Y[u]&&Y[u][r]?Y[u][r]:Y[u][r]=z()}else n=z();e.charCodeAt(0)===65279&&(e=e.substring(1)),n.styleSheet?n.styleSheet.cssText+=e:n.appendChild(document.createTextNode(e))}function z(){var g=document.createElement("style");if(g.setAttribute("type","text/css"),t.attributes)for(var m=Object.keys(t.attributes),v=0;v<m.length;v++)g.setAttribute(m[v],t.attributes[m[v]]);var S=r==="prepend"?"afterbegin":"beforeend";return a.insertAdjacentElement(S,g),g}})(`x-vue-echarts{display:flex;flex-direction:column;width:100%;height:100%;min-width:0}
.vue-echarts-inner{flex-grow:1;min-width:0;width:auto!important;height:auto!important}
`,{});var zt=function(){if(P!=null)return P;if(typeof HTMLElement>"u"||typeof customElements>"u")return P=!1;try{new Function("tag",`class EChartsElement extends HTMLElement {
  __dispose = null;

  disconnectedCallback() {
    if (this.__dispose) {
      this.__dispose();
      this.__dispose = null;
    }
  }
}

if (customElements.get(tag) == null) {
  customElements.define(tag, EChartsElement);
}
`)(Le)}catch{return P=!1}return P=!0}(),wt="ecTheme",xt="ecInitOptions",Ct="ecUpdateOptions",Ee=/(^&?~?!?)native:/,Oe=De({name:"echarts",props:x(x({option:Object,theme:{type:[Object,String]},initOptions:Object,updateOptions:Object,group:String,manualUpdate:Boolean},gt),yt),emits:{},inheritAttrs:!1,setup:function(e,t){var n=t.attrs,r=G(),o=G(),a=G(),u=G(),z=Q(wt,null),g=Q(xt,null),m=Q(Ct,null),v=je(e),S=v.autoresize,te=v.manualUpdate,ne=v.loading,O=v.loadingOptions,w=M(function(){return u.value||e.option||null}),C=M(function(){return e.theme||K(z,{})}),V=M(function(){return e.initOptions||K(g,{})}),E=M(function(){return e.updateOptions||K(m,{})}),D=M(function(){return function(d){var i={};for(var f in d)xe(f)||(i[f]=d[f]);return i}(n)}),j={},R=Re().proxy.$listeners,k={};function F(d){if(o.value){var i=a.value=Me(o.value,C.value,V.value);e.group&&(i.group=e.group),Object.keys(k).forEach(function(b){var h=k[b];if(h){var p=b.toLowerCase();p.charAt(0)==="~"&&(p=p.substring(1),h.__once__=!0);var N=i;if(p.indexOf("zr:")===0&&(N=i.getZr(),p=p.substring(3)),h.__once__){delete h.__once__;var B=h;h=function(){for(var W=[],A=0;A<arguments.length;A++)W[A]=arguments[A];B.apply(void 0,W),N.off(p,h)}}N.on(p,h)}}),S.value?Ue(function(){i&&!i.isDisposed()&&i.resize(),f()}):f()}function f(){var b=d||w.value;b&&i.setOption(b,E.value)}}function _(){a.value&&(a.value.dispose(),a.value=void 0)}R?Object.keys(R).forEach(function(d){Ee.test(d)?j[d.replace(Ee,"$1")]=R[d]:k[d]=R[d]}):Object.keys(n).filter(function(d){return xe(d)}).forEach(function(d){var i=d.charAt(2).toLowerCase()+d.slice(3);if(i.indexOf("native:")!==0)i.substring(i.length-4)==="Once"&&(i="~".concat(i.substring(0,i.length-4))),k[i]=n[d];else{var f="on".concat(i.charAt(7).toUpperCase()).concat(i.slice(8));j[f]=n[d]}});var T=null;Z(te,function(d){typeof T=="function"&&(T(),T=null),d||(T=Z(function(){return e.option},function(i,f){i&&(a.value?a.value.setOption(i,x({notMerge:i!==f},E.value)):F())},{deep:!0}))},{immediate:!0}),Z([C,V],function(){_(),F()},{deep:!0}),ge(function(){e.group&&a.value&&(a.value.group=e.group)});var _e=pt(a);return function(d,i,f){var b=Q(bt,{}),h=M(function(){return x(x({},K(b,{})),f==null?void 0:f.value)});ge(function(){var p=d.value;p&&(i.value?p.showLoading(h.value):p.hideLoading())})}(a,ne,O),function(d,i,f){var b=null;Z([f,d,i],function(h,p,N){var B=h[0],W=h[1],A=h[2];if(B&&W&&A){var ue=A===!0?{}:A,fe=ue.throttle,ve=fe===void 0?100:fe,he=ue.onResize,pe=function(){W.resize(),he==null||he()};b=ve?Be(pe,ve):pe,dt(B,b)}N(function(){B&&b&&_t(B,b)})})}(a,S,o),ke(function(){F()}),Te(function(){zt&&r.value?r.value.__dispose=_:_()}),x({chart:a,root:r,inner:o,setOption:function(d,i){e.manualUpdate&&(u.value=d),a.value?a.value.setOption(d,i||{}):F(d)},nonEventAttrs:D,nativeListeners:j},_e)},render:function(){var e=x(x({},this.nonEventAttrs),this.nativeListeners);return e.ref="root",e.class=e.class?["echarts"].concat(e.class):"echarts",me(Le,e,[me("div",{ref:"inner",class:"vue-echarts-inner"})])}});const Et={class:"dashboard"},Ot={class:"welcome-card animate__animated animate__fadeInDown"},kt={class:"welcome-info"},At={class:"welcome-text"},Lt={class:"welcome-avatar"},St={class:"stats-grid animate__animated animate__fadeInUp animate__delay-1s"},Dt={class:"stat-content"},jt={class:"stat-info"},Rt={class:"stat-value"},Tt={class:"stat-label"},Mt={class:"charts-section animate__animated animate__fadeInUp animate__delay-2s"},Ut={class:"card-header"},Ft={class:"chart-container"},Bt={class:"card-header"},Ht={class:"chart-container"},Nt={class:"bottom-section animate__animated animate__fadeInUp animate__delay-3s"},Wt={class:"quick-actions"},$t={class:"card-header"},qt={class:"activity-list"},It={class:"activity-content"},Pt={class:"activity-text"},Yt={class:"activity-time"},Vt={__name:"index",setup(e){He([Ne,We,$e,qe,Ie,Pe,Ye]);const t=Ve(),n=Ge(),r=M(()=>Ze().format("YYYY年MM月DD日")),o=$([{label:"总资产",value:"0",icon:"Box",color:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",trend:0,route:"/assets"},{label:"已入库",value:"0",icon:"Checked",color:"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",trend:0,route:"/operations/inbound"},{label:"已安装",value:"0",icon:"Tools",color:"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",trend:0,route:"/operations/install"},{label:"已出库",value:"0",icon:"Upload",color:"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",trend:0,route:"/operations/outbound"}]),a=$([{name:"新增资产",type:"primary",icon:"Plus",action:"addAsset"},{name:"批量导入",type:"success",icon:"Upload",action:"importAssets"},{name:"导出数据",type:"warning",icon:"Download",action:"exportData"},{name:"生成报表",type:"info",icon:"Document",action:"generateReport"}]),u=$([]),z=$({tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left"},series:[{name:"资产状态",type:"pie",radius:["50%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"18",fontWeight:"bold"}},labelLine:{show:!1},data:[]}]}),g=$({tooltip:{trigger:"axis"},legend:{data:["入库","出库","安装"]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:["1月","2月","3月","4月","5月","6月"]},yAxis:{type:"value"},series:[{name:"入库",type:"line",smooth:!0,data:[12,18,25,32,28,35]},{name:"出库",type:"line",smooth:!0,data:[8,15,20,28,22,30]},{name:"安装",type:"line",smooth:!0,data:[5,12,18,25,20,28]}]}),m=O=>{O&&t.push(O)},v=O=>{switch(O){case"addAsset":t.push("/assets/create");break;case"importAssets":t.push("/import-export");break;case"exportData":q.info("导出功能开发中...");break;case"generateReport":t.push("/statistics");break}},S=()=>{q.success("图表已刷新")},te=()=>{q.info("活动日志功能开发中...")};ke(()=>{ne()});const ne=async()=>{try{q.success("仪表板数据加载完成")}catch{q.error("数据加载失败")}};return(O,w)=>{const C=et,V=tt,E=Je,D=nt,j=Ke,R=Qe("Refresh"),k=at,F=rt;return y(),I("div",Et,[s("div",Ot,[c(E,{class:"welcome-content",shadow:"hover"},{default:l(()=>[s("div",kt,[s("div",At,[s("h2",null,"欢迎回来，"+L(U(n).userName)+"！",1),s("p",null,"今天是 "+L(r.value)+"，祝您工作愉快！",1)]),s("div",Lt,[c(V,{size:80,class:"user-avatar"},{default:l(()=>[c(C,{size:"40"},{default:l(()=>[c(U(Xe))]),_:1})]),_:1})])])]),_:1})]),s("div",St,[c(j,{gutter:24},{default:l(()=>[(y(!0),I(ae,null,re(o.value,(_,T)=>(y(),H(D,{xs:12,sm:6,key:T},{default:l(()=>[c(E,{class:"stat-card clickable",shadow:"hover",onClick:_e=>m(_.route)},{default:l(()=>[s("div",Dt,[s("div",{class:"stat-icon",style:it({background:_.color})},[c(C,{size:"24"},{default:l(()=>[(y(),H(ye(_.icon)))]),_:2},1024)],4),s("div",jt,[s("h3",Rt,L(_.value),1),s("p",Tt,L(_.label),1)])]),s("div",{class:ze(["stat-trend",_.trend>0?"positive":"negative"])},[c(C,null,{default:l(()=>[_.trend>0?(y(),H(U(we),{key:0})):(y(),H(U(we),{key:1}))]),_:2},1024),s("span",null,L(Math.abs(_.trend))+"%",1)],2)]),_:2},1032,["onClick"])]),_:2},1024))),128))]),_:1})]),s("div",Mt,[c(j,{gutter:24},{default:l(()=>[c(D,{xs:24,lg:12},{default:l(()=>[c(E,{title:"资产状态分布",shadow:"hover",class:"chart-card"},{header:l(()=>[s("div",Ut,[w[0]||(w[0]=s("span",null,"资产状态分布",-1)),c(k,{link:"",onClick:S},{default:l(()=>[c(C,null,{default:l(()=>[c(R)]),_:1})]),_:1})])]),default:l(()=>[s("div",Ft,[c(U(Oe),{option:z.value,class:"chart"},null,8,["option"])])]),_:1})]),_:1}),c(D,{xs:24,lg:12},{default:l(()=>[c(E,{title:"月度变化趋势",shadow:"hover",class:"chart-card"},{header:l(()=>[s("div",Bt,[w[1]||(w[1]=s("span",null,"月度变化趋势",-1)),c(k,{link:"",onClick:S},{default:l(()=>[c(C,null,{default:l(()=>[c(R)]),_:1})]),_:1})])]),default:l(()=>[s("div",Ht,[c(U(Oe),{option:g.value,class:"chart"},null,8,["option"])])]),_:1})]),_:1})]),_:1})]),s("div",Nt,[c(j,{gutter:24},{default:l(()=>[c(D,{xs:24,lg:8},{default:l(()=>[c(E,{title:"快速操作",shadow:"hover",class:"quick-actions-card"},{default:l(()=>[s("div",Wt,[(y(!0),I(ae,null,re(a.value,_=>(y(),H(k,{key:_.name,type:_.type,icon:_.icon,onClick:T=>v(_.action),class:"action-btn",size:"large"},{default:l(()=>[be(L(_.name),1)]),_:2},1032,["type","icon","onClick"]))),128))])]),_:1})]),_:1}),c(D,{xs:24,lg:16},{default:l(()=>[c(E,{title:"最近活动",shadow:"hover",class:"activity-card"},{header:l(()=>[s("div",$t,[w[3]||(w[3]=s("span",null,"最近活动",-1)),c(F,{type:"primary",onClick:te},{default:l(()=>w[2]||(w[2]=[be("查看全部",-1)])),_:1,__:[2]})])]),default:l(()=>[s("div",qt,[(y(!0),I(ae,null,re(u.value,_=>(y(),I("div",{key:_.id,class:"activity-item"},[s("div",{class:ze(["activity-icon",_.type])},[c(C,null,{default:l(()=>[(y(),H(ye(_.icon)))]),_:2},1024)],2),s("div",It,[s("p",Pt,L(_.description),1),s("span",Yt,L(_.time),1)])]))),128))])]),_:1})]),_:1})]),_:1})])])}}},en=Se(Vt,[["__scopeId","data-v-3d206db3"]]);export{en as default};
