#!/usr/bin/env node
/**
 * 前端静态文件服务器 - 一键运行脚本
 * 类似后端JAR文件的运行方式
 * 
 * 使用方法:
 *   node serve.js
 *   或
 *   npm run serve
 * 
 * 作者: 全栈设计大师
 * 版本: 1.0.0
 */

const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

// 配置
const PORT = process.env.PORT || 3000;
const DIST_DIR = path.join(__dirname, 'dist');

// MIME类型映射
const mimeTypes = {
  '.html': 'text/html',
  '.js': 'application/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.gif': 'image/gif',
  '.ico': 'image/x-icon',
  '.svg': 'image/svg+xml',
  '.woff': 'font/woff',
  '.woff2': 'font/woff2',
  '.ttf': 'font/ttf',
  '.eot': 'application/vnd.ms-fontobject'
};

// 获取文件MIME类型
function getMimeType(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  return mimeTypes[ext] || 'application/octet-stream';
}

// 检查dist目录是否存在
function checkDistDirectory() {
  if (!fs.existsSync(DIST_DIR)) {
    console.error('❌ 错误: dist目录不存在！');
    console.log('💡 请先运行构建命令:');
    console.log('   npm run build');
    process.exit(1);
  }
}

// 创建HTTP服务器
function createServer() {
  return http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url);
    let pathname = parsedUrl.pathname;
    
    // 处理根路径
    if (pathname === '/') {
      pathname = '/index.html';
    }
    
    // 构建文件路径
    const filePath = path.join(DIST_DIR, pathname);
    
    // 检查文件是否存在
    fs.access(filePath, fs.constants.F_OK, (err) => {
      if (err) {
        // 文件不存在，对于SPA应用返回index.html
        if (pathname.startsWith('/api/')) {
          // API请求返回404
          res.writeHead(404, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ error: 'API endpoint not found' }));
        } else {
          // 前端路由，返回index.html
          const indexPath = path.join(DIST_DIR, 'index.html');
          fs.readFile(indexPath, (err, data) => {
            if (err) {
              res.writeHead(500);
              res.end('Internal Server Error');
              return;
            }
            res.writeHead(200, { 'Content-Type': 'text/html' });
            res.end(data);
          });
        }
        return;
      }
      
      // 读取并返回文件
      fs.readFile(filePath, (err, data) => {
        if (err) {
          res.writeHead(500);
          res.end('Internal Server Error');
          return;
        }
        
        const mimeType = getMimeType(filePath);
        res.writeHead(200, { 
          'Content-Type': mimeType,
          'Cache-Control': 'public, max-age=31536000' // 缓存一年
        });
        res.end(data);
      });
    });
  });
}

// 启动服务器
function startServer() {
  checkDistDirectory();
  
  const server = createServer();
  
  server.listen(PORT, () => {
    console.log('🚀 前端服务器启动成功！');
    console.log('=' * 50);
    console.log(`📱 访问地址: http://localhost:${PORT}`);
    console.log(`📁 静态文件目录: ${DIST_DIR}`);
    console.log(`⏰ 启动时间: ${new Date().toLocaleString()}`);
    console.log('=' * 50);
    console.log('💡 提示: 按 Ctrl+C 停止服务器');
  });
  
  // 优雅关闭
  process.on('SIGINT', () => {
    console.log('\n🛑 正在关闭服务器...');
    server.close(() => {
      console.log('✅ 服务器已关闭');
      process.exit(0);
    });
  });
}

// 启动
if (require.main === module) {
  startServer();
}

module.exports = { startServer };
