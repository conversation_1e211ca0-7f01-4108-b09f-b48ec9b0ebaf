# 🔧 Git仓库配置指南

## ✅ **已完成的配置**

### **1. 远程仓库配置**
```bash
远程仓库地址: git@81.69.17.39:adminxp/ZC.git
分支: master
状态: 已配置并推送初始代码
```

### **2. 文件结构**
```
asset-management-system/
├── .git/                    # Git仓库配置
├── .gitignore              # 忽略文件配置
├── backend/                # 后端Java项目
├── frontend/               # 前端Vue项目
├── database/               # 数据库脚本
├── docs/                   # 项目文档
└── super_fast.py          # 快速启动脚本
```

### **3. .gitignore 配置**
已配置忽略以下文件/目录：
- 编译输出：`target/`, `dist/`, `build/`
- 依赖目录：`node_modules/`, `.mvn/`
- IDE文件：`.idea/`, `.vscode/`
- 系统文件：`.DS_Store`, `Thumbs.db`
- 环境配置：`.env*`
- 缓存文件：`.cache/`, `*.cache`

## 🚀 **常用Git命令**

### **日常开发流程**
```bash
# 1. 查看状态
git status

# 2. 添加文件
git add .                    # 添加所有文件
git add filename            # 添加指定文件

# 3. 提交代码
git commit -m "提交说明"

# 4. 推送到远程
git push origin master

# 5. 拉取最新代码
git pull origin master
```

### **分支管理**
```bash
# 创建新分支
git checkout -b feature/new-feature

# 切换分支
git checkout master
git checkout feature/new-feature

# 查看分支
git branch -a

# 合并分支
git checkout master
git merge feature/new-feature

# 删除分支
git branch -d feature/new-feature
```

### **查看历史**
```bash
# 查看提交历史
git log --oneline

# 查看文件变更
git diff

# 查看远程仓库
git remote -v
```

## 📋 **开发建议**

### **提交规范**
```bash
# 功能开发
git commit -m "feat: 添加用户管理功能"

# 问题修复
git commit -m "fix: 修复登录验证问题"

# 文档更新
git commit -m "docs: 更新API文档"

# 代码重构
git commit -m "refactor: 重构资产服务层"

# 性能优化
git commit -m "perf: 优化数据库查询性能"
```

### **分支策略**
- `master`: 主分支，稳定版本
- `develop`: 开发分支，集成新功能
- `feature/*`: 功能分支，开发新功能
- `hotfix/*`: 热修复分支，紧急修复

### **协作流程**
1. 从master创建功能分支
2. 在功能分支上开发
3. 提交并推送功能分支
4. 创建Pull Request
5. 代码审查后合并到master

## 🔐 **SSH密钥配置**

如果遇到权限问题，需要配置SSH密钥：

```bash
# 1. 生成SSH密钥
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# 2. 查看公钥
cat ~/.ssh/id_rsa.pub

# 3. 将公钥添加到Git服务器
# 复制公钥内容到服务器的SSH Keys设置中

# 4. 测试连接
ssh -T git@81.69.17.39
```

## 🛠️ **故障排除**

### **常见问题**

1. **推送被拒绝**
```bash
git pull origin master --rebase
git push origin master
```

2. **合并冲突**
```bash
# 手动解决冲突后
git add .
git commit -m "resolve conflicts"
```

3. **撤销提交**
```bash
# 撤销最后一次提交（保留文件修改）
git reset --soft HEAD~1

# 撤销最后一次提交（丢弃文件修改）
git reset --hard HEAD~1
```

4. **查看远程仓库状态**
```bash
git remote show origin
```

## 📞 **技术支持**

- 仓库地址: `git@81.69.17.39:adminxp/ZC.git`
- 项目名称: 资产管理系统 (Asset Management System)
- 初始提交: 已完成，包含完整的前后端代码

---

**配置完成时间**: 2025-07-27  
**初始提交**: 100个文件，22212行代码  
**状态**: ✅ 已成功配置并推送到远程仓库
