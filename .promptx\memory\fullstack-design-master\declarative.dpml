<?xml version="1.0" encoding="UTF-8"?>
<memory>
  <item id="mem_1753590586049_53qg98yvh" time="2025/07/27 12:29">
    <content>
      用户项目遇到Element Plus图标组件引用问题：Plus、Search、Refresh图标在render时未定义，需要修复图标导入和注册机制
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753591055828_9g9t9igkb" time="2025/07/27 12:37">
    <content>
      用户在导入导出页面遇到新问题：handleUploadError方法未定义，FolderOpened和Refresh图标未导入
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753591293952_muem3649g" time="2025/07/27 12:41">
    <content>
      用户反馈页面刷新后会跳转到首页，需要修复路由保持功能，确保刷新后停留在当前页面
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753591464102_jnvp7n8t7" time="2025/07/27 12:44">
    <content>
      库存统计页面SQL语法错误：MySQL不支持在SELECT子句中使用maxValue等别名，需要修复后端AssetRepository的SQL查询语句
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753591842502_5ofrfmfkv" time="2025/07/27 12:50">
    <content>
      库存统计SQL错误仍然存在，需要检查是否有其他Repository方法或者编译缓存问题导致修复未生效
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753593271504_bunmndnz2" time="2025/07/27 13:14">
    <content>
      SQL语法错误持续存在，可能是MySQL版本兼容性问题，需要采用更基础的SQL语法，避免复杂的列别名和聚合函数
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753594179055_pqkrk8syk" time="2025/07/27 13:29">
    <content>
      用户要求清理项目，移除所有模拟数据和无用文件，优化项目结构，使其更加专业和干净
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753618063755_dkdo6ip0h" time="2025/07/27 20:07">
    <content>
      修复了库存统计SQL语法错误：将IFNULL替换为COALESCE函数解决MySQL兼容性问题。同时在侧边栏资产管理菜单中添加了产品类型管理功能，使用Collection图标，并更新了路由匹配逻辑确保菜单正确高亮。
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753618550331_jutsr5wi5" time="2025/07/27 20:15">
    <content>
      彻底修复了库存统计SQL语法错误：在AssetRepository.java中将所有IFNULL函数替换为COALESCE函数，共修复3个查询方法（getAssetValueStatistics、getInventoryStatisticsByLocation、getInventoryStatisticsByType），解决了MySQL版本兼容性问题。
    </content>
    <tags>#其他</tags>
  </item>
</memory>