import{_ as X}from"./_plugin-vue_export-helper-62491c14.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                */import{u as q,r as E,b as A,o as G,c as B,d as J,f as g,g as e,w as a,j as c,E as W,h as Z,F as m,M as S,i,aH as ee,K as te,a6 as ae,aI as le,H as se,X as ne,a8 as w,x as oe,y as k,aB as re,z as ie,k as de,l as ue,aL as ce,aM as pe,n as _e,p as me,aN as fe,aO as ge,aQ as he,aR as ye,aS as we}from"./index-2733c819.js";import{g as Ce,d as be,b as ve,u as Ee}from"./user-993d4a0e.js";const ke={class:"users-container"},xe={class:"action-bar"},Ve={class:"pagination-container"},Te={__name:"index",setup(Ie){const C=q(),b=E(!1),f=E([]),r=A({keyword:"",role:"",status:""}),o=A({page:1,size:10,total:0}),x=E([]),p=()=>{o.page=1,_()},U=()=>{Object.keys(r).forEach(l=>{r[l]=""}),p()},D=l=>{f.value=l},$=()=>{C.push("/users/create")},M=l=>{C.push(`/users/edit/${l.id}`)},N=()=>{C.push("/users/permissions")},L=async l=>{try{await k.confirm(`确定要删除用户 "${l.username}" 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await be(l.id),c.success("删除成功"),_()}catch(t){t!=="cancel"&&(console.error("删除用户失败:",t),c.error("删除失败"))}},j=async()=>{try{await k.confirm(`确定要删除选中的 ${f.value.length} 个用户吗？`,"确认批量删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const l=f.value.map(t=>t.id);await ve(l),c.success("批量删除成功"),f.value=[],_()}catch(l){l!=="cancel"&&c.error("批量删除失败")}},F=async l=>{const t=l.status==="ACTIVE"?"禁用":"启用",n=l.status==="ACTIVE"?"INACTIVE":"ACTIVE";try{await k.confirm(`确定要${t}用户 "${l.username}" 吗？`,`确认${t}`,{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await Ee(l.id,n),l.status=n,c.success(`${t}成功`)}catch(v){v!=="cancel"&&c.error(`${t}失败`)}},H=l=>{o.size=l,_()},Y=l=>{o.page=l,_()},_=async()=>{b.value=!0;try{const l={page:o.page,size:o.size,keyword:r.keyword,role:r.role||void 0,status:r.status||void 0},n=(await Ce(l)).data;x.value=n.records,o.total=n.total,o.page=n.current,o.size=n.size}catch(l){console.error("加载用户列表失败:",l),c.error("加载用户列表失败")}finally{b.value=!1}},K=l=>re(l).format("YYYY-MM-DD HH:mm:ss");return G(()=>{_()}),(l,t)=>{const n=ie,v=de,h=ue,y=ce,V=pe,u=_e,O=me,T=W,d=fe,I=ge,P=he,R=ye,Q=we;return B(),J("div",ke,[t[12]||(t[12]=g("div",{class:"page-header"},[g("h2",null,"用户管理"),g("p",null,"管理系统用户账号和权限")],-1)),e(T,{class:"search-card",shadow:"never"},{default:a(()=>[e(O,{model:r,inline:"",class:"search-form"},{default:a(()=>[e(h,{label:"关键词"},{default:a(()=>[e(v,{modelValue:r.keyword,"onUpdate:modelValue":t[0]||(t[0]=s=>r.keyword=s),placeholder:"用户名、姓名、邮箱...",clearable:"",onClear:p,onKeyup:Z(p,["enter"]),class:"search-input"},{prefix:a(()=>[e(n,null,{default:a(()=>[e(m(S))]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(h,{label:"角色"},{default:a(()=>[e(V,{modelValue:r.role,"onUpdate:modelValue":t[1]||(t[1]=s=>r.role=s),placeholder:"选择角色",clearable:"",onChange:p},{default:a(()=>[e(y,{label:"管理员",value:"ADMIN"}),e(y,{label:"普通用户",value:"USER"})]),_:1},8,["modelValue"])]),_:1}),e(h,{label:"状态"},{default:a(()=>[e(V,{modelValue:r.status,"onUpdate:modelValue":t[2]||(t[2]=s=>r.status=s),placeholder:"选择状态",clearable:"",onChange:p},{default:a(()=>[e(y,{label:"正常",value:"ACTIVE"}),e(y,{label:"禁用",value:"INACTIVE"})]),_:1},8,["modelValue"])]),_:1}),e(h,null,{default:a(()=>[e(u,{type:"primary",onClick:p},{default:a(()=>[e(n,null,{default:a(()=>[e(m(S))]),_:1}),t[5]||(t[5]=i(" 搜索 ",-1))]),_:1,__:[5]}),e(u,{onClick:U},{default:a(()=>[e(n,null,{default:a(()=>[e(m(ee))]),_:1}),t[6]||(t[6]=i(" 重置 ",-1))]),_:1,__:[6]})]),_:1})]),_:1},8,["model"]),g("div",xe,[e(u,{type:"primary",onClick:$},{default:a(()=>[e(n,null,{default:a(()=>[e(m(te))]),_:1}),t[7]||(t[7]=i(" 新增用户 ",-1))]),_:1,__:[7]}),e(u,{type:"info",onClick:N},{default:a(()=>[e(n,null,{default:a(()=>[e(m(ae))]),_:1}),t[8]||(t[8]=i(" 权限管理 ",-1))]),_:1,__:[8]}),e(u,{type:"danger",disabled:f.value.length===0,onClick:j},{default:a(()=>[e(n,null,{default:a(()=>[e(m(le))]),_:1}),t[9]||(t[9]=i(" 批量删除 ",-1))]),_:1,__:[9]},8,["disabled"])])]),_:1}),e(T,null,{default:a(()=>[se((B(),ne(P,{data:x.value,style:{width:"100%"},onSelectionChange:D},{default:a(()=>[e(d,{type:"selection",width:"55"}),e(d,{prop:"username",label:"用户名",width:"120"}),e(d,{prop:"realName",label:"真实姓名",width:"120"}),e(d,{prop:"email",label:"邮箱",width:"200"}),e(d,{prop:"phone",label:"电话",width:"130"}),e(d,{prop:"department",label:"部门",width:"120"}),e(d,{prop:"role",label:"角色",width:"100"},{default:a(s=>[e(I,{type:s.row.role==="ADMIN"?"danger":"primary"},{default:a(()=>[i(w(s.row.role==="ADMIN"?"管理员":"普通用户"),1)]),_:2},1032,["type"])]),_:1}),e(d,{prop:"status",label:"状态",width:"100"},{default:a(s=>[e(I,{type:s.row.status==="ACTIVE"?"success":"info"},{default:a(()=>[i(w(s.row.status==="ACTIVE"?"正常":"禁用"),1)]),_:2},1032,["type"])]),_:1}),e(d,{prop:"createdAt",label:"创建时间",width:"180"},{default:a(s=>[i(w(K(s.row.createdAt)),1)]),_:1}),e(d,{label:"操作",width:"200",fixed:"right"},{default:a(s=>[e(u,{link:"",onClick:z=>M(s.row)},{default:a(()=>t[10]||(t[10]=[i("编辑",-1)])),_:2,__:[10]},1032,["onClick"]),e(u,{link:"",onClick:z=>F(s.row),class:oe(s.row.status==="ACTIVE"?"text-warning":"text-success")},{default:a(()=>[i(w(s.row.status==="ACTIVE"?"禁用":"启用"),1)]),_:2},1032,["onClick","class"]),e(u,{link:"",class:"text-danger",onClick:z=>L(s.row)},{default:a(()=>t[11]||(t[11]=[i(" 删除 ",-1)])),_:2,__:[11]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Q,b.value]]),g("div",Ve,[e(R,{"current-page":o.page,"onUpdate:currentPage":t[3]||(t[3]=s=>o.page=s),"page-size":o.size,"onUpdate:pageSize":t[4]||(t[4]=s=>o.size=s),"page-sizes":[10,20,50,100],total:o.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:H,onCurrentChange:Y},null,8,["current-page","page-size","total"])])]),_:1})])}}},Ye=X(Te,[["__scopeId","data-v-382c5812"]]);export{Ye as default};
