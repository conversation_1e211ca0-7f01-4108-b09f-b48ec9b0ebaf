import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'animate.css'

// ECharts配置
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { Pie<PERSON>hart, LineChart, BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'

import App from './App.vue'
import router from './router'
import { useAuthStore } from './stores/auth'

// 注册ECharts组件
use([
  <PERSON>vas<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Bar<PERSON>hart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

const app = createApp(App)

// 使用 Element Plus
app.use(ElementPlus)

// 注册 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

const pinia = createPinia()
app.use(pinia)
app.use(router)

// 初始化认证状态
const initApp = async () => {
  const authStore = useAuthStore()

  // 如果有token，检查认证状态
  if (authStore.token) {
    await authStore.checkAuth()
  }

  app.mount('#app')
}

initApp()