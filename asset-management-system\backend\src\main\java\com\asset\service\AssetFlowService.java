package com.asset.service;

import com.asset.dto.AssetOperationRequest;
import com.asset.dto.AssetResponse;
import com.asset.entity.Asset;
import com.asset.entity.AssetHistory;
import com.asset.entity.LocationHistory;
import com.asset.exception.BusinessException;
import com.asset.exception.ResourceNotFoundException;
import com.asset.repository.AssetRepository;
import com.asset.repository.AssetHistoryRepository;
import com.asset.repository.LocationHistoryRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

@Slf4j
@Service
public class AssetFlowService {

    @Autowired
    private AssetRepository assetRepository;

    @Autowired
    private AssetHistoryRepository assetHistoryRepository;

    @Autowired
    private LocationHistoryRepository locationHistoryRepository;

    @Autowired
    private AuthService authService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 资产入库
     */
    @Transactional
    public AssetResponse receiveAsset(Long assetId, AssetOperationRequest request) {
        Asset asset = getAssetAndCheckPermission(assetId);
        
        // 验证状态
        if (asset.getStatus() != Asset.AssetStatus.PENDING) {
            throw new BusinessException("只有待处理状态的资产才能进行入库操作");
        }
        
        String currentUsername = authService.getCurrentUser().getUsername();
        String oldValue = assetToJson(asset);
        
        // 更新资产信息
        asset.setReceiver(request.getReceiver() != null ? request.getReceiver() : request.getOperator());
        asset.setReceivedAt(request.getReceivedAt() != null ? request.getReceivedAt() : LocalDateTime.now());
        asset.setStatus(Asset.AssetStatus.RECEIVED);
        asset.setUpdatedAt(LocalDateTime.now());
        
        assetRepository.updateById(asset);
        
        // 记录历史
        recordAssetHistory(assetId, AssetHistory.OperationType.RECEIVE,
                          oldValue, assetToJson(asset), currentUsername,
                          "资产入库：" + (request.getReason() != null ? request.getReason() : "确认接收"));
        
        log.info("用户 {} 对资产 {} 执行入库操作", currentUsername, asset.getAssetCode());
        
        return AssetResponse.fromAsset(asset);
    }

    /**
     * 资产安装
     */
    @Transactional
    public AssetResponse installAsset(Long assetId, AssetOperationRequest request) {
        Asset asset = getAssetAndCheckPermission(assetId);
        
        // 验证状态
        if (asset.getStatus() != Asset.AssetStatus.RECEIVED) {
            throw new BusinessException("只有已入库状态的资产才能进行安装操作");
        }
        
        if (!StringUtils.hasText(request.getInstallLocation())) {
            throw new BusinessException("安装位置不能为空");
        }
        
        String currentUsername = authService.getCurrentUser().getUsername();
        String oldValue = assetToJson(asset);
        String previousLocation = asset.getCurrentLocation();
        
        // 更新资产信息
        asset.setInstaller(request.getInstaller() != null ? request.getInstaller() : request.getOperator());
        asset.setInstalledAt(request.getInstalledAt() != null ? request.getInstalledAt() : LocalDateTime.now());
        asset.setInstallLocation(request.getInstallLocation());
        asset.setCurrentLocation(request.getInstallLocation());
        asset.setStatus(Asset.AssetStatus.INSTALLED);
        asset.setUpdatedAt(LocalDateTime.now());
        
        assetRepository.updateById(asset);
        
        // 记录历史
        recordAssetHistory(assetId, AssetHistory.OperationType.INSTALL,
                          oldValue, assetToJson(asset), currentUsername,
                          "资产安装：" + (request.getReason() != null ? request.getReason() : "完成安装"));
        
        // 记录位置历史
        recordLocationHistory(assetId, previousLocation, request.getInstallLocation(),
                             "安装部署", currentUsername);
        
        log.info("用户 {} 对资产 {} 执行安装操作，安装位置：{}", 
                currentUsername, asset.getAssetCode(), request.getInstallLocation());
        
        return AssetResponse.fromAsset(asset);
    }

    /**
     * 资产位置变更
     */
    @Transactional
    public AssetResponse moveAsset(Long assetId, AssetOperationRequest request) {
        Asset asset = getAssetAndCheckPermission(assetId);
        
        // 验证状态
        if (asset.getStatus() != Asset.AssetStatus.INSTALLED) {
            throw new BusinessException("只有已安装状态的资产才能进行位置变更");
        }
        
        if (!StringUtils.hasText(request.getToLocation())) {
            throw new BusinessException("目标位置不能为空");
        }
        
        String currentUsername = authService.getCurrentUser().getUsername();
        String oldValue = assetToJson(asset);
        String previousLocation = asset.getCurrentLocation();
        
        // 更新资产信息
        asset.setCurrentLocation(request.getToLocation());
        asset.setUpdatedAt(LocalDateTime.now());
        
        assetRepository.updateById(asset);
        
        // 记录历史
        recordAssetHistory(assetId, AssetHistory.OperationType.MOVE,
                          oldValue, assetToJson(asset), currentUsername,
                          "位置变更：" + (request.getMoveReason() != null ? request.getMoveReason() : "位置调整"));
        
        // 记录位置历史
        recordLocationHistory(assetId, previousLocation, request.getToLocation(),
                             request.getMoveReason(), currentUsername);
        
        log.info("用户 {} 对资产 {} 执行位置变更，从 {} 到 {}", 
                currentUsername, asset.getAssetCode(), previousLocation, request.getToLocation());
        
        return AssetResponse.fromAsset(asset);
    }

    /**
     * 资产出库
     */
    @Transactional
    public AssetResponse outboundAsset(Long assetId, AssetOperationRequest request) {
        Asset asset = getAssetAndCheckPermission(assetId);
        
        // 验证状态
        if (asset.getStatus() != Asset.AssetStatus.RECEIVED && 
            asset.getStatus() != Asset.AssetStatus.INSTALLED) {
            throw new BusinessException("只有已入库或已安装状态的资产才能进行出库操作");
        }
        
        if (!StringUtils.hasText(request.getOutboundReason())) {
            throw new BusinessException("出库原因不能为空");
        }
        
        String currentUsername = authService.getCurrentUser().getUsername();
        String oldValue = assetToJson(asset);
        
        // 更新资产信息
        asset.setOutboundReason(request.getOutboundReason());
        asset.setOutboundOperator(request.getOutboundOperator() != null ? 
                                  request.getOutboundOperator() : request.getOperator());
        asset.setOutboundAt(request.getOutboundAt() != null ? request.getOutboundAt() : LocalDateTime.now());
        asset.setStatus(Asset.AssetStatus.OUTBOUND);
        asset.setUpdatedAt(LocalDateTime.now());
        
        assetRepository.updateById(asset);
        
        // 记录历史
        recordAssetHistory(assetId, AssetHistory.OperationType.OUTBOUND,
                          oldValue, assetToJson(asset), currentUsername,
                          "资产出库：" + request.getOutboundReason());
        
        log.info("用户 {} 对资产 {} 执行出库操作，原因：{}", 
                currentUsername, asset.getAssetCode(), request.getOutboundReason());
        
        return AssetResponse.fromAsset(asset);
    }

    /**
     * 重新激活资产（出库后重新入库）
     */
    @Transactional
    public AssetResponse reactivateAsset(Long assetId, AssetOperationRequest request) {
        Asset asset = getAssetAndCheckPermission(assetId);
        
        // 验证状态
        if (asset.getStatus() != Asset.AssetStatus.OUTBOUND) {
            throw new BusinessException("只有已出库状态的资产才能重新激活");
        }
        
        String currentUsername = authService.getCurrentUser().getUsername();
        String oldValue = assetToJson(asset);
        
        // 更新资产信息
        asset.setReceiver(request.getReceiver() != null ? request.getReceiver() : request.getOperator());
        asset.setReceivedAt(LocalDateTime.now());
        asset.setStatus(Asset.AssetStatus.RECEIVED);
        asset.setUpdatedAt(LocalDateTime.now());
        
        // 清空出库信息
        asset.setOutboundReason(null);
        asset.setOutboundOperator(null);
        asset.setOutboundAt(null);
        
        assetRepository.updateById(asset);
        
        // 记录历史
        recordAssetHistory(assetId, AssetHistory.OperationType.RECEIVE,
                          oldValue, assetToJson(asset), currentUsername,
                          "资产重新激活：" + (request.getReason() != null ? request.getReason() : "重新入库"));
        
        log.info("用户 {} 重新激活资产 {}", currentUsername, asset.getAssetCode());
        
        return AssetResponse.fromAsset(asset);
    }

    /**
     * 批量操作
     */
    @Transactional
    public void batchOperation(java.util.List<Long> assetIds, String operationType, 
                              AssetOperationRequest request) {
        String currentUsername = authService.getCurrentUser().getUsername();
        
        for (Long assetId : assetIds) {
            try {
                switch (operationType.toUpperCase()) {
                    case "RECEIVE":
                        receiveAsset(assetId, request);
                        break;
                    case "INSTALL":
                        installAsset(assetId, request);
                        break;
                    case "MOVE":
                        moveAsset(assetId, request);
                        break;
                    case "OUTBOUND":
                        outboundAsset(assetId, request);
                        break;
                    default:
                        throw new BusinessException("不支持的操作类型：" + operationType);
                }
            } catch (Exception e) {
                log.error("批量操作失败，资产ID：{}，错误：{}", assetId, e.getMessage());
                // 继续处理其他资产，不中断整个批量操作
            }
        }
        
        log.info("用户 {} 执行批量操作 {}，处理 {} 个资产", 
                currentUsername, operationType, assetIds.size());
    }

    // 私有方法

    private Asset getAssetAndCheckPermission(Long assetId) {
        Asset asset = assetRepository.selectById(assetId);
        if (asset == null) {
            throw new ResourceNotFoundException("资产不存在");
        }
        
        // 检查权限
        if (!authService.isCurrentUserAdmin() && 
            !asset.getUserId().equals(authService.getCurrentUserId())) {
            throw new BusinessException(403, "无权限操作该资产");
        }
        
        return asset;
    }

    private void recordAssetHistory(Long assetId, AssetHistory.OperationType operationType,
                                   String oldValue, String newValue, String operator, String description) {
        try {
            AssetHistory history = new AssetHistory();
            history.setAssetId(assetId);
            history.setOperationType(operationType);
            history.setOldValue(oldValue);
            history.setNewValue(newValue);
            history.setOperator(operator);
            history.setDescription(description);
            history.setCreatedAt(LocalDateTime.now());
            
            assetHistoryRepository.insert(history);
        } catch (Exception e) {
            log.error("记录资产历史失败: {}", e.getMessage());
        }
    }

    private void recordLocationHistory(Long assetId, String fromLocation, String toLocation,
                                      String reason, String operator) {
        try {
            LocationHistory locationHistory = new LocationHistory();
            locationHistory.setAssetId(assetId);
            locationHistory.setFromLocation(fromLocation);
            locationHistory.setToLocation(toLocation);
            locationHistory.setMoveReason(reason);
            locationHistory.setOperator(operator);
            locationHistory.setMovedAt(LocalDateTime.now());
            
            locationHistoryRepository.insert(locationHistory);
        } catch (Exception e) {
            log.error("记录位置历史失败: {}", e.getMessage());
        }
    }

    private String assetToJson(Asset asset) {
        try {
            return objectMapper.writeValueAsString(asset);
        } catch (Exception e) {
            return asset.toString();
        }
    }
}