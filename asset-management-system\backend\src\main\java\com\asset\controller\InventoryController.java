package com.asset.controller;

import com.asset.dto.ApiResponse;
import com.asset.service.InventoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/inventory")
public class InventoryController extends BaseController {

    @Autowired
    private InventoryService inventoryService;

    /**
     * 获取完整的库存统计报告
     */
    @GetMapping("/report")
    public ApiResponse<Map<String, Object>> getCompleteInventoryReport() {
        try {
            Map<String, Object> report = inventoryService.getCompleteInventoryReport();
            return ApiResponse.success(report);
        } catch (Exception e) {
            log.error("获取库存报告失败: {}", e.getMessage());
            return ApiResponse.error("获取库存报告失败: " + e.getMessage());
        }
    }

    /**
     * 获取库存盘点数据
     */
    @GetMapping("/audit")
    public ApiResponse<Map<String, Object>> getInventoryAuditData() {
        try {
            Map<String, Object> auditData = inventoryService.getInventoryAuditData();
            return ApiResponse.success(auditData);
        } catch (Exception e) {
            log.error("获取库存盘点数据失败: {}", e.getMessage());
            return ApiResponse.error("获取库存盘点数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取库存变化趋势
     */
    @GetMapping("/trends")
    public ApiResponse<Map<String, Object>> getInventoryTrends(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            Map<String, Object> trends = inventoryService.getInventoryTrends(startTime, endTime);
            return ApiResponse.success(trends);
        } catch (Exception e) {
            log.error("获取库存趋势失败: {}", e.getMessage());
            return ApiResponse.error("获取库存趋势失败: " + e.getMessage());
        }
    }

    /**
     * 获取库存异常报告
     */
    @GetMapping("/anomalies")
    public ApiResponse<Map<String, Object>> getInventoryAnomalies() {
        try {
            Map<String, Object> anomalies = inventoryService.getInventoryAnomalies();
            return ApiResponse.success(anomalies);
        } catch (Exception e) {
            log.error("获取库存异常报告失败: {}", e.getMessage());
            return ApiResponse.error("获取库存异常报告失败: " + e.getMessage());
        }
    }

    /**
     * 获取库存摘要
     */
    @GetMapping("/summary")
    public ApiResponse<Map<String, Object>> getInventorySummary() {
        try {
            Map<String, Object> summary = inventoryService.getInventorySummary();
            return ApiResponse.success(summary);
        } catch (Exception e) {
            log.error("获取库存摘要失败: {}", e.getMessage());
            return ApiResponse.error("获取库存摘要失败: " + e.getMessage());
        }
    }
}