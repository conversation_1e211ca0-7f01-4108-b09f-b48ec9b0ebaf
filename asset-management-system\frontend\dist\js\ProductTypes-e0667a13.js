import{_ as K}from"./_plugin-vue_export-helper-62491c14.js";/* empty css                   *//* empty css                   *//* empty css                 *//* empty css                *//* empty css                 */import{r as c,b as L,o as j,c as p,d as y,f as l,g as o,w as n,F as g,H as q,X as E,j as f,n as A,E as G,aV as H,aS as X,i as m,K as J,a2 as O,a3 as Q,$ as h,a8 as C,b6 as W,k as Y,l as Z,p as ee,G as te,aK as se,aI as ae,y as oe,z as ne}from"./index-2733c819.js";const le={class:"product-types"},ie={class:"page-header"},de={class:"header-right"},re={class:"types-grid"},ue={class:"type-content"},ce={class:"type-icon"},pe={class:"type-info"},me={key:0},_e={class:"type-count"},fe={class:"type-actions"},ve={__name:"ProductTypes",setup(ye){const v=c(!1),x=c(!1),r=c(!1),u=c(!1),_=c(),i=c([{id:1,name:"计算机设备",description:"台式机、笔记本电脑等",assetCount:15},{id:2,name:"网络设备",description:"路由器、交换机、防火墙等",assetCount:8},{id:3,name:"办公设备",description:"打印机、复印机、传真机等",assetCount:12},{id:4,name:"移动设备",description:"手机、平板电脑等",assetCount:6},{id:5,name:"服务器设备",description:"物理服务器、存储设备等",assetCount:4}]),s=L({name:"",description:""}),T={name:[{required:!0,message:"请输入产品类型名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}]},k=()=>{u.value=!1,r.value=!0,b()},B=t=>{u.value=!0,s.name=t.name,s.description=t.description||"",s.id=t.id,r.value=!0},D=t=>{oe.confirm(`确定要删除产品类型"${t.name}"吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{const e=i.value.findIndex(d=>d.id===t.id);e>-1&&i.value.splice(e,1),f.success("删除成功")}).catch(()=>{})},I=async()=>{if(_.value)try{if(await _.value.validate(),x.value=!0,await new Promise(t=>setTimeout(t,1e3)),u.value){const t=i.value.findIndex(e=>e.id===s.id);t>-1&&(i.value[t]={...i.value[t],name:s.name,description:s.description}),f.success("更新成功")}else{const t={id:Date.now(),name:s.name,description:s.description,assetCount:0};i.value.push(t),f.success("创建成功")}r.value=!1}catch(t){if(t.errors)return;f.error(u.value?"更新失败":"创建失败")}finally{x.value=!1}},b=()=>{_.value&&_.value.resetFields(),s.name="",s.description="",s.id=null},F=async()=>{v.value=!0;try{await new Promise(t=>setTimeout(t,500))}catch{f.error("加载数据失败")}finally{v.value=!1}};return j(()=>{F()}),(t,e)=>{const d=A,P=ne,$=W,N=G,w=Y,V=Z,M=ee,R=H,S=X;return p(),y("div",le,[l("div",ie,[e[5]||(e[5]=l("div",{class:"header-left"},[l("h2",null,"产品类型管理"),l("p",null,"管理系统中的产品类型，支持添加、编辑和删除")],-1)),l("div",de,[o(d,{type:"primary",onClick:k,icon:g(J)},{default:n(()=>e[4]||(e[4]=[m(" 新增类型 ",-1)])),_:1,__:[4]},8,["icon"])])]),q((p(),E(N,{class:"types-card"},{default:n(()=>[l("div",re,[(p(!0),y(O,null,Q(i.value,(a,U)=>(p(),y("div",{key:a.id||U,class:"type-item"},[l("div",ue,[l("div",ce,[o(P,{size:"24"},{default:n(()=>[o(g(te))]),_:1})]),l("div",pe,[l("h4",null,C(a.name),1),a.description?(p(),y("p",me,C(a.description),1)):h("",!0),l("span",_e,C(a.assetCount||0)+" 个资产",1)])]),l("div",fe,[o(d,{link:"",type:"primary",onClick:z=>B(a),icon:g(se)},{default:n(()=>e[6]||(e[6]=[m(" 编辑 ",-1)])),_:2,__:[6]},1032,["onClick","icon"]),o(d,{link:"",type:"danger",onClick:z=>D(a),icon:g(ae),disabled:a.assetCount>0},{default:n(()=>e[7]||(e[7]=[m(" 删除 ",-1)])),_:2,__:[7]},1032,["onClick","icon","disabled"])])]))),128))]),!v.value&&i.value.length===0?(p(),E($,{key:0,description:"暂无产品类型"},{default:n(()=>[o(d,{type:"primary",onClick:k},{default:n(()=>e[8]||(e[8]=[m("添加第一个类型",-1)])),_:1,__:[8]})]),_:1})):h("",!0)]),_:1})),[[S,v.value]]),o(R,{modelValue:r.value,"onUpdate:modelValue":e[3]||(e[3]=a=>r.value=a),title:u.value?"编辑产品类型":"新增产品类型",width:"500px",onClose:b},{footer:n(()=>[o(d,{onClick:e[2]||(e[2]=a=>r.value=!1)},{default:n(()=>e[9]||(e[9]=[m("取消",-1)])),_:1,__:[9]}),o(d,{type:"primary",onClick:I,loading:x.value},{default:n(()=>[m(C(u.value?"更新":"创建"),1)]),_:1},8,["loading"])]),default:n(()=>[o(M,{ref_key:"formRef",ref:_,model:s,rules:T,"label-width":"80px"},{default:n(()=>[o(V,{label:"类型名称",prop:"name"},{default:n(()=>[o(w,{modelValue:s.name,"onUpdate:modelValue":e[0]||(e[0]=a=>s.name=a),placeholder:"请输入产品类型名称",clearable:""},null,8,["modelValue"])]),_:1}),o(V,{label:"描述",prop:"description"},{default:n(()=>[o(w,{modelValue:s.description,"onUpdate:modelValue":e[1]||(e[1]=a=>s.description=a),type:"textarea",rows:3,placeholder:"请输入类型描述（可选）",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},Ee=K(ve,[["__scopeId","data-v-00b19190"]]);export{Ee as default};
