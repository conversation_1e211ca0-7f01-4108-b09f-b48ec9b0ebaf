#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资产管理系统 - 超级快速启动脚本
完全模拟手动操作，最快启动速度

使用方法:
  python super_fast.py

作者: 全栈设计大师
版本: 1.0.0
"""

import os
import sys
import subprocess
import time
import webbrowser
from pathlib import Path

def log(message, emoji="ℹ️"):
    """简单日志输出"""
    from datetime import datetime
    timestamp = datetime.now().strftime('%H:%M:%S')
    print(f"[{timestamp}] {emoji} {message}")

def compile_backend(backend_dir, maven_cmd):
    """编译后端项目"""
    log("开始编译后端项目...", "🔨")

    try:
        # 执行编译命令
        result = subprocess.run(
            [maven_cmd, "compile"],
            cwd=backend_dir,
            capture_output=True,
            text=True,
            timeout=120  # 2分钟超时
        )

        if result.returncode == 0:
            log("后端编译成功", "✅")
            return True
        else:
            log("后端编译失败", "❌")
            print("编译错误信息:")
            # 显示关键错误信息
            error_lines = result.stdout.split('\n') + result.stderr.split('\n')
            for line in error_lines:
                if "ERROR" in line or "COMPILATION ERROR" in line:
                    print(f"  {line}")
            return False

    except subprocess.TimeoutExpired:
        log("编译超时（超过2分钟）", "❌")
        return False
    except Exception as e:
        log(f"编译过程异常: {str(e)}", "❌")
        return False

def main():
    """主函数 - 超级快速启动"""
    project_root = Path(__file__).parent.absolute()
    backend_dir = project_root / "backend"
    frontend_dir = project_root / "frontend"

    # 自定义工具路径
    maven_cmd = r"D:\cursor\apache-maven-3.9.9\bin\mvn.cmd"
    npm_cmd = r"D:\cursor\nodejs\npm.cmd"

    print("🚀 超级快速启动 - 编译并启动")
    print("=" * 40)

    # 步骤1: 编译后端
    if not compile_backend(backend_dir, maven_cmd):
        print("\n❌ 后端编译失败，无法启动服务")
        return False
    
    # 步骤2：启动服务（编译成功后）
    log("编译成功，开始启动服务...", "🚀")

    if os.name == 'nt':  # Windows
        log("启动后端服务...", "🔧")
        # 在新的cmd窗口中启动后端，默认最小化
        subprocess.Popen(
            f'start "资产管理系统-后端" /min cmd /k "title 后端服务 && echo 后端服务启动中... && cd /d {backend_dir} && {maven_cmd} spring-boot:run"',
            shell=True
        )

        # 等待1秒再启动前端，避免资源竞争
        time.sleep(1)

        log("启动前端服务...", "🎨")
        # 在新的cmd窗口中启动前端，默认最小化
        subprocess.Popen(
            f'start "资产管理系统-前端" /min cmd /k "title 前端服务 && echo 前端服务启动中... && cd /d {frontend_dir} && {npm_cmd} run dev"',
            shell=True
        )
    else:  # Linux/Mac
        log("启动后端服务...", "🔧")
        subprocess.Popen(
            f'gnome-terminal --title="后端服务" -- bash -c "echo 后端服务启动中... && cd {backend_dir} && {maven_cmd} spring-boot:run; exec bash"',
            shell=True
        )

        time.sleep(1)

        log("启动前端服务...", "🎨")
        subprocess.Popen(
            f'gnome-terminal --title="前端服务" -- bash -c "echo 前端服务启动中... && cd {frontend_dir} && {npm_cmd} run dev; exec bash"',
            shell=True
        )
    
    log("服务启动命令已执行", "✅")

    print("\n🎉 编译和启动完成！")
    print("\n📱 访问地址:")
    print("   🏠 系统首页: http://localhost:3000")
    print("   📊 库存统计: http://localhost:3000/statistics")
    print("   👥 用户管理: http://localhost:3000/users")
    print("   🔧 后端API: http://localhost:8080/api")

    print("\n💡 重要提示:")
    print("   ✅ 后端已编译成功")
    print("   🪟 后端和前端在独立窗口中运行（已最小化）")
    print("   ⏱️ 后端启动需要30-60秒")
    print("   ⚡ 前端启动需要10-20秒")
    print("   👀 可从任务栏查看服务窗口状态")
    print("   🔄 关闭对应窗口可停止服务")
    print("   ⚠️ 如果页面无法访问，请等待服务完全启动")
    
    # 等待一下，然后询问是否打开浏览器
    print("\n⏳ 等待15秒后自动打开浏览器...")
    for i in range(15, 0, -1):
        print(f"\r   倒计时: {i}秒 (按Ctrl+C跳过)", end="", flush=True)
        try:
            time.sleep(1)
        except KeyboardInterrupt:
            print("\n   跳过等待")
            break
    
    print("\n🌐 正在打开浏览器...")
    try:
        webbrowser.open('http://localhost:3000')
        print("✅ 浏览器已打开")
    except Exception as e:
        print(f"⚠️ 无法自动打开浏览器: {e}")
        print("请手动访问: http://localhost:3000")
    
    print("\n🎉 启动完成！")
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，退出程序")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        input("按回车键退出...")
        sys.exit(1)
