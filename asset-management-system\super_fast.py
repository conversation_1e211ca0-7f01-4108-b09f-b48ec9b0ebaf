#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资产管理系统 - 超级快速启动脚本
完全模拟手动操作，最快启动速度

使用方法:
  python super_fast.py

作者: 全栈设计大师
版本: 1.0.0
"""

import os
import sys
import subprocess
import time
import webbrowser
from pathlib import Path

def main():
    """主函数 - 超级快速启动"""
    project_root = Path(__file__).parent.absolute()
    backend_dir = project_root / "backend"
    frontend_dir = project_root / "frontend"
    
    # 自定义工具路径
    maven_cmd = r"D:\cursor\apache-maven-3.9.9\bin\mvn.cmd"
    npm_cmd = r"D:\cursor\nodejs\npm.cmd"
    
    print("🚀 超级快速启动 - 模拟手动操作")
    print("=" * 40)
    
    # 方法1：直接使用系统命令启动（最快）
    if os.name == 'nt':  # Windows
        print("⚡ 启动后端服务...")
        # 在新的cmd窗口中启动后端，完全独立
        subprocess.Popen(
            f'start "资产管理系统-后端" cmd /k "title 后端服务 && cd /d {backend_dir} && {maven_cmd} spring-boot:run"',
            shell=True
        )
        
        print("⚡ 启动前端服务...")
        # 在新的cmd窗口中启动前端，完全独立
        subprocess.Popen(
            f'start "资产管理系统-前端" cmd /k "title 前端服务 && cd /d {frontend_dir} && {npm_cmd} run dev"',
            shell=True
        )
    else:  # Linux/Mac
        print("⚡ 启动后端服务...")
        subprocess.Popen(
            f'gnome-terminal --title="后端服务" -- bash -c "cd {backend_dir} && {maven_cmd} spring-boot:run; exec bash"',
            shell=True
        )
        
        print("⚡ 启动前端服务...")
        subprocess.Popen(
            f'gnome-terminal --title="前端服务" -- bash -c "cd {frontend_dir} && {npm_cmd} run dev; exec bash"',
            shell=True
        )
    
    print("\n✅ 服务启动命令已执行！")
    print("\n📱 预期访问地址:")
    print("   🏠 系统首页: http://localhost:3000")
    print("   📊 库存统计: http://localhost:3000/statistics")
    print("   👥 用户管理: http://localhost:3000/users")
    print("   🔧 后端API: http://localhost:8080/api")
    
    print("\n💡 提示:")
    print("   - 后端和前端在独立窗口中运行")
    print("   - 关闭对应窗口可停止服务")
    print("   - 后端启动需要30-60秒")
    print("   - 前端启动需要10-20秒")
    print("   - 如果页面无法访问，请等待服务完全启动")
    
    # 等待一下，然后询问是否打开浏览器
    print("\n⏳ 等待15秒后自动打开浏览器...")
    for i in range(15, 0, -1):
        print(f"\r   倒计时: {i}秒 (按Ctrl+C跳过)", end="", flush=True)
        try:
            time.sleep(1)
        except KeyboardInterrupt:
            print("\n   跳过等待")
            break
    
    print("\n🌐 正在打开浏览器...")
    try:
        webbrowser.open('http://localhost:3000')
        print("✅ 浏览器已打开")
    except Exception as e:
        print(f"⚠️ 无法自动打开浏览器: {e}")
        print("请手动访问: http://localhost:3000")
    
    print("\n🎉 启动完成！")
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，退出程序")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        input("按回车键退出...")
        sys.exit(1)
