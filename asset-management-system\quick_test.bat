@echo off
chcp 65001 >nul
echo 🔧 快速测试前后端一体化应用
echo.

:: 强制删除target目录
echo 🧹 清理构建目录...
taskkill /f /im java.exe >nul 2>&1
timeout /t 2 >nul
rmdir /s /q backend\target >nul 2>&1

:: 重新构建
echo 📦 重新构建应用...
cd backend
D:\cursor\apache-maven-3.9.9\bin\mvn.cmd clean package -DskipTests -q
if %errorlevel% neq 0 (
    echo ❌ 构建失败
    pause
    exit /b 1
)

cd ..
echo ✅ 构建成功

:: 启动应用
echo 🚀 启动应用...
echo.
echo 📱 前端页面: http://localhost:8080
echo 🔧 后端API: http://localhost:8080/api
echo.
echo 💡 按 Ctrl+C 停止服务
echo.

java -jar backend/target/asset-management-backend-1.0.0.jar --spring.profiles.active=test

pause
