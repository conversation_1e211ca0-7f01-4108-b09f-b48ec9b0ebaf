package com.asset.service;

import com.asset.dto.LoginRequest;
import com.asset.dto.LoginResponse;
import com.asset.entity.User;
import com.asset.repository.UserRepository;
import com.asset.service.UserDetailsServiceImpl.CustomUserDetails;
import com.asset.utils.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class AuthService {

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private UserDetailsServiceImpl userDetailsService;

    /**
     * 用户登录
     */
    public LoginResponse login(LoginRequest loginRequest) {
        try {
            log.info("用户 {} 尝试登录", loginRequest.getUsername());

            // 进行身份验证
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(
                            loginRequest.getUsername(),
                            loginRequest.getPassword()
                    )
            );

            // 获取用户详情
            CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();
            User user = userDetails.getUser();

            // 生成JWT Token
            Map<String, Object> extraClaims = new HashMap<>();
            extraClaims.put("userId", user.getId());
            extraClaims.put("role", user.getRole().name());
            extraClaims.put("realName", user.getRealName());

            String token = jwtUtil.generateToken(userDetails, extraClaims);

            log.info("用户 {} 登录成功", loginRequest.getUsername());

            return new LoginResponse(token, LoginResponse.UserInfo.fromUser(user));

        } catch (BadCredentialsException e) {
            log.warn("用户 {} 登录失败: 密码错误", loginRequest.getUsername());
            throw new RuntimeException("用户名或密码错误");
        } catch (DisabledException e) {
            log.warn("用户 {} 登录失败: 账户已被禁用", loginRequest.getUsername());
            throw new RuntimeException("账户已被禁用");
        } catch (Exception e) {
            log.error("用户 {} 登录失败: {}", loginRequest.getUsername(), e.getMessage());
            throw new RuntimeException("登录失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前登录用户信息
     */
    public LoginResponse.UserInfo getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        
        if (authentication != null && authentication.getPrincipal() instanceof CustomUserDetails) {
            CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();
            return LoginResponse.UserInfo.fromUser(userDetails.getUser());
        }
        
        throw new RuntimeException("未找到当前登录用户信息");
    }

    /**
     * 获取当前登录用户ID
     */
    public Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        
        if (authentication != null && authentication.getPrincipal() instanceof CustomUserDetails) {
            CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();
            return userDetails.getUserId();
        }
        
        throw new RuntimeException("未找到当前登录用户信息");
    }

    /**
     * 检查当前用户是否为管理员
     */
    public boolean isCurrentUserAdmin() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            
            if (authentication != null && authentication.getPrincipal() instanceof CustomUserDetails) {
                CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();
                return userDetails.getUserRole() == User.UserRole.ADMIN;
            }
            
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 验证Token有效性
     */
    public boolean validateToken(String token) {
        try {
            if (token == null || token.trim().isEmpty()) {
                return false;
            }

            String username = jwtUtil.extractUsername(token);
            UserDetails userDetails = userDetailsService.loadUserByUsername(username);
            
            return jwtUtil.validateToken(token, userDetails);
        } catch (Exception e) {
            log.error("Token验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 刷新Token
     */
    public LoginResponse refreshToken(String token) {
        try {
            if (!validateToken(token)) {
                throw new RuntimeException("无效的Token");
            }

            String username = jwtUtil.extractUsername(token);
            UserDetails userDetails = userDetailsService.loadUserByUsername(username);
            CustomUserDetails customUserDetails = (CustomUserDetails) userDetails;
            
            // 生成新的Token
            Map<String, Object> extraClaims = new HashMap<>();
            extraClaims.put("userId", customUserDetails.getUserId());
            extraClaims.put("role", customUserDetails.getUserRole().name());
            extraClaims.put("realName", customUserDetails.getRealName());

            String newToken = jwtUtil.generateToken(userDetails, extraClaims);

            return new LoginResponse(newToken, LoginResponse.UserInfo.fromUser(customUserDetails.getUser()));

        } catch (Exception e) {
            log.error("Token刷新失败: {}", e.getMessage());
            throw new RuntimeException("Token刷新失败: " + e.getMessage());
        }
    }

    /**
     * 用户登出
     */
    public void logout() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null) {
                String username = authentication.getName();
                log.info("用户 {} 退出登录", username);
            }
            
            // 清除安全上下文
            SecurityContextHolder.clearContext();
            
        } catch (Exception e) {
            log.error("用户登出失败: {}", e.getMessage());
        }
    }
}