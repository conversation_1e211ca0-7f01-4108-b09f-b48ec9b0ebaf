# 设计文档

## 概述

资产管理系统是一个基于Java+Spring Boot+Vue.js+MySQL8技术栈的Web应用程序，旨在为用户提供完整的资产生命周期管理和流转跟踪功能。系统采用前后端分离架构，支持多用户权限管理，每个用户只能管理自己录入的资产信息，管理员可以查看所有用户的资产信息。

## 系统架构

### 技术栈
- **后端**: Java 8+ + Spring Boot 2.7+ + Spring Security + MyBatis Plus
- **前端**: Vue.js 3 + Element Plus + Vue Router + Vuex
- **数据库**: MySQL 8.0
- **构建工具**: Maven (后端) + Vite (前端)
- **文件处理**: Apache POI (Excel导入导出)

### 架构模式
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Vue.js)  │────│  后端 (Spring)   │────│  数据库 (MySQL)  │
│                 │    │                 │    │                 │
│ - 用户界面       │    │ - REST API      │    │ - 数据存储       │
│ - 状态管理       │    │ - 业务逻辑       │    │ - 事务管理       │
│ - 路由管理       │    │ - 权限控制       │    │ - 数据完整性     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 用户界面设计

### 整体布局
系统采用经典的后台管理系统布局，包含顶部导航栏、左侧边栏和主内容区域。

```
┌─────────────────────────────────────────────────────────────┐
│                    顶部导航栏                                │
│  Logo    用户信息    退出登录                                │
├─────────────┬───────────────────────────────────────────────┤
│             │                                               │
│   左侧边栏   │              主内容区域                        │
│             │                                               │
│  - 首页     │                                               │
│  - 资产管理  │                                               │
│  - 库存统计  │                                               │
│  - 用户管理  │                                               │
│             │                                               │
└─────────────┴───────────────────────────────────────────────┘
```

### 侧边栏功能模块设计

基于用户需求，将功能进行合理分组，避免功能过于分散：

#### 1. 首页 (Dashboard)
- 资产概览统计
- 最近操作记录
- 快速操作入口

#### 2. 资产管理
- **资产列表**: 查看、搜索、筛选资产
- **新增资产**: 单个资产录入
- **批量导入**: Excel文件批量导入
- **批量导出**: 导出资产数据到Excel
- **资产详情**: 查看完整资产信息和操作历史

#### 3. 资产操作
- **入库管理**: 资产入库操作和记录
- **安装管理**: 资产安装位置记录
- **位置变更**: 资产位置变更和历史跟踪
- **出库管理**: 资产出库操作和记录

#### 4. 库存统计
- **统计概览**: 总体库存统计
- **分类统计**: 按产品类型统计
- **位置统计**: 按安装位置统计
- **状态统计**: 按资产状态统计

#### 5. 用户管理 (仅管理员可见)
- **用户列表**: 查看所有用户
- **用户创建**: 创建新用户
- **权限管理**: 用户权限设置
- **资产查看**: 切换查看不同用户的资产

## 数据模型设计

### 数据库配置
- **数据库名**: zc
- **用户名**: db
- **密码**: 123456

### 核心数据表

#### 1. 用户表 (sys_user)
```sql
CREATE TABLE sys_user (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码(加密)',
    real_name VARCHAR(50) COMMENT '真实姓名',
    role ENUM('USER', 'ADMIN') DEFAULT 'USER' COMMENT '角色',
    status TINYINT DEFAULT 1 COMMENT '状态(1:启用 0:禁用)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 2. 资产表 (asset)
```sql
CREATE TABLE asset (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    asset_code VARCHAR(100) COMMENT '资产编号',
    serial_number VARCHAR(100) COMMENT '序列号',
    product_model VARCHAR(100) COMMENT '产品型号',
    product_type VARCHAR(100) COMMENT '产品类型',
    current_location VARCHAR(200) COMMENT '当前位置',
    status ENUM('PENDING', 'IN_STOCK', 'INSTALLED', 'OUT_STOCK') DEFAULT 'PENDING' COMMENT '状态',
    remarks TEXT COMMENT '备注信息',
    user_id BIGINT NOT NULL COMMENT '录入用户ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_asset_code (asset_code),
    INDEX idx_serial_number (serial_number),
    FOREIGN KEY (user_id) REFERENCES sys_user(id)
);
```

#### 3. 资产操作历史表 (asset_operation_history)
```sql
CREATE TABLE asset_operation_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    asset_id BIGINT NOT NULL COMMENT '资产ID',
    operation_type ENUM('CREATE', 'IN_STOCK', 'INSTALL', 'LOCATION_CHANGE', 'OUT_STOCK', 'UPDATE') NOT NULL COMMENT '操作类型',
    old_location VARCHAR(200) COMMENT '原位置',
    new_location VARCHAR(200) COMMENT '新位置',
    operator_name VARCHAR(50) COMMENT '操作人员',
    operation_reason VARCHAR(500) COMMENT '操作原因',
    operation_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    user_id BIGINT NOT NULL COMMENT '操作用户ID',
    remarks TEXT COMMENT '备注',
    INDEX idx_asset_id (asset_id),
    INDEX idx_operation_time (operation_time),
    FOREIGN KEY (asset_id) REFERENCES asset(id),
    FOREIGN KEY (user_id) REFERENCES sys_user(id)
);
```

#### 4. 入库记录表 (inbound_record)
```sql
CREATE TABLE inbound_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    asset_id BIGINT NOT NULL COMMENT '资产ID',
    receiver_name VARCHAR(50) COMMENT '签收人',
    inbound_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '入库时间',
    user_id BIGINT NOT NULL COMMENT '操作用户ID',
    remarks TEXT COMMENT '备注',
    INDEX idx_asset_id (asset_id),
    FOREIGN KEY (asset_id) REFERENCES asset(id),
    FOREIGN KEY (user_id) REFERENCES sys_user(id)
);
```

#### 5. 安装记录表 (installation_record)
```sql
CREATE TABLE installation_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    asset_id BIGINT NOT NULL COMMENT '资产ID',
    install_location VARCHAR(200) NOT NULL COMMENT '安装位置',
    install_date DATE COMMENT '安装日期',
    installer_name VARCHAR(50) COMMENT '安装人员',
    user_id BIGINT NOT NULL COMMENT '操作用户ID',
    remarks TEXT COMMENT '备注',
    INDEX idx_asset_id (asset_id),
    FOREIGN KEY (asset_id) REFERENCES asset(id),
    FOREIGN KEY (user_id) REFERENCES sys_user(id)
);
```

#### 6. 出库记录表 (outbound_record)
```sql
CREATE TABLE outbound_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    asset_id BIGINT NOT NULL COMMENT '资产ID',
    outbound_reason VARCHAR(500) COMMENT '出库原因',
    outbound_person VARCHAR(50) COMMENT '出库人员',
    outbound_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '出库时间',
    user_id BIGINT NOT NULL COMMENT '操作用户ID',
    remarks TEXT COMMENT '备注',
    INDEX idx_asset_id (asset_id),
    FOREIGN KEY (asset_id) REFERENCES asset(id),
    FOREIGN KEY (user_id) REFERENCES sys_user(id)
);
```

## 组件和接口设计

### 后端API接口设计

#### 1. 用户认证接口
```
POST /api/auth/login          # 用户登录
POST /api/auth/logout         # 用户退出
GET  /api/auth/current        # 获取当前用户信息
```

#### 2. 用户管理接口 (管理员)
```
GET    /api/users             # 获取用户列表
POST   /api/users             # 创建用户
PUT    /api/users/{id}        # 更新用户
DELETE /api/users/{id}        # 删除用户
PUT    /api/users/{id}/status # 启用/禁用用户
```

#### 3. 资产管理接口
```
GET    /api/assets            # 获取资产列表
POST   /api/assets            # 创建资产
PUT    /api/assets/{id}       # 更新资产
DELETE /api/assets/{id}       # 删除资产
GET    /api/assets/{id}       # 获取资产详情
GET    /api/assets/{id}/history # 获取资产操作历史
```

#### 4. 资产操作接口
```
POST   /api/assets/{id}/inbound    # 资产入库
POST   /api/assets/{id}/install    # 资产安装
POST   /api/assets/{id}/relocate   # 资产位置变更
POST   /api/assets/{id}/outbound   # 资产出库
```

#### 5. 导入导出接口
```
POST   /api/assets/import     # 批量导入资产
GET    /api/assets/export     # 导出资产数据
```

#### 6. 统计查询接口
```
GET    /api/statistics/overview    # 概览统计
GET    /api/statistics/by-type     # 按类型统计
GET    /api/statistics/by-location # 按位置统计
GET    /api/statistics/by-status   # 按状态统计
```

### 前端组件设计

#### 1. 布局组件
- `Layout.vue`: 主布局组件
- `Sidebar.vue`: 侧边栏组件
- `Header.vue`: 顶部导航组件

#### 2. 资产管理组件
- `AssetList.vue`: 资产列表组件
- `AssetForm.vue`: 资产表单组件
- `AssetDetail.vue`: 资产详情组件
- `AssetImport.vue`: 资产导入组件

#### 3. 资产操作组件
- `InboundForm.vue`: 入库表单组件
- `InstallForm.vue`: 安装表单组件
- `RelocateForm.vue`: 位置变更表单组件
- `OutboundForm.vue`: 出库表单组件

#### 4. 统计组件
- `StatisticsOverview.vue`: 统计概览组件
- `StatisticsCharts.vue`: 统计图表组件

## 错误处理

### 后端错误处理
- 统一异常处理器处理所有业务异常
- 标准化错误响应格式
- 详细的错误日志记录

### 前端错误处理
- 全局错误拦截器
- 用户友好的错误提示
- 网络异常处理

## 测试策略

### 后端测试
- 单元测试: 使用JUnit 5 + Mockito
- 集成测试: 使用Spring Boot Test
- API测试: 使用MockMvc

### 前端测试
- 单元测试: 使用Jest + Vue Test Utils
- 端到端测试: 使用Cypress

### 数据库测试
- 使用H2内存数据库进行测试
- 数据库迁移脚本测试