# 资产管理系统设计文档

## 概述

资产管理系统是一个基于Java+Spring Boot+Vue.js+MySQL8技术栈的Web应用程序，主要用于跟踪和管理企业资产的全生命周期流转。系统采用前后端分离架构，支持多用户使用，每个用户只能管理自己录入的资产，管理员可以查看所有用户的资产信息。

### 核心功能
- 用户认证和权限管理
- 资产信息管理（CRUD操作）
- 资产导入导出（Excel格式）
- 资产流转跟踪（入库、安装、位置变更、出库）
- 库存统计和查询
- 完整的操作历史记录

## 系统架构

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Vue.js)  │────│  后端 (Spring)   │────│  数据库 (MySQL)  │
│                 │    │                 │    │                 │
│ - 用户界面       │    │ - REST API      │    │ - 数据存储       │
│ - 状态管理       │    │ - 业务逻辑       │    │ - 事务管理       │
│ - 路由管理       │    │ - 数据访问       │    │ - 数据完整性     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 技术栈详情
- **前端**: Vue.js 3 + Vue Router + Vuex/Pinia + Element Plus + Axios
- **后端**: Spring Boot 2.7+ + Spring Security + Spring Data JPA + MyBatis Plus
- **数据库**: MySQL 8.0
- **构建工具**: Maven (后端) + Vite (前端)
- **文件处理**: Apache POI (Excel导入导出)

## 组件和接口

### 前端组件架构
```
src/
├── components/           # 通用组件
│   ├── Layout/          # 布局组件
│   ├── Table/           # 表格组件
│   ├── Form/            # 表单组件
│   └── Upload/          # 文件上传组件
├── views/               # 页面组件
│   ├── Login/           # 登录页面
│   ├── Dashboard/       # 仪表板
│   ├── Asset/           # 资产管理
│   │   ├── List.vue     # 资产列表
│   │   ├── Detail.vue   # 资产详情
│   │   ├── Form.vue     # 资产表单
│   │   └── History.vue  # 历史记录
│   ├── Import/          # 导入导出
│   └── Statistics/      # 统计报表
├── store/               # 状态管理
├── router/              # 路由配置
├── api/                 # API接口
└── utils/               # 工具函数
```

### 后端组件架构
```
src/main/java/com/asset/
├── controller/          # 控制器层
│   ├── AuthController   # 认证控制器
│   ├── AssetController  # 资产控制器
│   ├── UserController   # 用户控制器
│   └── StatController   # 统计控制器
├── service/             # 服务层
│   ├── AuthService      # 认证服务
│   ├── AssetService     # 资产服务
│   ├── UserService      # 用户服务
│   ├── ImportService    # 导入服务
│   └── HistoryService   # 历史记录服务
├── repository/          # 数据访问层
│   ├── AssetRepository  # 资产数据访问
│   ├── UserRepository   # 用户数据访问
│   └── HistoryRepository# 历史记录数据访问
├── entity/              # 实体类
├── dto/                 # 数据传输对象
├── config/              # 配置类
└── utils/               # 工具类
```

### 核心API接口

#### 认证相关
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/profile` - 获取用户信息

#### 资产管理
- `GET /api/assets` - 获取资产列表
- `GET /api/assets/{id}` - 获取资产详情
- `POST /api/assets` - 创建资产
- `PUT /api/assets/{id}` - 更新资产
- `DELETE /api/assets/{id}` - 删除资产

#### 资产操作
- `POST /api/assets/{id}/receive` - 资产入库
- `POST /api/assets/{id}/install` - 资产安装
- `POST /api/assets/{id}/move` - 资产位置变更
- `POST /api/assets/{id}/outbound` - 资产出库

#### 导入导出
- `POST /api/assets/import` - 批量导入资产
- `GET /api/assets/export` - 导出资产数据

#### 统计查询
- `GET /api/statistics/overview` - 库存概览
- `GET /api/statistics/by-type` - 按类型统计
- `GET /api/statistics/by-location` - 按位置统计

## 数据模型

### 数据库设计

#### 用户表 (users)
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('USER', 'ADMIN') DEFAULT 'USER',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 资产表 (assets)
```sql
CREATE TABLE assets (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    asset_code VARCHAR(100),                    -- 资产编号
    serial_number VARCHAR(100),                 -- 序列号
    product_model VARCHAR(100),                 -- 产品型号
    product_type VARCHAR(100),                  -- 产品类型
    status ENUM('PENDING', 'RECEIVED', 'INSTALLED', 'OUTBOUND') DEFAULT 'PENDING',
    current_location VARCHAR(200),              -- 当前位置
    receiver VARCHAR(100),                      -- 签收人
    received_at TIMESTAMP NULL,                 -- 入库时间
    installer VARCHAR(100),                     -- 安装人员
    installed_at TIMESTAMP NULL,                -- 安装时间
    notes TEXT,                                 -- 备注信息
    user_id BIGINT NOT NULL,                    -- 所属用户
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_id (user_id),
    INDEX idx_asset_code (asset_code),
    INDEX idx_status (status)
);
```

#### 资产历史记录表 (asset_history)
```sql
CREATE TABLE asset_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    asset_id BIGINT NOT NULL,
    operation_type ENUM('CREATE', 'UPDATE', 'RECEIVE', 'INSTALL', 'MOVE', 'OUTBOUND') NOT NULL,
    old_value JSON,                             -- 变更前的值
    new_value JSON,                             -- 变更后的值
    operator VARCHAR(100),                      -- 操作人员
    reason VARCHAR(500),                        -- 操作原因
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE CASCADE,
    INDEX idx_asset_id (asset_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_created_at (created_at)
);
```

#### 位置历史表 (location_history)
```sql
CREATE TABLE location_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    asset_id BIGINT NOT NULL,
    from_location VARCHAR(200),                 -- 原位置
    to_location VARCHAR(200),                   -- 新位置
    move_reason VARCHAR(500),                   -- 移动原因
    operator VARCHAR(100),                      -- 操作人员
    moved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE CASCADE,
    INDEX idx_asset_id (asset_id),
    INDEX idx_moved_at (moved_at)
);
```

### 实体类设计

#### Asset实体
```java
@Entity
@Table(name = "assets")
public class Asset {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "asset_code")
    private String assetCode;
    
    @Column(name = "serial_number")
    private String serialNumber;
    
    @Column(name = "product_model")
    private String productModel;
    
    @Column(name = "product_type")
    private String productType;
    
    @Enumerated(EnumType.STRING)
    private AssetStatus status;
    
    @Column(name = "current_location")
    private String currentLocation;
    
    private String receiver;
    
    @Column(name = "received_at")
    private LocalDateTime receivedAt;
    
    private String installer;
    
    @Column(name = "installed_at")
    private LocalDateTime installedAt;
    
    private String notes;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private User user;
    
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
}
```

## 错误处理

### 异常处理策略
1. **全局异常处理器**: 使用@ControllerAdvice统一处理异常
2. **业务异常**: 自定义业务异常类，包含错误码和错误信息
3. **数据验证**: 使用Bean Validation进行参数校验
4. **权限异常**: 处理认证和授权相关异常

### 错误响应格式
```json
{
    "success": false,
    "code": "ASSET_NOT_FOUND",
    "message": "资产不存在",
    "timestamp": "2024-01-01T10:00:00Z"
}
```

### 常见错误码
- `AUTH_FAILED`: 认证失败
- `ACCESS_DENIED`: 权限不足
- `ASSET_NOT_FOUND`: 资产不存在
- `DUPLICATE_ASSET_CODE`: 资产编号重复
- `INVALID_OPERATION`: 无效操作
- `FILE_UPLOAD_ERROR`: 文件上传失败

## 测试策略

### 测试层次
1. **单元测试**: 使用JUnit 5 + Mockito测试服务层逻辑
2. **集成测试**: 使用@SpringBootTest测试完整流程
3. **API测试**: 使用MockMvc测试控制器层
4. **前端测试**: 使用Jest + Vue Test Utils测试组件

### 测试覆盖重点
- 用户认证和权限控制
- 资产CRUD操作
- 资产状态流转
- 导入导出功能
- 历史记录生成
- 数据权限隔离

### 测试数据管理
- 使用@Transactional确保测试数据隔离
- 使用TestContainers进行数据库集成测试
- 准备标准测试数据集

## 安全考虑

### 认证和授权
- 使用JWT Token进行无状态认证
- 实现基于角色的访问控制(RBAC)
- 数据权限隔离：用户只能访问自己的数据

### 数据安全
- 密码使用BCrypt加密存储
- 敏感数据传输使用HTTPS
- SQL注入防护：使用参数化查询
- XSS防护：前端输入验证和转义

### 操作审计
- 记录所有关键操作到历史表
- 包含操作人、操作时间、操作内容
- 支持操作轨迹追踪和审计

## 性能优化

### 数据库优化
- 合理设计索引：用户ID、资产编号、状态等
- 分页查询：避免大量数据一次性加载
- 连接池配置：使用HikariCP优化数据库连接

### 缓存策略
- 用户信息缓存：减少频繁的用户查询
- 统计数据缓存：缓存计算结果
- 使用Redis作为缓存存储

### 前端优化
- 组件懒加载：按需加载页面组件
- 表格虚拟滚动：处理大量数据展示
- 图片懒加载：优化页面加载速度

## 部署架构

### 开发环境
- 前端：Vite开发服务器 (端口3000)
- 后端：Spring Boot内置Tomcat (端口8080)
- 数据库：MySQL 8.0 (端口3306)

### 生产环境
- 前端：Nginx静态文件服务
- 后端：Spring Boot JAR包部署
- 数据库：MySQL 8.0集群
- 反向代理：Nginx负载均衡

### 配置管理
- 使用Spring Profiles区分环境
- 敏感配置使用环境变量
- 数据库连接信息外部化配置