package com.asset.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "asset_history")
@TableName("asset_history")
public class AssetHistory {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Long id;

    @NotNull(message = "资产ID不能为空")
    @Column(name = "asset_id", nullable = false)
    private Long assetId;

    @NotNull(message = "操作类型不能为空")
    @Enumerated(EnumType.STRING)
    @Column(name = "operation_type", nullable = false, length = 20)
    private OperationType operationType;

    @Column(name = "old_value", columnDefinition = "JSON")
    private String oldValue;

    @Column(name = "new_value", columnDefinition = "JSON")
    private String newValue;

    @Column(length = 100)
    private String operator;

    @Column(length = 500)
    private String reason;

    @Column(columnDefinition = "TEXT")
    private String description;

    @TableField(fill = FieldFill.INSERT)
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    // 多对一关系：多个历史记录属于一个资产
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "asset_id", insertable = false, updatable = false)
    @TableField(exist = false)
    private Asset asset;

    public enum OperationType {
        CREATE("创建"),
        UPDATE("更新"),
        RECEIVE("入库"),
        INSTALL("安装"),
        MOVE("位置变更"),
        OUTBOUND("出库"),
        NOTE("备注");

        private final String description;

        OperationType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}