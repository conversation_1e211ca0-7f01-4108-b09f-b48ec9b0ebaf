package com.asset.repository;

import com.asset.entity.User;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Optional;

@Mapper
public interface UserRepository extends BaseMapper<User> {

    /**
     * 根据用户名查找用户
     */
    @Select("SELECT * FROM users WHERE username = #{username} AND deleted = 0")
    Optional<User> findByUsername(@Param("username") String username);

    /**
     * 根据邮箱查找用户
     */
    @Select("SELECT * FROM users WHERE email = #{email} AND deleted = 0")
    Optional<User> findByEmail(@Param("email") String email);

    /**
     * 分页查询用户列表
     */
    @Select("SELECT * FROM users WHERE deleted = 0 " +
            "AND (#{keyword} IS NULL OR username LIKE CONCAT('%', #{keyword}, '%') " +
            "OR real_name LIKE CONCAT('%', #{keyword}, '%') " +
            "OR email LIKE CONCAT('%', #{keyword}, '%')) " +
            "ORDER BY created_at DESC")
    IPage<User> findUsersWithPagination(Page<User> page, @Param("keyword") String keyword);

    /**
     * 根据角色查找用户
     */
    @Select("SELECT * FROM users WHERE role = #{role} AND deleted = 0")
    List<User> findByRole(@Param("role") User.UserRole role);

    /**
     * 根据状态查找用户
     */
    @Select("SELECT * FROM users WHERE status = #{status} AND deleted = 0")
    List<User> findByStatus(@Param("status") User.UserStatus status);

    /**
     * 检查用户名是否存在
     */
    @Select("SELECT COUNT(1) FROM users WHERE username = #{username} AND deleted = 0 AND id != #{excludeId}")
    int countByUsernameAndIdNot(@Param("username") String username, @Param("excludeId") Long excludeId);

    /**
     * 检查邮箱是否存在
     */
    @Select("SELECT COUNT(1) FROM users WHERE email = #{email} AND deleted = 0 AND id != #{excludeId}")
    int countByEmailAndIdNot(@Param("email") String email, @Param("excludeId") Long excludeId);

    /**
     * 统计用户数量
     */
    @Select("SELECT COUNT(1) FROM users WHERE deleted = 0")
    long countActiveUsers();

    /**
     * 根据部门统计用户数量
     */
    @Select("SELECT department, COUNT(1) as count FROM users " +
            "WHERE deleted = 0 AND department IS NOT NULL " +
            "GROUP BY department ORDER BY count DESC")
    List<Object> countUsersByDepartment();
}