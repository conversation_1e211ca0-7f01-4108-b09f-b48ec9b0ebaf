import{_ as G}from"./_plugin-vue_export-helper-62491c14.js";/* empty css                 *//* empty css               *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                     *//* empty css               *//* empty css                */import{u as U,r as _,b as O,o as W,c as b,d as X,g as t,w as e,aq as q,j as Q,aC as H,f as l,F as w,G as J,a8 as d,R as K,N as Z,b5 as tt,i as f,X as V,$ as et,aB as N,ap as z,z as at,E as st,aD as ot,aN as lt,aQ as nt,aO as it,n as rt,b6 as ut}from"./index-2733c819.js";import"./index-fd3ee58d.js";import{g as dt,a as ct}from"./inventory-bb5e7097.js";import{j as pt}from"./asset-f5b5b286.js";const mt={class:"inventory-statistics"},_t={class:"summary-content"},ft={class:"summary-icon total"},yt={class:"summary-info"},vt={class:"summary-value"},ht={class:"summary-content"},gt={class:"summary-icon locations"},bt={class:"summary-info"},wt={class:"summary-value"},xt={class:"summary-content"},Ct={class:"summary-icon types"},Et={class:"summary-info"},Dt={class:"summary-value"},St={class:"summary-content"},Rt={class:"summary-icon value"},Vt={class:"summary-info"},Nt={class:"summary-value"},kt={class:"card-header"},It={__name:"Statistics",setup(Ot){const A=U(),x=_(),C=_();let p=null,c=null;const h=O({totalAssets:0,locationCount:0,typeCount:0}),E=O({totalValue:0,avgValue:0,minValue:0,maxValue:0}),y=_([]),m=_([]),D=_([]),v=_([]),B={PENDING:"待处理",RECEIVED:"已入库",INSTALLED:"已安装",OUTBOUND:"已出库",SCRAPPED:"已报废"},S=o=>o?Number(o).toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}):"0.00",L=(o,a)=>!o||!a?"-":N(o).add(a,"month").format("YYYY-MM-DD"),k=(o,a)=>{if(!o||!a)return 0;const s=N(o).add(a,"month");return Math.max(0,s.diff(N(),"day"))},T=o=>{const a=k(o.purchaseDate,o.warrantyPeriod);return a<=7?"danger":a<=15?"warning":"success"},M=()=>{if(!x.value)return;if(p=z(x.value),!y.value||y.value.length===0){const s={title:{text:"暂无数据",left:"center",top:"center",textStyle:{color:"#999",fontSize:14}}};p.setOption(s);return}const o=y.value.map(s=>({name:B[s.status]||s.status,value:s.count||0})).filter(s=>s.value>0),a={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left"},series:[{name:"资产状态",type:"pie",radius:"50%",data:o,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};p.setOption(a)},P=()=>{if(!C.value)return;if(c=z(C.value),!m.value||m.value.length===0){const s={title:{text:"暂无数据",left:"center",top:"center",textStyle:{color:"#999",fontSize:14}}};c.setOption(s);return}const o=m.value.slice(0,10).map(s=>({name:s.location||"未知位置",value:s.totalCount||0})).filter(s=>s.value>0);if(o.length===0){const s={title:{text:"暂无数据",left:"center",top:"center",textStyle:{color:"#999",fontSize:14}}};c.setOption(s);return}const a={tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"value"},yAxis:{type:"category",data:o.map(s=>s.name)},series:[{name:"资产数量",type:"bar",data:o.map(s=>s.value),itemStyle:{color:"#409EFF"}}]};c.setOption(a)},j=async()=>{try{const o=await dt();console.log("库存摘要数据:",o.data),Object.assign(h,o.data),o.data.statusDistribution&&(y.value=o.data.statusDistribution,console.log("状态分布数据:",y.value)),o.data.valueStatistics&&(Object.assign(E,o.data.valueStatistics),console.log("价值统计数据:",E));const s=(await ct()).data;console.log("完整报告数据:",s),s.locationInventory&&(m.value=s.locationInventory,console.log("位置统计数据:",m.value)),s.typeInventory&&(D.value=s.typeInventory,console.log("类型统计数据:",D.value));const r=await pt();v.value=r.data,await q(),M(),P()}catch(o){console.error("加载数据失败:",o),Q.error("加载数据失败")}},F=o=>{A.push(`/assets/${o}`)};return W(()=>{j(),window.addEventListener("resize",()=>{p==null||p.resize(),c==null||c.resize()})}),(o,a)=>{const s=at,r=st,u=ot,g=H,n=lt,R=nt,I=it,Y=rt,$=ut;return b(),X("div",mt,[t(g,{gutter:20,class:"summary-cards"},{default:e(()=>[t(u,{span:6},{default:e(()=>[t(r,{class:"summary-card"},{default:e(()=>[l("div",_t,[l("div",ft,[t(s,null,{default:e(()=>[t(w(J))]),_:1})]),l("div",yt,[a[0]||(a[0]=l("div",{class:"summary-title"},"总资产数",-1)),l("div",vt,d(h.totalAssets||0),1)])])]),_:1})]),_:1}),t(u,{span:6},{default:e(()=>[t(r,{class:"summary-card"},{default:e(()=>[l("div",ht,[l("div",gt,[t(s,null,{default:e(()=>[t(w(K))]),_:1})]),l("div",bt,[a[1]||(a[1]=l("div",{class:"summary-title"},"位置数量",-1)),l("div",wt,d(h.locationCount||0),1)])])]),_:1})]),_:1}),t(u,{span:6},{default:e(()=>[t(r,{class:"summary-card"},{default:e(()=>[l("div",xt,[l("div",Ct,[t(s,null,{default:e(()=>[t(w(Z))]),_:1})]),l("div",Et,[a[2]||(a[2]=l("div",{class:"summary-title"},"类型数量",-1)),l("div",Dt,d(h.typeCount||0),1)])])]),_:1})]),_:1}),t(u,{span:6},{default:e(()=>[t(r,{class:"summary-card"},{default:e(()=>[l("div",St,[l("div",Rt,[t(s,null,{default:e(()=>[t(w(tt))]),_:1})]),l("div",Vt,[a[3]||(a[3]=l("div",{class:"summary-title"},"总价值",-1)),l("div",Nt,"¥"+d(S(E.totalValue)),1)])])]),_:1})]),_:1})]),_:1}),t(g,{gutter:20},{default:e(()=>[t(u,{span:12},{default:e(()=>[t(r,null,{header:e(()=>a[4]||(a[4]=[l("span",null,"资产状态分布",-1)])),default:e(()=>[l("div",{ref_key:"statusChartRef",ref:x,style:{height:"300px"}},null,512)]),_:1})]),_:1}),t(u,{span:12},{default:e(()=>[t(r,null,{header:e(()=>a[5]||(a[5]=[l("span",null,"位置分布统计",-1)])),default:e(()=>[l("div",{ref_key:"locationChartRef",ref:C,style:{height:"300px"}},null,512)]),_:1})]),_:1})]),_:1}),t(g,{gutter:20,style:{"margin-top":"20px"}},{default:e(()=>[t(u,{span:12},{default:e(()=>[t(r,null,{header:e(()=>a[6]||(a[6]=[l("span",null,"产品类型统计",-1)])),default:e(()=>[t(R,{data:D.value,stripe:"","max-height":"400"},{default:e(()=>[t(n,{prop:"type",label:"产品类型"}),t(n,{prop:"totalCount",label:"总数量",width:"80"}),t(n,{prop:"installedCount",label:"已安装",width:"80"}),t(n,{prop:"receivedCount",label:"已入库",width:"80"}),t(n,{prop:"totalValue",label:"总价值",width:"120"},{default:e(({row:i})=>[f(" ¥"+d(S(i.totalValue)),1)]),_:1})]),_:1},8,["data"])]),_:1})]),_:1}),t(u,{span:12},{default:e(()=>[t(r,null,{header:e(()=>a[7]||(a[7]=[l("span",null,"位置详细统计",-1)])),default:e(()=>[t(R,{data:m.value,stripe:"","max-height":"400"},{default:e(()=>[t(n,{prop:"location",label:"位置"}),t(n,{prop:"totalCount",label:"总数量",width:"80"}),t(n,{prop:"installedCount",label:"已安装",width:"80"}),t(n,{prop:"receivedCount",label:"已入库",width:"80"}),t(n,{prop:"totalValue",label:"总价值",width:"120"},{default:e(({row:i})=>[f(" ¥"+d(S(i.totalValue)),1)]),_:1})]),_:1},8,["data"])]),_:1})]),_:1})]),_:1}),t(g,{style:{"margin-top":"20px"}},{default:e(()=>[t(u,{span:24},{default:e(()=>[t(r,null,{header:e(()=>[l("div",kt,[a[8]||(a[8]=l("span",null,"即将过保资产 (30天内)",-1)),v.value.length>0?(b(),V(I,{key:0,type:"warning"},{default:e(()=>[f(d(v.value.length)+" 项 ",1)]),_:1})):et("",!0)])]),default:e(()=>[v.value.length>0?(b(),V(R,{key:0,data:v.value,stripe:""},{default:e(()=>[t(n,{prop:"assetCode",label:"资产编号",width:"120"}),t(n,{prop:"productModel",label:"产品型号",width:"150"}),t(n,{prop:"brand",label:"品牌",width:"100"}),t(n,{prop:"purchaseDate",label:"采购日期",width:"120"}),t(n,{prop:"warrantyPeriod",label:"保修期(月)",width:"100"}),t(n,{label:"过保日期",width:"120"},{default:e(({row:i})=>[f(d(L(i.purchaseDate,i.warrantyPeriod)),1)]),_:1}),t(n,{prop:"currentLocation",label:"当前位置",width:"120"}),t(n,{label:"剩余天数",width:"100"},{default:e(({row:i})=>[t(I,{type:T(i)},{default:e(()=>[f(d(k(i.purchaseDate,i.warrantyPeriod))+" 天 ",1)]),_:2},1032,["type"])]),_:1}),t(n,{label:"操作",width:"100",fixed:"right"},{default:e(({row:i})=>[t(Y,{type:"primary",size:"small",onClick:zt=>F(i.id)},{default:e(()=>a[9]||(a[9]=[f(" 查看 ",-1)])),_:2,__:[9]},1032,["onClick"])]),_:1})]),_:1},8,["data"])):(b(),V($,{key:1,description:"暂无即将过保的资产"}))]),_:1})]),_:1})]),_:1})])}}},Xt=G(It,[["__scopeId","data-v-e54a8776"]]);export{Xt as default};
