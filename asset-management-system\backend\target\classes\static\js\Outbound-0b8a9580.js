import{_ as te}from"./_plugin-vue_export-helper-62491c14.js";/* empty css                   *//* empty css                   *//* empty css                       *//* empty css                 *//* empty css                     *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                */import{r as _,b as w,o as le,c as D,d as oe,f,g as e,w as o,j as v,E as ae,aV as ne,F as k,M as se,i as u,aH as ue,L as de,H as re,X as M,a8 as R,$ as ie,y as pe,aB as me,k as ce,l as be,aL as _e,aM as fe,z as ge,n as ve,p as Ve,aN as Ce,aO as ye,aQ as Ee,aR as Te,aU as we,aS as De}from"./index-2733c819.js";const ke={class:"outbound-container"},Re={class:"card-header"},he={class:"pagination-container"},Le={__name:"Outbound",setup(Se){const V=_(!1),C=_(!1),h=_([]),y=_([]),g=_(!1),E=_(),i=w({assetCode:"",currentLocation:"",status:""}),r=w({current:1,pageSize:20,total:0}),n=w({assetId:null,assetCode:"",productModel:"",currentLocation:"",outboundReason:"",outboundPerson:"",outboundTime:new Date,remarks:""}),O={outboundReason:[{required:!0,message:"请选择出库原因",trigger:"change"}],outboundPerson:[{required:!0,message:"请输入出库人员姓名",trigger:"blur"}],outboundTime:[{required:!0,message:"请选择出库时间",trigger:"change"}]},b=async()=>{V.value=!0;try{h.value=[],r.total=0}catch(a){v.error("加载数据失败: "+a.message)}finally{V.value=!1}},L=()=>{r.current=1,b()},P=()=>{Object.assign(i,{assetCode:"",currentLocation:"",status:""}),L()},z=a=>{y.value=a},I=a=>{Object.assign(n,{assetId:a.id,assetCode:a.assetCode,productModel:a.productModel,currentLocation:a.currentLocation,outboundReason:"",outboundPerson:"",outboundTime:new Date,remarks:""}),g.value=!0},B=async()=>{try{await E.value.validate(),C.value=!0,await new Promise(a=>setTimeout(a,1e3)),v.success("出库成功"),g.value=!1,b()}catch(a){a.message&&v.error("出库失败: "+a.message)}finally{C.value=!1}},F=()=>{if(y.value.length===0){v.warning("请选择要出库的资产");return}pe.confirm(`确定要批量出库选中的 ${y.value.length} 个资产吗？`,"批量出库确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{v.success("批量出库成功"),b()})},A=a=>{console.log("查看详情:",a)},H=a=>{console.log("查看历史:",a)},$=()=>{var a;(a=E.value)==null||a.resetFields()},j=a=>{r.pageSize=a,b()},Y=a=>{r.current=a,b()},q=a=>({PENDING:"warning",RECEIVED:"success",INSTALLED:"info",OUTBOUND:"danger"})[a]||"info",G=a=>({PENDING:"待入库",RECEIVED:"已入库",INSTALLED:"已安装",OUTBOUND:"已出库"})[a]||a,Q=a=>me(a).format("YYYY-MM-DD HH:mm:ss");return le(()=>{b()}),(a,t)=>{const m=ce,s=be,c=_e,S=fe,T=ge,p=ve,U=Ve,N=ae,d=Ce,X=ye,J=Ee,K=Te,W=we,Z=ne,ee=De;return D(),oe("div",ke,[t[23]||(t[23]=f("div",{class:"page-header"},[f("h2",null,"出库管理"),f("p",null,"管理资产的出库操作和记录")],-1)),e(N,{class:"search-card",shadow:"never"},{default:o(()=>[e(U,{model:i,inline:"",class:"search-form"},{default:o(()=>[e(s,{label:"资产编号"},{default:o(()=>[e(m,{modelValue:i.assetCode,"onUpdate:modelValue":t[0]||(t[0]=l=>i.assetCode=l),placeholder:"请输入资产编号",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(s,{label:"当前位置"},{default:o(()=>[e(m,{modelValue:i.currentLocation,"onUpdate:modelValue":t[1]||(t[1]=l=>i.currentLocation=l),placeholder:"请输入当前位置",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(s,{label:"状态"},{default:o(()=>[e(S,{modelValue:i.status,"onUpdate:modelValue":t[2]||(t[2]=l=>i.status=l),placeholder:"请选择状态",clearable:"",style:{width:"150px"}},{default:o(()=>[e(c,{label:"已安装",value:"INSTALLED"}),e(c,{label:"已出库",value:"OUTBOUND"})]),_:1},8,["modelValue"])]),_:1}),e(s,null,{default:o(()=>[e(p,{type:"primary",onClick:L},{default:o(()=>[e(T,null,{default:o(()=>[e(k(se))]),_:1}),t[14]||(t[14]=u(" 搜索 ",-1))]),_:1,__:[14]}),e(p,{onClick:P},{default:o(()=>[e(T,null,{default:o(()=>[e(k(ue))]),_:1}),t[15]||(t[15]=u(" 重置 ",-1))]),_:1,__:[15]})]),_:1})]),_:1},8,["model"])]),_:1}),e(N,{class:"table-card",shadow:"never"},{header:o(()=>[f("div",Re,[t[17]||(t[17]=f("span",null,"资产出库管理",-1)),e(p,{type:"danger",onClick:F},{default:o(()=>[e(T,null,{default:o(()=>[e(k(de))]),_:1}),t[16]||(t[16]=u(" 批量出库 ",-1))]),_:1,__:[16]})])]),default:o(()=>[re((D(),M(J,{data:h.value,onSelectionChange:z,style:{width:"100%"}},{default:o(()=>[e(d,{type:"selection",width:"55"}),e(d,{prop:"assetCode",label:"资产编号",width:"120"}),e(d,{prop:"serialNumber",label:"序列号",width:"150"}),e(d,{prop:"productModel",label:"产品型号",width:"150"}),e(d,{prop:"productType",label:"产品类型",width:"120"}),e(d,{prop:"currentLocation",label:"当前位置",width:"180"}),e(d,{prop:"status",label:"状态",width:"100"},{default:o(({row:l})=>[e(X,{type:q(l.status)},{default:o(()=>[u(R(G(l.status)),1)]),_:2},1032,["type"])]),_:1}),e(d,{prop:"outboundTime",label:"出库时间",width:"180"},{default:o(({row:l})=>[u(R(l.outboundTime?Q(l.outboundTime):"-"),1)]),_:1}),e(d,{prop:"outboundPerson",label:"出库人员",width:"120"},{default:o(({row:l})=>[u(R(l.outboundPerson||"-"),1)]),_:1}),e(d,{label:"操作",width:"200",fixed:"right"},{default:o(({row:l})=>[l.status==="INSTALLED"?(D(),M(p,{key:0,type:"danger",size:"small",onClick:x=>I(l)},{default:o(()=>t[18]||(t[18]=[u(" 出库 ",-1)])),_:2,__:[18]},1032,["onClick"])):ie("",!0),e(p,{type:"info",size:"small",onClick:x=>A(l)},{default:o(()=>t[19]||(t[19]=[u(" 详情 ",-1)])),_:2,__:[19]},1032,["onClick"]),e(p,{type:"success",size:"small",onClick:x=>H(l)},{default:o(()=>t[20]||(t[20]=[u(" 历史 ",-1)])),_:2,__:[20]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ee,V.value]]),f("div",he,[e(K,{"current-page":r.current,"onUpdate:currentPage":t[3]||(t[3]=l=>r.current=l),"page-size":r.pageSize,"onUpdate:pageSize":t[4]||(t[4]=l=>r.pageSize=l),total:r.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:j,onCurrentChange:Y},null,8,["current-page","page-size","total"])])]),_:1}),e(Z,{modelValue:g.value,"onUpdate:modelValue":t[13]||(t[13]=l=>g.value=l),title:"资产出库",width:"600px",onClose:$},{footer:o(()=>[e(p,{onClick:t[12]||(t[12]=l=>g.value=!1)},{default:o(()=>t[21]||(t[21]=[u("取消",-1)])),_:1,__:[21]}),e(p,{type:"danger",onClick:B,loading:C.value},{default:o(()=>t[22]||(t[22]=[u(" 确认出库 ",-1)])),_:1,__:[22]},8,["loading"])]),default:o(()=>[e(U,{ref_key:"outboundFormRef",ref:E,model:n,rules:O,"label-width":"100px"},{default:o(()=>[e(s,{label:"资产编号"},{default:o(()=>[e(m,{modelValue:n.assetCode,"onUpdate:modelValue":t[5]||(t[5]=l=>n.assetCode=l),disabled:""},null,8,["modelValue"])]),_:1}),e(s,{label:"产品型号"},{default:o(()=>[e(m,{modelValue:n.productModel,"onUpdate:modelValue":t[6]||(t[6]=l=>n.productModel=l),disabled:""},null,8,["modelValue"])]),_:1}),e(s,{label:"当前位置"},{default:o(()=>[e(m,{modelValue:n.currentLocation,"onUpdate:modelValue":t[7]||(t[7]=l=>n.currentLocation=l),disabled:""},null,8,["modelValue"])]),_:1}),e(s,{label:"出库原因",prop:"outboundReason"},{default:o(()=>[e(S,{modelValue:n.outboundReason,"onUpdate:modelValue":t[8]||(t[8]=l=>n.outboundReason=l),placeholder:"请选择出库原因",style:{width:"100%"}},{default:o(()=>[e(c,{label:"设备报废",value:"SCRAP"}),e(c,{label:"设备维修",value:"REPAIR"}),e(c,{label:"设备调拨",value:"TRANSFER"}),e(c,{label:"设备退回",value:"RETURN"}),e(c,{label:"其他",value:"OTHER"})]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"出库人员",prop:"outboundPerson"},{default:o(()=>[e(m,{modelValue:n.outboundPerson,"onUpdate:modelValue":t[9]||(t[9]=l=>n.outboundPerson=l),placeholder:"请输入出库人员姓名"},null,8,["modelValue"])]),_:1}),e(s,{label:"出库时间",prop:"outboundTime"},{default:o(()=>[e(W,{modelValue:n.outboundTime,"onUpdate:modelValue":t[10]||(t[10]=l=>n.outboundTime=l),type:"datetime",placeholder:"选择出库时间",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(s,{label:"备注"},{default:o(()=>[e(m,{modelValue:n.remarks,"onUpdate:modelValue":t[11]||(t[11]=l=>n.remarks=l),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},Ye=te(Le,[["__scopeId","data-v-c109b19b"]]);export{Ye as default};
