package com.asset.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 权限配置实体
 */
@Data
@TableName("permission_config")
public class PermissionConfig {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 功能模块名称
     */
    @TableField("module_name")
    private String moduleName;
    
    /**
     * 普通用户权限
     */
    @TableField("user_permission")
    private Boolean userPermission;
    
    /**
     * 管理员权限
     */
    @TableField("admin_permission")
    private Boolean adminPermission;
    
    /**
     * 是否必需权限
     */
    @TableField("required_permission")
    private Boolean requiredPermission;
    
    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}