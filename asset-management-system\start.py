#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资产管理系统 - 快速启动脚本
Asset Management System - Quick Start Script

这是一个简化版的启动脚本，用于快速启动已编译的系统。
如果需要完整的构建功能，请使用 build.py

使用方法:
  python start.py          # 启动完整系统
  python start.py backend  # 仅启动后端
  python start.py frontend # 仅启动前端

作者: 全栈设计大师
版本: 1.0.0
"""

import os
import sys
import subprocess
import time
import signal
import requests
from pathlib import Path
from datetime import datetime

class QuickStarter:
    """快速启动器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.backend_dir = self.project_root / "backend"
        self.frontend_dir = self.project_root / "frontend"
        self.backend_process = None
        self.frontend_process = None
        
    def log(self, message, emoji="ℹ️"):
        """简单日志输出"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] {emoji} {message}")
    
    def start_backend(self):
        """启动后端服务"""
        self.log("启动后端服务...", "🔧")
        try:
            self.backend_process = subprocess.Popen(
                ["mvn", "spring-boot:run"],
                cwd=self.backend_dir,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
            self.log(f"后端服务启动中 (PID: {self.backend_process.pid})", "✅")
            return True
        except Exception as e:
            self.log(f"后端启动失败: {str(e)}", "❌")
            return False
    
    def start_frontend(self):
        """启动前端服务"""
        self.log("启动前端服务...", "🎨")
        try:
            self.frontend_process = subprocess.Popen(
                ["npm", "run", "dev"],
                cwd=self.frontend_dir,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
            self.log(f"前端服务启动中 (PID: {self.frontend_process.pid})", "✅")
            return True
        except Exception as e:
            self.log(f"前端启动失败: {str(e)}", "❌")
            return False
    
    def wait_for_service(self, url, name, timeout=60):
        """等待服务就绪"""
        self.log(f"等待{name}服务就绪...", "⏳")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                response = requests.get(url, timeout=3)
                if response.status_code == 200:
                    self.log(f"{name}服务就绪", "✅")
                    return True
            except:
                pass
            time.sleep(2)
        
        self.log(f"{name}服务启动超时", "❌")
        return False
    
    def stop_services(self):
        """停止服务"""
        self.log("正在停止服务...", "🛑")
        
        if self.backend_process:
            self.backend_process.terminate()
            try:
                self.backend_process.wait(timeout=10)
            except:
                self.backend_process.kill()
        
        if self.frontend_process:
            self.frontend_process.terminate()
            try:
                self.frontend_process.wait(timeout=10)
            except:
                self.frontend_process.kill()
        
        self.log("服务已停止", "✅")
    
    def signal_handler(self, signum, frame):
        """信号处理"""
        self.log("接收到停止信号", "🔄")
        self.stop_services()
        sys.exit(0)
    
    def start_full_system(self):
        """启动完整系统"""
        print("🏢 资产管理系统 - 快速启动")
        print("=" * 50)
        
        # 启动后端
        if not self.start_backend():
            return False
        
        # 启动前端
        if not self.start_frontend():
            self.stop_services()
            return False
        
        # 等待服务就绪
        if not self.wait_for_service("http://localhost:8080/api/health", "后端"):
            self.stop_services()
            return False
        
        if not self.wait_for_service("http://localhost:3000", "前端"):
            self.stop_services()
            return False
        
        # 显示访问信息
        print("\n🎉 系统启动成功！")
        print("📱 访问地址:")
        print("   🏠 系统首页: http://localhost:3000")
        print("   📊 库存统计: http://localhost:3000/statistics")
        print("   👥 用户管理: http://localhost:3000/users")
        print("\n💡 按 Ctrl+C 停止服务")
        print("=" * 50)
        
        # 注册信号处理器并保持运行
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        try:
            while True:
                time.sleep(1)
                # 检查进程状态
                if self.backend_process and self.backend_process.poll() is not None:
                    self.log("后端进程意外退出", "❌")
                    break
                if self.frontend_process and self.frontend_process.poll() is not None:
                    self.log("前端进程意外退出", "❌")
                    break
        except KeyboardInterrupt:
            pass
        
        self.stop_services()
        return True
    
    def start_backend_only(self):
        """仅启动后端"""
        print("🔧 启动后端服务")
        print("=" * 30)
        
        if not self.start_backend():
            return False
        
        if not self.wait_for_service("http://localhost:8080/api/health", "后端"):
            self.stop_services()
            return False
        
        print("\n✅ 后端服务启动成功！")
        print("🔗 API地址: http://localhost:8080/api")
        print("\n💡 按 Ctrl+C 停止服务")
        
        signal.signal(signal.SIGINT, self.signal_handler)
        
        try:
            while True:
                time.sleep(1)
                if self.backend_process and self.backend_process.poll() is not None:
                    self.log("后端进程意外退出", "❌")
                    break
        except KeyboardInterrupt:
            pass
        
        self.stop_services()
        return True
    
    def start_frontend_only(self):
        """仅启动前端"""
        print("🎨 启动前端服务")
        print("=" * 30)
        
        if not self.start_frontend():
            return False
        
        if not self.wait_for_service("http://localhost:3000", "前端"):
            self.stop_services()
            return False
        
        print("\n✅ 前端服务启动成功！")
        print("🔗 访问地址: http://localhost:3000")
        print("\n💡 按 Ctrl+C 停止服务")
        
        signal.signal(signal.SIGINT, self.signal_handler)
        
        try:
            while True:
                time.sleep(1)
                if self.frontend_process and self.frontend_process.poll() is not None:
                    self.log("前端进程意外退出", "❌")
                    break
        except KeyboardInterrupt:
            pass
        
        self.stop_services()
        return True

def main():
    """主函数"""
    starter = QuickStarter()
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "backend":
            success = starter.start_backend_only()
        elif command == "frontend":
            success = starter.start_frontend_only()
        elif command in ["help", "-h", "--help"]:
            print("""
🏢 资产管理系统 - 快速启动脚本

用法:
  python start.py          # 启动完整系统
  python start.py backend  # 仅启动后端服务
  python start.py frontend # 仅启动前端服务
  python start.py help     # 显示帮助信息

注意:
  - 使用前请确保项目已编译
  - 如需完整构建，请使用 build.py
  - 默认端口: 后端8080, 前端3000
            """)
            return
        else:
            print(f"❌ 未知命令: {command}")
            print("使用 'python start.py help' 查看帮助")
            return
    else:
        success = starter.start_full_system()
    
    if not success:
        print("❌ 启动失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
