-- 创建权限配置表
CREATE TABLE permission_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    module_name VARCHAR(100) NOT NULL UNIQUE COMMENT '功能模块名称',
    user_permission BOOLEAN DEFAULT FALSE COMMENT '普通用户权限',
    admin_permission BOOLEAN DEFAULT TRUE COMMENT '管理员权限',
    required_permission BOOLEAN DEFAULT FALSE COMMENT '是否必需权限',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '权限配置表';

-- 插入默认权限配置
INSERT INTO permission_config (module_name, user_permission, admin_permission, required_permission) VALUES
('资产管理', TRUE, TRUE, FALSE),
('资产搜索', TRUE, TRUE, FALSE),
('位置跟踪', TRUE, TRUE, FALSE),
('导入导出', FALSE, TRUE, FALSE),
('资产操作', FALSE, TRUE, FALSE),
('统计报表', FALSE, TRUE, FALSE),
('库存统计', FALSE, TRUE, FALSE),
('用户管理', FALSE, TRUE, TRUE),
('系统设置', FALSE, TRUE, TRUE);