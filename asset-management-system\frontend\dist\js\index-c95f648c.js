import{_ as B}from"./_plugin-vue_export-helper-62491c14.js";/* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                     *//* empty css               *//* empty css               *//* empty css                */import{r as u,o as D,c as N,d as S,f as s,g as t,w as a,E as z,aC as A,F as i,G as T,a8 as c,b3 as I,b4 as M,b5 as R,i as _,S as $,V as j,j as v,z as F,aD as G,n as L,aN as Q,aQ as q}from"./index-2733c819.js";const H={class:"statistics-container"},J={class:"stat-content"},K={class:"stat-icon total"},O={class:"stat-info"},P={class:"stat-number"},U={class:"stat-content"},W={class:"stat-icon active"},X={class:"stat-info"},Y={class:"stat-number"},Z={class:"stat-content"},tt={class:"stat-icon pending"},st={class:"stat-info"},at={class:"stat-number"},et={class:"stat-content"},lt={class:"stat-icon value"},ot={class:"stat-info"},nt={class:"stat-number"},dt={class:"card-header"},it={class:"chart-container"},ct={class:"placeholder-chart"},_t={class:"card-header"},rt={class:"chart-container"},ut={class:"placeholder-chart"},pt={class:"card-header"},vt={__name:"index",setup(ft){const f=u(0),m=u(0),b=u(0),g=u(0),k=u([{category:"计算机设备",total:150,active:120,pending:30,value:15e5,percentage:45.5},{category:"办公设备",total:80,active:70,pending:10,value:4e5,percentage:24.2},{category:"网络设备",total:60,active:55,pending:5,value:8e5,percentage:18.2},{category:"其他设备",total:40,active:35,pending:5,value:2e5,percentage:12.1}]),h=()=>{v.success("图表已刷新")},x=()=>{v.info("导出功能开发中...")},C=w=>{v.info(`查看 ${w.category} 详情功能开发中...`)},E=()=>{f.value=330,m.value=280,b.value=50,g.value=29e5};return D(()=>{E()}),(w,e)=>{const n=F,l=z,d=G,y=A,p=L,o=Q,V=q;return N(),S("div",H,[e[13]||(e[13]=s("div",{class:"page-header"},[s("h2",null,"统计报表"),s("p",null,"查看资产统计数据和分析报表")],-1)),t(y,{gutter:20,class:"stats-cards"},{default:a(()=>[t(d,{span:6},{default:a(()=>[t(l,{class:"stat-card"},{default:a(()=>[s("div",J,[s("div",K,[t(n,null,{default:a(()=>[t(i(T))]),_:1})]),s("div",O,[s("div",P,c(f.value),1),e[0]||(e[0]=s("div",{class:"stat-label"},"总资产数",-1))])])]),_:1})]),_:1}),t(d,{span:6},{default:a(()=>[t(l,{class:"stat-card"},{default:a(()=>[s("div",U,[s("div",W,[t(n,null,{default:a(()=>[t(i(I))]),_:1})]),s("div",X,[s("div",Y,c(m.value),1),e[1]||(e[1]=s("div",{class:"stat-label"},"在用资产",-1))])])]),_:1})]),_:1}),t(d,{span:6},{default:a(()=>[t(l,{class:"stat-card"},{default:a(()=>[s("div",Z,[s("div",tt,[t(n,null,{default:a(()=>[t(i(M))]),_:1})]),s("div",st,[s("div",at,c(b.value),1),e[2]||(e[2]=s("div",{class:"stat-label"},"待处理",-1))])])]),_:1})]),_:1}),t(d,{span:6},{default:a(()=>[t(l,{class:"stat-card"},{default:a(()=>[s("div",et,[s("div",lt,[t(n,null,{default:a(()=>[t(i(R))]),_:1})]),s("div",ot,[s("div",nt,"¥"+c(g.value),1),e[3]||(e[3]=s("div",{class:"stat-label"},"总价值",-1))])])]),_:1})]),_:1})]),_:1}),t(y,{gutter:20,class:"charts-section"},{default:a(()=>[t(d,{span:12},{default:a(()=>[t(l,null,{header:a(()=>[s("div",dt,[e[5]||(e[5]=s("span",null,"资产状态分布",-1)),t(p,{link:"",onClick:h},{default:a(()=>e[4]||(e[4]=[_("刷新",-1)])),_:1,__:[4]})])]),default:a(()=>[s("div",it,[s("div",ct,[t(n,{size:"48"},{default:a(()=>[t(i($))]),_:1}),e[6]||(e[6]=s("p",null,"图表功能开发中...",-1))])])]),_:1})]),_:1}),t(d,{span:12},{default:a(()=>[t(l,null,{header:a(()=>[s("div",_t,[e[8]||(e[8]=s("span",null,"资产类型分布",-1)),t(p,{link:"",onClick:h},{default:a(()=>e[7]||(e[7]=[_("刷新",-1)])),_:1,__:[7]})])]),default:a(()=>[s("div",rt,[s("div",ut,[t(n,{size:"48"},{default:a(()=>[t(i(j))]),_:1}),e[9]||(e[9]=s("p",null,"图表功能开发中...",-1))])])]),_:1})]),_:1})]),_:1}),t(l,{class:"table-section"},{header:a(()=>[s("div",pt,[e[11]||(e[11]=s("span",null,"详细统计",-1)),s("div",null,[t(p,{type:"primary",onClick:x},{default:a(()=>e[10]||(e[10]=[_("导出报表",-1)])),_:1,__:[10]})])])]),default:a(()=>[t(V,{data:k.value,style:{width:"100%"}},{default:a(()=>[t(o,{prop:"category",label:"分类",width:"150"}),t(o,{prop:"total",label:"总数",width:"100"}),t(o,{prop:"active",label:"在用",width:"100"}),t(o,{prop:"pending",label:"待处理",width:"100"}),t(o,{prop:"value",label:"总价值",width:"150"},{default:a(r=>[_(" ¥"+c(r.row.value.toLocaleString()),1)]),_:1}),t(o,{prop:"percentage",label:"占比",width:"100"},{default:a(r=>[_(c(r.row.percentage)+"% ",1)]),_:1}),t(o,{label:"操作"},{default:a(r=>[t(p,{link:"",onClick:mt=>C(r.row)},{default:a(()=>e[12]||(e[12]=[_("查看详情",-1)])),_:2,__:[12]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})])}}},Vt=B(vt,[["__scopeId","data-v-a2e22bac"]]);export{Vt as default};
