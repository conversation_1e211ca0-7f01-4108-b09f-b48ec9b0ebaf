#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资产管理系统 - 前后端一体化构建脚本
将前端和后端打包成一个JAR文件，可直接运行

使用方法:
  python build_all.py

生成文件:
  backend/target/asset-management-system.jar

运行方法:
  java -jar backend/target/asset-management-system.jar

作者: 全栈设计大师
版本: 1.0.0
"""

import os
import sys
import subprocess
import time
from pathlib import Path
from datetime import datetime

def log(message, emoji="ℹ️"):
    """日志输出"""
    timestamp = datetime.now().strftime('%H:%M:%S')
    print(f"[{timestamp}] {emoji} {message}")

def run_command(command, cwd, description):
    """执行命令"""
    log(f"执行: {description}", "🔧")
    try:
        result = subprocess.run(
            command,
            cwd=cwd,
            capture_output=True,
            text=True,
            timeout=300  # 5分钟超时
        )
        
        if result.returncode == 0:
            log(f"{description} - 成功", "✅")
            return True
        else:
            log(f"{description} - 失败", "❌")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        log(f"{description} - 超时", "❌")
        return False
    except Exception as e:
        log(f"{description} - 异常: {str(e)}", "❌")
        return False

def main():
    """主函数"""
    project_root = Path(__file__).parent.absolute()
    backend_dir = project_root / "backend"
    frontend_dir = project_root / "frontend"
    
    # 自定义工具路径
    maven_cmd = r"D:\cursor\apache-maven-3.9.9\bin\mvn.cmd"
    npm_cmd = r"D:\cursor\nodejs\npm.cmd"
    
    print("🏗️ 资产管理系统 - 前后端一体化构建")
    print("=" * 60)
    log("开始构建前后端一体化应用", "🚀")
    
    # 步骤1: 清理之前的构建
    log("清理之前的构建文件", "🧹")
    if not run_command([maven_cmd, "clean"], backend_dir, "清理后端项目"):
        return False
    
    # 步骤2: 安装前端依赖（如果需要）
    node_modules = frontend_dir / "node_modules"
    if not node_modules.exists():
        log("检测到前端依赖缺失，开始安装", "📦")
        if not run_command([npm_cmd, "install"], frontend_dir, "安装前端依赖"):
            return False
    else:
        log("前端依赖已存在，跳过安装", "✅")
    
    # 步骤3: 构建前端
    if not run_command([npm_cmd, "run", "build"], frontend_dir, "构建前端项目"):
        return False
    
    # 步骤4: 打包后端（包含前端）
    log("开始打包前后端一体化应用", "📦")
    if not run_command([maven_cmd, "package", "-DskipTests"], backend_dir, "打包一体化应用"):
        return False
    
    # 步骤5: 检查生成的JAR文件
    jar_file = backend_dir / "target" / "asset-management-backend-1.0.0.jar"
    if jar_file.exists():
        file_size = jar_file.stat().st_size / (1024 * 1024)  # MB
        log(f"JAR文件生成成功: {jar_file}", "🎉")
        log(f"文件大小: {file_size:.1f} MB", "📊")
        
        # 创建启动脚本
        create_run_scripts(jar_file)
        
        print("\n" + "=" * 60)
        print("🎉 构建完成！")
        print("\n📁 生成文件:")
        print(f"   JAR文件: {jar_file}")
        print(f"   启动脚本: run.bat (Windows) / run.sh (Linux)")
        
        print("\n🚀 运行方法:")
        print("   方法1: java -jar backend/target/asset-management-backend-1.0.0.jar")
        print("   方法2: 双击 run.bat (Windows)")
        print("   方法3: ./run.sh (Linux)")
        
        print("\n🌐 访问地址:")
        print("   前端页面: http://localhost:8080")
        print("   后端API: http://localhost:8080/api")
        
        print("\n💡 特性:")
        print("   ✅ 前后端合并为单个JAR文件")
        print("   ✅ 支持Vue Router的History模式")
        print("   ✅ 静态资源自动处理")
        print("   ✅ 一键启动，无需额外配置")
        
        return True
    else:
        log("JAR文件生成失败", "❌")
        return False

def create_run_scripts(jar_file):
    """创建启动脚本"""
    project_root = Path(__file__).parent.absolute()
    
    # Windows启动脚本
    bat_content = f"""@echo off
chcp 65001 >nul
echo 🚀 启动资产管理系统...
echo.
echo 📱 访问地址: http://localhost:8080
echo 🔧 后端API: http://localhost:8080/api
echo.
echo 💡 按 Ctrl+C 停止服务
echo.
java -jar "{jar_file}"
pause
"""
    
    bat_file = project_root / "run.bat"
    with open(bat_file, 'w', encoding='utf-8') as f:
        f.write(bat_content)
    
    # Linux启动脚本
    sh_content = f"""#!/bin/bash
echo "🚀 启动资产管理系统..."
echo ""
echo "📱 访问地址: http://localhost:8080"
echo "🔧 后端API: http://localhost:8080/api"
echo ""
echo "💡 按 Ctrl+C 停止服务"
echo ""
java -jar "{jar_file}"
"""
    
    sh_file = project_root / "run.sh"
    with open(sh_file, 'w', encoding='utf-8') as f:
        f.write(sh_content)
    
    # 给Linux脚本添加执行权限
    try:
        os.chmod(sh_file, 0o755)
    except:
        pass
    
    log("启动脚本创建完成", "✅")

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ 构建失败")
            input("按回车键退出...")
            sys.exit(1)
        else:
            input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，退出程序")
    except Exception as e:
        print(f"\n❌ 构建异常: {e}")
        input("按回车键退出...")
        sys.exit(1)
