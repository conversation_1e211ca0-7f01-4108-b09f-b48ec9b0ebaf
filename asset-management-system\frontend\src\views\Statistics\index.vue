<template>
  <div class="statistics-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>统计报表</h2>
      <p>查看资产统计数据和分析报表</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><Box /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ totalAssets }}</div>
              <div class="stat-label">总资产数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon active">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ activeAssets }}</div>
              <div class="stat-label">在用资产</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ pendingAssets }}</div>
              <div class="stat-label">待处理</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon value">
              <el-icon><Money /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">¥{{ totalValue }}</div>
              <div class="stat-label">总价值</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-section">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>资产状态分布</span>
              <el-button link @click="refreshChart">刷新</el-button>
            </div>
          </template>
          <div class="chart-container">
            <div class="placeholder-chart">
              <el-icon size="48"><DataAnalysis /></el-icon>
              <p>图表功能开发中...</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>资产类型分布</span>
              <el-button link @click="refreshChart">刷新</el-button>
            </div>
          </template>
          <div class="chart-container">
            <div class="placeholder-chart">
              <el-icon size="48"><PieChart /></el-icon>
              <p>图表功能开发中...</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据表格 -->
    <el-card class="table-section">
      <template #header>
        <div class="card-header">
          <span>详细统计</span>
          <div>
            <el-button type="primary" @click="exportReport">导出报表</el-button>
          </div>
        </div>
      </template>
      
      <el-table :data="statisticsData" style="width: 100%">
        <el-table-column prop="category" label="分类" width="150" />
        <el-table-column prop="total" label="总数" width="100" />
        <el-table-column prop="active" label="在用" width="100" />
        <el-table-column prop="pending" label="待处理" width="100" />
        <el-table-column prop="value" label="总价值" width="150">
          <template #default="scope">
            ¥{{ scope.row.value.toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="percentage" label="占比" width="100">
          <template #default="scope">
            {{ scope.row.percentage }}%
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button link @click="viewDetails(scope.row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Box, Check, Clock, Money, DataAnalysis, PieChart } from '@element-plus/icons-vue'

// 响应式数据
const totalAssets = ref(0)
const activeAssets = ref(0)
const pendingAssets = ref(0)
const totalValue = ref(0)

const statisticsData = ref([
  {
    category: '计算机设备',
    total: 150,
    active: 120,
    pending: 30,
    value: 1500000,
    percentage: 45.5
  },
  {
    category: '办公设备',
    total: 80,
    active: 70,
    pending: 10,
    value: 400000,
    percentage: 24.2
  },
  {
    category: '网络设备',
    total: 60,
    active: 55,
    pending: 5,
    value: 800000,
    percentage: 18.2
  },
  {
    category: '其他设备',
    total: 40,
    active: 35,
    pending: 5,
    value: 200000,
    percentage: 12.1
  }
])

// 方法
const refreshChart = () => {
  ElMessage.success('图表已刷新')
}

const exportReport = () => {
  ElMessage.info('导出功能开发中...')
}

const viewDetails = (row) => {
  ElMessage.info(`查看 ${row.category} 详情功能开发中...`)
}

const loadStatistics = () => {
  // 模拟数据加载
  totalAssets.value = 330
  activeAssets.value = 280
  pendingAssets.value = 50
  totalValue.value = 2900000
}

onMounted(() => {
  loadStatistics()
})
</script>

<style scoped lang="scss">
.statistics-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
  
  h2 {
    margin: 0 0 8px 0;
    color: #303133;
  }
  
  p {
    margin: 0;
    color: #909399;
    font-size: 14px;
  }
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  .stat-content {
    display: flex;
    align-items: center;
    
    .stat-icon {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      
      .el-icon {
        font-size: 24px;
        color: white;
      }
      
      &.total {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
      
      &.active {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }
      
      &.pending {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }
      
      &.value {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      }
    }
    
    .stat-info {
      .stat-number {
        font-size: 28px;
        font-weight: bold;
        color: #303133;
        line-height: 1;
        margin-bottom: 4px;
      }
      
      .stat-label {
        font-size: 14px;
        color: #909399;
      }
    }
  }
}

.charts-section {
  margin-bottom: 20px;
}

.chart-container {
  height: 300px;
  
  .placeholder-chart {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #909399;
    
    p {
      margin-top: 12px;
      font-size: 14px;
    }
  }
}

.table-section {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
