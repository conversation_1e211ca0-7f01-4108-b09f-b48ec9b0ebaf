<template>
  <div class="statistics-by-type">
    <div class="page-header">
      <h2>分类统计</h2>
      <p>按产品类型统计资产分布情况</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon server">
              <el-icon size="32"><Box /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ typeStats.server || 0 }}</div>
              <div class="stat-label">服务器</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon network">
              <el-icon size="32"><Connection /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ typeStats.network || 0 }}</div>
              <div class="stat-label">网络设备</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon storage">
              <el-icon size="32"><FolderOpened /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ typeStats.storage || 0 }}</div>
              <div class="stat-label">存储设备</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon other">
              <el-icon size="32"><MoreFilled /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ typeStats.other || 0 }}</div>
              <div class="stat-label">其他设备</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :xs="24" :lg="12">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>产品类型分布</span>
              <el-button-group>
                <el-button size="small" @click="refreshChart">刷新</el-button>
                <el-button size="small" @click="exportChart">导出</el-button>
              </el-button-group>
            </div>
          </template>
          <div ref="pieChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :xs="24" :lg="12">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>类型趋势分析</span>
              <el-select v-model="timeRange" size="small" style="width: 120px">
                <el-option label="最近7天" value="7d" />
                <el-option label="最近30天" value="30d" />
                <el-option label="最近90天" value="90d" />
              </el-select>
            </div>
          </template>
          <div ref="lineChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细数据表格 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>详细统计数据</span>
          <el-button type="primary" size="small" @click="exportData">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </div>
      </template>

      <el-table v-loading="loading" :data="tableData" style="width: 100%">
        <el-table-column prop="productType" label="产品类型" width="150" />
        <el-table-column prop="totalCount" label="总数量" width="100" align="center" />
        <el-table-column prop="pendingCount" label="待入库" width="100" align="center">
          <template #default="{ row }">
            <el-tag type="warning" size="small">{{ row.pendingCount }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="receivedCount" label="已入库" width="100" align="center">
          <template #default="{ row }">
            <el-tag type="success" size="small">{{ row.receivedCount }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="installedCount" label="已安装" width="100" align="center">
          <template #default="{ row }">
            <el-tag type="info" size="small">{{ row.installedCount }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="outboundCount" label="已出库" width="100" align="center">
          <template #default="{ row }">
            <el-tag type="danger" size="small">{{ row.outboundCount }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="totalValue" label="总价值" width="120" align="right">
          <template #default="{ row }">
            ¥{{ formatCurrency(row.totalValue) }}
          </template>
        </el-table-column>
        <el-table-column prop="percentage" label="占比" width="100" align="center">
          <template #default="{ row }">
            {{ row.percentage }}%
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewDetails(row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Box, Connection, FolderOpened, MoreFilled, Download } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 响应式数据
const loading = ref(false)
const timeRange = ref('30d')
const pieChartRef = ref()
const lineChartRef = ref()
const pieChart = ref(null)
const lineChart = ref(null)

// 统计数据
const typeStats = reactive({
  server: 45,
  network: 32,
  storage: 28,
  other: 15
})

const tableData = ref([
  {
    productType: '服务器',
    totalCount: 45,
    pendingCount: 5,
    receivedCount: 15,
    installedCount: 20,
    outboundCount: 5,
    totalValue: 2250000,
    percentage: 37.5
  },
  {
    productType: '网络设备',
    totalCount: 32,
    pendingCount: 3,
    receivedCount: 10,
    installedCount: 15,
    outboundCount: 4,
    totalValue: 960000,
    percentage: 26.7
  },
  {
    productType: '存储设备',
    totalCount: 28,
    pendingCount: 2,
    receivedCount: 8,
    installedCount: 15,
    outboundCount: 3,
    totalValue: 1400000,
    percentage: 23.3
  },
  {
    productType: '其他设备',
    totalCount: 15,
    pendingCount: 1,
    receivedCount: 5,
    installedCount: 8,
    outboundCount: 1,
    totalValue: 300000,
    percentage: 12.5
  }
])

// 方法
const initCharts = () => {
  nextTick(() => {
    initPieChart()
    initLineChart()
  })
}

const initPieChart = () => {
  if (!pieChartRef.value) return
  
  pieChart.value = echarts.init(pieChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '产品类型',
        type: 'pie',
        radius: '50%',
        data: [
          { value: typeStats.server, name: '服务器' },
          { value: typeStats.network, name: '网络设备' },
          { value: typeStats.storage, name: '存储设备' },
          { value: typeStats.other, name: '其他设备' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  pieChart.value.setOption(option)
}

const initLineChart = () => {
  if (!lineChartRef.value) return
  
  lineChart.value = echarts.init(lineChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['服务器', '网络设备', '存储设备', '其他设备']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '服务器',
        type: 'line',
        stack: 'Total',
        data: [12, 15, 18, 22, 28, 35, 45]
      },
      {
        name: '网络设备',
        type: 'line',
        stack: 'Total',
        data: [8, 12, 15, 18, 22, 28, 32]
      },
      {
        name: '存储设备',
        type: 'line',
        stack: 'Total',
        data: [5, 8, 12, 15, 20, 25, 28]
      },
      {
        name: '其他设备',
        type: 'line',
        stack: 'Total',
        data: [2, 4, 6, 8, 10, 12, 15]
      }
    ]
  }
  
  lineChart.value.setOption(option)
}

const refreshChart = () => {
  ElMessage.success('图表已刷新')
  initCharts()
}

const exportChart = () => {
  if (pieChart.value) {
    const url = pieChart.value.getDataURL({
      pixelRatio: 2,
      backgroundColor: '#fff'
    })
    const link = document.createElement('a')
    link.href = url
    link.download = '产品类型分布图.png'
    link.click()
  }
}

const exportData = () => {
  ElMessage.success('数据导出功能开发中...')
}

const viewDetails = (row) => {
  ElMessage.info(`查看 ${row.productType} 的详细信息`)
}

const formatCurrency = (value) => {
  return new Intl.NumberFormat('zh-CN').format(value)
}

const loadData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('数据加载完成')
  } catch (error) {
    ElMessage.error('加载数据失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadData()
  initCharts()
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    pieChart.value?.resize()
    lineChart.value?.resize()
  })
})
</script>

<style scoped>
.statistics-by-type {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  margin-bottom: 20px;
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
}

.stat-icon.server {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.network {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.storage {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.other {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.chart-row {
  margin-bottom: 20px;
}

.chart-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
  width: 100%;
}

.table-card {
  margin-bottom: 20px;
}

/* 响应式设计 */
@media (min-width: 1920px) {
  .statistics-by-type {
    padding: 24px;
  }
  
  .chart-container {
    height: 350px;
  }
  
  .stat-value {
    font-size: 28px;
  }
}

@media (min-width: 2560px) {
  .statistics-by-type {
    padding: 32px;
  }
  
  .page-header h2 {
    font-size: 28px;
  }
  
  .page-header p {
    font-size: 16px;
  }
  
  .chart-container {
    height: 400px;
  }
  
  .stat-value {
    font-size: 32px;
  }
}
</style>
