<template>
  <div class="statistics-by-location">
    <div class="page-header">
      <h2>位置统计</h2>
      <p>按位置查看资产分布和统计信息</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="32" color="#409EFF">
                <LocationFilled />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ totalLocations }}</div>
              <div class="stat-label">总位置数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="32" color="#67C23A">
                <Box />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ totalAssets }}</div>
              <div class="stat-label">总资产数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="32" color="#E6A23C">
                <Money />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">¥{{ formatMoney(totalValue) }}</div>
              <div class="stat-label">总价值</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="32" color="#F56C6C">
                <TrendCharts />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">¥{{ formatMoney(avgValue) }}</div>
              <div class="stat-label">平均价值</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 位置统计图表 -->
    <el-row :gutter="20" class="charts-section">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>位置资产数量分布</span>
            </div>
          </template>
          <div ref="quantityChartRef" style="height: 400px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>位置资产价值分布</span>
            </div>
          </template>
          <div ref="valueChartRef" style="height: 400px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细数据表格 -->
    <el-card class="table-section">
      <template #header>
        <div class="card-header">
          <span>位置详细统计</span>
          <el-button type="primary" @click="exportData">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </div>
      </template>
      
      <el-table :data="locationData" stripe style="width: 100%">
        <el-table-column prop="location" label="位置" width="200" />
        <el-table-column prop="totalCount" label="资产总数" width="120" align="center" />
        <el-table-column prop="pendingCount" label="待处理" width="100" align="center" />
        <el-table-column prop="receivedCount" label="已入库" width="100" align="center" />
        <el-table-column prop="installedCount" label="已安装" width="100" align="center" />
        <el-table-column prop="outboundCount" label="已出库" width="100" align="center" />
        <el-table-column prop="scrappedCount" label="已报废" width="100" align="center" />
        <el-table-column prop="totalValue" label="总价值" width="150" align="right">
          <template #default="{ row }">
            ¥{{ formatMoney(row.totalValue) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="viewDetails(row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { init } from 'echarts'
import { ElMessage } from 'element-plus'
import { getCompleteInventoryReport } from '@/api/inventory'
import { LocationFilled, Box, Money, TrendCharts, Download } from '@element-plus/icons-vue'

const router = useRouter()
const quantityChartRef = ref()
const valueChartRef = ref()
let quantityChart = null
let valueChart = null

// 统计数据
const totalLocations = ref(0)
const totalAssets = ref(0)
const totalValue = ref(0)
const avgValue = ref(0)

// 位置数据
const locationData = ref([])

const formatMoney = (value) => {
  if (!value) return '0.00'
  return Number(value).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

const initQuantityChart = () => {
  if (!quantityChartRef.value || !locationData.value.length) return
  
  quantityChart = init(quantityChartRef.value)
  
  const data = locationData.value.slice(0, 10).map(item => ({
    name: item.location || '未知位置',
    value: item.totalCount || 0
  }))
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: data.map(item => item.name)
    },
    series: [{
      name: '资产数量',
      type: 'bar',
      data: data.map(item => item.value),
      itemStyle: { color: '#409EFF' }
    }]
  }
  
  quantityChart.setOption(option)
}

const initValueChart = () => {
  if (!valueChartRef.value || !locationData.value.length) return
  
  valueChart = init(valueChartRef.value)
  
  const data = locationData.value.slice(0, 10).map(item => ({
    name: item.location || '未知位置',
    value: item.totalValue || 0
  }))
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: ¥{c}'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [{
      name: '资产价值',
      type: 'pie',
      radius: '50%',
      data: data,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }
  
  valueChart.setOption(option)
}

const loadData = async () => {
  try {
    const response = await getCompleteInventoryReport()
    const data = response.data
    
    if (data.locationInventory) {
      locationData.value = data.locationInventory
      
      // 计算统计数据
      totalLocations.value = locationData.value.length
      totalAssets.value = locationData.value.reduce((sum, item) => sum + (item.totalCount || 0), 0)
      totalValue.value = locationData.value.reduce((sum, item) => sum + (item.totalValue || 0), 0)
      avgValue.value = totalAssets.value > 0 ? totalValue.value / totalAssets.value : 0
      
      // 初始化图表
      await nextTick()
      initQuantityChart()
      initValueChart()
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  }
}

const exportData = () => {
  ElMessage.info('导出功能开发中...')
}

const viewDetails = (row) => {
  ElMessage.info(`查看 ${row.location} 详情功能开发中...`)
}

onMounted(() => {
  loadData()
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    quantityChart?.resize()
    valueChart?.resize()
  })
})
</script>

<style scoped>
.statistics-by-location {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  margin-right: 16px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.charts-section {
  margin-bottom: 20px;
}

.table-section {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
