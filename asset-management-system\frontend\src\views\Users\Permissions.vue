<template>
  <div class="permissions-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <el-page-header @back="handleBack">
        <template #content>
          <span class="page-title">权限管理</span>
        </template>
      </el-page-header>
      <p class="page-description">管理系统的角色和权限配置</p>
    </div>

    <!-- 角色权限表 -->
    <el-card title="角色权限配置">
      <template #header>
        <div class="card-header">
          <span>角色权限配置</span>
          <el-button type="primary" @click="handleSave" :loading="saveLoading">
            <el-icon><Check /></el-icon>
            保存配置
          </el-button>
        </div>
      </template>

      <el-table :data="rolePermissions" style="width: 100%">
        <el-table-column prop="module" label="功能模块" width="200">
          <template #default="scope">
            <div class="module-cell">
              <el-icon :color="scope.row.icon.color">
                <component :is="scope.row.icon.name" />
              </el-icon>
              <span>{{ scope.row.module }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="description" label="功能描述" />

        <el-table-column label="普通用户" width="120" align="center">
          <template #default="scope">
            <el-switch
              v-model="scope.row.userPermission"
              :disabled="scope.row.required"
              @change="handlePermissionChange"
            />
          </template>
        </el-table-column>

        <el-table-column label="管理员" width="120" align="center">
          <template #default="scope">
            <el-switch
              v-model="scope.row.adminPermission"
              disabled
              :model-value="true"
            />
            <div class="permission-note">默认全部权限</div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 用户角色统计 -->
    <el-row :gutter="24" style="margin-top: 24px">
      <el-col :span="12">
        <el-card title="用户角色分布">
          <template #header>
            <span>用户角色分布</span>
          </template>
          
          <div class="role-stats">
            <div class="stat-item">
              <div class="stat-icon admin">
                <el-icon><UserFilled /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ userStats.adminUsers || 0 }}</div>
                <div class="stat-label">管理员</div>
              </div>
            </div>

            <div class="stat-item">
              <div class="stat-icon user">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ (userStats.totalUsers || 0) - (userStats.adminUsers || 0) }}</div>
                <div class="stat-label">普通用户</div>
              </div>
            </div>

            <div class="stat-item">
              <div class="stat-icon total">
                <el-icon><Group /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ userStats.totalUsers || 0 }}</div>
                <div class="stat-label">总用户数</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card title="权限说明">
          <template #header>
            <span>权限说明</span>
          </template>
          
          <div class="permission-notes">
            <div class="note-item">
              <el-tag type="danger" size="small">管理员</el-tag>
              <span>拥有系统所有功能的完整权限，包括用户管理、系统配置等</span>
            </div>
            
            <div class="note-item">
              <el-tag type="primary" size="small">普通用户</el-tag>
              <span>根据配置享有特定功能权限，默认只能管理自己的资产</span>
            </div>
            
            <div class="note-item">
              <el-tag type="warning" size="small">注意</el-tag>
              <span>权限变更会在用户下次登录时生效</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Check, UserFilled, User, Group } from '@element-plus/icons-vue'
import { getUserStatistics } from '@/api/user'

const router = useRouter()

// 保存加载状态
const saveLoading = ref(false)

// 用户统计数据
const userStats = reactive({
  totalUsers: 0,
  adminUsers: 0,
  activeUsers: 0,
  inactiveUsers: 0
})

// 角色权限配置
const rolePermissions = ref([
  {
    module: '资产管理',
    description: '查看、创建、编辑、删除资产信息',
    icon: { name: 'Box', color: '#409eff' },
    userPermission: true,
    adminPermission: true,
    required: false
  },
  {
    module: '资产搜索',
    description: '使用高级搜索功能查找资产',
    icon: { name: 'Search', color: '#67c23a' },
    userPermission: true,
    adminPermission: true,
    required: false
  },
  {
    module: '位置跟踪',
    description: '跟踪和管理资产位置变更',
    icon: { name: 'LocationFilled', color: '#e6a23c' },
    userPermission: true,
    adminPermission: true,
    required: false
  },
  {
    module: '导入导出',
    description: '批量导入导出资产数据',
    icon: { name: 'Upload', color: '#f56c6c' },
    userPermission: false,
    adminPermission: true,
    required: false
  },
  {
    module: '资产操作',
    description: '入库、安装、出库等资产流程操作',
    icon: { name: 'Operation', color: '#909399' },
    userPermission: false,
    adminPermission: true,
    required: false
  },
  {
    module: '统计报表',
    description: '查看各种统计图表和报表',
    icon: { name: 'DataAnalysis', color: '#722ed1' },
    userPermission: false,
    adminPermission: true,
    required: false
  },
  {
    module: '库存统计',
    description: '查看详细的库存统计信息',
    icon: { name: 'DataBoard', color: '#13c2c2' },
    userPermission: false,
    adminPermission: true,
    required: false
  },
  {
    module: '用户管理',
    description: '管理系统用户账号和权限',
    icon: { name: 'User', color: '#eb2f96' },
    userPermission: false,
    adminPermission: true,
    required: true
  },
  {
    module: '系统设置',
    description: '配置系统参数和产品类型',
    icon: { name: 'Setting', color: '#fa8c16' },
    userPermission: false,
    adminPermission: true,
    required: true
  }
])

// 权限变更处理
const handlePermissionChange = () => {
  // 这里可以添加权限变更的实时保存逻辑
  console.log('权限配置已变更')
}

// 保存权限配置
const handleSave = async () => {
  saveLoading.value = true
  try {
    // 模拟保存权限配置
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 这里应该调用实际的API保存权限配置
    // await savePermissionConfig(rolePermissions.value)
    
    ElMessage.success('权限配置保存成功')
  } catch (error) {
    ElMessage.error('保存失败: ' + error.message)
  } finally {
    saveLoading.value = false
  }
}

// 加载用户统计数据
const loadUserStats = async () => {
  try {
    const response = await getUserStatistics()
    Object.assign(userStats, response.data)
  } catch (error) {
    console.error('加载用户统计失败:', error)
  }
}

// 返回用户管理页
const handleBack = () => {
  router.push('/users')
}

onMounted(() => {
  loadUserStats()
})
</script>

<style scoped lang="scss">
.permissions-container {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
}

.page-description {
  margin: 8px 0 0 0;
  color: #666;
  font-size: 14px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.module-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.permission-note {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.role-stats {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  background: #f8f9fa;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  
  &.admin {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  &.user {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }
  
  &.total {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 2px;
}

.permission-notes {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.note-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  line-height: 1.6;
  
  span {
    flex: 1;
    font-size: 14px;
    color: #666;
  }
}
</style>