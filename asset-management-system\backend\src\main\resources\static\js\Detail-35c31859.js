import{_ as K}from"./_plugin-vue_export-helper-62491c14.js";/* empty css                   *//* empty css               *//* empty css                *//* empty css                *//* empty css               */import{aT as S,a as ne,r as A,s as z,b as ie,t as ue,c as d,X as b,w as o,f as e,g as a,i as k,d as f,a2 as C,$ as m,a3 as G,j as T,k as re,l as de,aU as ce,aL as pe,aM as me,p as ve,n as Q,aV as fe,q as _e,u as be,o as ye,F as ge,a8 as c,H as ke,y as Ve,aC as De,aS as we,aW as Ee,x as X,aB as J,aO as Ae,E as he,aX as Ye,aY as Ce,aD as Te,aE as Le,aa as Re,z as Ie}from"./index-2733c819.js";import{c as Me,e as Oe,d as Ue}from"./asset-f5b5b286.js";/* empty css                   *//* empty css                 *//* empty css                  *//* empty css                     *//* empty css                       */function xe(v,y){return S({url:`/assets/${v}/receive`,method:"post",data:y})}function Se(v,y){return S({url:`/assets/${v}/install`,method:"post",data:y})}function He(v,y){return S({url:`/assets/${v}/move`,method:"post",data:y})}function Ne(v,y){return S({url:`/assets/${v}/outbound`,method:"post",data:y})}function qe(v,y){return S({url:`/assets/${v}/reactivate`,method:"post",data:y})}const Be={class:"dialog-footer"},$e={__name:"index",props:{modelValue:{type:Boolean,default:!1},operationType:{type:String,required:!0},assetInfo:{type:Object,default:()=>({})}},emits:["update:modelValue","success"],setup(v,{emit:y}){const L=ne(),_=v,l=y,h=A(),w=A(!1),R=A([]),M=z({get:()=>_.modelValue,set:r=>l("update:modelValue",r)}),q=z(()=>({receive:"资产入库",install:"资产安装",move:"位置变更",outbound:"资产出库",reactivate:"重新激活"})[_.operationType]||"资产操作"),n=ie({operator:"",reason:"",receiver:"",receivedAt:"",installer:"",installLocation:"",installedAt:"",toLocation:"",moveReason:"",outboundReason:"",outboundOperator:"",outboundAt:""}),B=z(()=>{const r={operator:[{required:!0,message:"请输入操作人员",trigger:"blur"}]};switch(_.operationType){case"receive":r.receiver=[{required:!0,message:"请输入签收人",trigger:"blur"}];break;case"install":r.installer=[{required:!0,message:"请输入安装人员",trigger:"blur"}],r.installLocation=[{required:!0,message:"请选择安装位置",trigger:"change"}];break;case"move":r.toLocation=[{required:!0,message:"请选择目标位置",trigger:"change"}],r.moveReason=[{required:!0,message:"请输入变更原因",trigger:"blur"}];break;case"outbound":r.outboundReason=[{required:!0,message:"请选择出库原因",trigger:"change"}],r.outboundOperator=[{required:!0,message:"请输入操作人员",trigger:"blur"}];break;case"reactivate":r.receiver=[{required:!0,message:"请输入签收人",trigger:"blur"}];break}return r});ue(()=>_.modelValue,r=>{r&&(I(),$())});const I=()=>{var r,s;switch(Object.keys(n).forEach(g=>{n[g]=""}),n.operator=((r=L.user)==null?void 0:r.realName)||((s=L.user)==null?void 0:s.username)||"",_.operationType){case"receive":n.receiver=n.operator;break;case"install":n.installer=n.operator;break;case"outbound":n.outboundOperator=n.operator;break;case"reactivate":n.receiver=n.operator;break}},$=async()=>{try{const{data:r}=await Me();R.value=r||[]}catch(r){console.error("加载位置选项失败:",r)}},P=async()=>{if(h.value)try{await h.value.validate(),w.value=!0;const r={...n};let s;switch(_.operationType){case"receive":s=await xe(_.assetInfo.id,r);break;case"install":s=await Se(_.assetInfo.id,r);break;case"move":s=await He(_.assetInfo.id,r);break;case"outbound":s=await Ne(_.assetInfo.id,r);break;case"reactivate":s=await qe(_.assetInfo.id,r);break;default:throw new Error("不支持的操作类型")}T.success(s.message||"操作成功"),l("success",s.data),O()}catch(r){if(r.errors)return;T.error(r.message||"操作失败")}finally{w.value=!1}},O=()=>{M.value=!1,h.value&&h.value.resetFields()};return(r,s)=>{const g=re,p=de,V=ce,D=pe,U=me,F=ve,H=Q,j=fe;return d(),b(j,{modelValue:M.value,"onUpdate:modelValue":s[13]||(s[13]=i=>M.value=i),title:q.value,width:"600px","close-on-click-modal":!1,onClose:O},{footer:o(()=>[e("div",Be,[a(H,{onClick:O},{default:o(()=>s[14]||(s[14]=[k("取消",-1)])),_:1,__:[14]}),a(H,{type:"primary",onClick:P,loading:w.value},{default:o(()=>s[15]||(s[15]=[k(" 确定 ",-1)])),_:1,__:[15]},8,["loading"])])]),default:o(()=>[a(F,{ref_key:"formRef",ref:h,model:n,rules:B.value,"label-width":"100px",size:"default"},{default:o(()=>[v.operationType==="receive"?(d(),f(C,{key:0},[a(p,{label:"签收人",prop:"receiver"},{default:o(()=>[a(g,{modelValue:n.receiver,"onUpdate:modelValue":s[0]||(s[0]=i=>n.receiver=i),placeholder:"请输入签收人",clearable:""},null,8,["modelValue"])]),_:1}),a(p,{label:"入库时间",prop:"receivedAt"},{default:o(()=>[a(V,{modelValue:n.receivedAt,"onUpdate:modelValue":s[1]||(s[1]=i=>n.receivedAt=i),type:"datetime",placeholder:"选择入库时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",class:"full-width"},null,8,["modelValue"])]),_:1})],64)):m("",!0),v.operationType==="install"?(d(),f(C,{key:1},[a(p,{label:"安装人员",prop:"installer"},{default:o(()=>[a(g,{modelValue:n.installer,"onUpdate:modelValue":s[2]||(s[2]=i=>n.installer=i),placeholder:"请输入安装人员",clearable:""},null,8,["modelValue"])]),_:1}),a(p,{label:"安装位置",prop:"installLocation"},{default:o(()=>[a(U,{modelValue:n.installLocation,"onUpdate:modelValue":s[3]||(s[3]=i=>n.installLocation=i),placeholder:"选择或输入安装位置",filterable:"","allow-create":"","default-first-option":"",class:"full-width"},{default:o(()=>[(d(!0),f(C,null,G(R.value,i=>(d(),b(D,{key:i,label:i,value:i},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(p,{label:"安装时间",prop:"installedAt"},{default:o(()=>[a(V,{modelValue:n.installedAt,"onUpdate:modelValue":s[4]||(s[4]=i=>n.installedAt=i),type:"datetime",placeholder:"选择安装时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",class:"full-width"},null,8,["modelValue"])]),_:1})],64)):m("",!0),v.operationType==="move"?(d(),f(C,{key:2},[a(p,{label:"当前位置"},{default:o(()=>[a(g,{value:v.assetInfo.currentLocation,disabled:""},null,8,["value"])]),_:1}),a(p,{label:"目标位置",prop:"toLocation"},{default:o(()=>[a(U,{modelValue:n.toLocation,"onUpdate:modelValue":s[5]||(s[5]=i=>n.toLocation=i),placeholder:"选择或输入目标位置",filterable:"","allow-create":"","default-first-option":"",class:"full-width"},{default:o(()=>[(d(!0),f(C,null,G(R.value,i=>(d(),b(D,{key:i,label:i,value:i},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(p,{label:"变更原因",prop:"moveReason"},{default:o(()=>[a(g,{modelValue:n.moveReason,"onUpdate:modelValue":s[6]||(s[6]=i=>n.moveReason=i),type:"textarea",rows:3,placeholder:"请输入位置变更原因",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1})],64)):m("",!0),v.operationType==="outbound"?(d(),f(C,{key:3},[a(p,{label:"出库原因",prop:"outboundReason"},{default:o(()=>[a(U,{modelValue:n.outboundReason,"onUpdate:modelValue":s[7]||(s[7]=i=>n.outboundReason=i),placeholder:"选择或输入出库原因",filterable:"","allow-create":"","default-first-option":"",class:"full-width"},{default:o(()=>[a(D,{label:"设备报废",value:"设备报废"}),a(D,{label:"设备维修",value:"设备维修"}),a(D,{label:"设备调拨",value:"设备调拨"}),a(D,{label:"设备退回",value:"设备退回"}),a(D,{label:"设备升级",value:"设备升级"}),a(D,{label:"其他",value:"其他"})]),_:1},8,["modelValue"])]),_:1}),a(p,{label:"操作人员",prop:"outboundOperator"},{default:o(()=>[a(g,{modelValue:n.outboundOperator,"onUpdate:modelValue":s[8]||(s[8]=i=>n.outboundOperator=i),placeholder:"请输入操作人员",clearable:""},null,8,["modelValue"])]),_:1}),a(p,{label:"出库时间",prop:"outboundAt"},{default:o(()=>[a(V,{modelValue:n.outboundAt,"onUpdate:modelValue":s[9]||(s[9]=i=>n.outboundAt=i),type:"datetime",placeholder:"选择出库时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",class:"full-width"},null,8,["modelValue"])]),_:1})],64)):m("",!0),v.operationType==="reactivate"?(d(),b(p,{key:4,label:"签收人",prop:"receiver"},{default:o(()=>[a(g,{modelValue:n.receiver,"onUpdate:modelValue":s[10]||(s[10]=i=>n.receiver=i),placeholder:"请输入签收人",clearable:""},null,8,["modelValue"])]),_:1})):m("",!0),a(p,{label:"操作人员",prop:"operator"},{default:o(()=>[a(g,{modelValue:n.operator,"onUpdate:modelValue":s[11]||(s[11]=i=>n.operator=i),placeholder:"请输入操作人员",clearable:""},null,8,["modelValue"])]),_:1}),a(p,{label:"备注说明",prop:"reason"},{default:o(()=>[a(g,{modelValue:n.reason,"onUpdate:modelValue":s[12]||(s[12]=i=>n.reason=i),type:"textarea",rows:3,placeholder:"请输入备注说明",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"])}}},Pe=K($e,[["__scopeId","data-v-0de1422b"]]);const Fe={class:"asset-detail"},je={class:"page-header"},ze={class:"header-left"},Ge={class:"header-title"},We={class:"header-actions"},Xe={class:"card-header"},Je={class:"info-grid"},Ke={class:"info-item"},Qe={class:"info-item"},Ze={class:"info-item"},et={class:"info-item"},tt={class:"info-item"},lt={class:"info-item"},at={class:"info-item"},ot={class:"info-item"},st={class:"info-item"},nt={class:"info-item"},it={key:0,class:"info-item full-width"},ut={class:"specification"},rt={key:1,class:"info-item full-width"},dt={class:"notes"},ct={class:"flow-timeline"},pt={class:"timeline-content"},mt={class:"timeline-content"},vt={key:0},ft={class:"timeline-content"},_t={key:0},bt={key:1},yt={class:"timeline-content"},gt={key:0},kt={key:1},Vt={class:"status-info"},Dt={class:"status-item"},wt={class:"status-text"},Et={class:"operations-list"},At={class:"operation-content"},ht={class:"operation-desc"},Yt={class:"operation-time"},Ct={class:"view-all"},Tt={class:"quick-actions"},Lt={__name:"Detail",setup(v){const y=_e(),L=be(),_=A(!1),l=A({}),h=A([{id:1,type:"create",icon:"Plus",description:"资产创建完成",time:"2小时前"},{id:2,type:"update",icon:"Edit",description:"更新了资产信息",time:"1天前"}]),w={PENDING:{text:"待处理",type:"info",class:"pending",description:"资产已创建，等待入库"},RECEIVED:{text:"已入库",type:"success",class:"received",description:"资产已签收入库"},INSTALLED:{text:"已安装",type:"primary",class:"installed",description:"资产已安装部署"},OUTBOUND:{text:"已出库",type:"warning",class:"outbound",description:"资产已出库处理"},SCRAPPED:{text:"已报废",type:"danger",class:"scrapped",description:"资产已报废处理"}},R=u=>{var t;return((t=w[u])==null?void 0:t.text)||u},M=u=>{var t;return((t=w[u])==null?void 0:t.type)||"info"},q=u=>{var t;return((t=w[u])==null?void 0:t.class)||"default"},n=u=>{var t;return((t=w[u])==null?void 0:t.description)||""},B=u=>u?J(u).format("YYYY-MM-DD"):"-",I=u=>u?J(u).format("YYYY-MM-DD HH:mm:ss"):"-",$=u=>u?`¥${u.toLocaleString()}`:"-",P=u=>u?`${u}个月`:"-",O=async()=>{_.value=!0;try{const{data:u}=await Oe(y.params.id);l.value=u}catch{T.error("加载资产详情失败")}finally{_.value=!1}},r=()=>{L.back()},s=()=>{L.push(`/assets/edit/${l.value.id}`)},g=async()=>{try{await Ve.confirm(`确定要删除资产 "${l.value.assetCode}" 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await Ue(l.value.id),T.success("删除成功"),L.push("/assets")}catch(u){u!=="cancel"&&T.error("删除失败")}},p=A(!1),V=A(""),D=()=>{V.value="receive",p.value=!0},U=()=>{V.value="install",p.value=!0},F=()=>{V.value="move",p.value=!0},H=()=>{V.value="outbound",p.value=!0},j=()=>{V.value="reactivate",p.value=!0},i=u=>{l.value=u,T.success("操作完成")},Z=()=>{T.info("历史记录功能开发中...")};return ye(()=>{O()}),(u,t)=>{const E=Q,ee=Ae,x=he,N=Ye,te=Ce,W=Te,le=Ie,ae=Le,oe=De,se=we;return d(),f("div",Fe,[e("div",je,[e("div",ze,[a(E,{onClick:r,icon:ge(Ee),class:"back-btn"},{default:o(()=>t[1]||(t[1]=[k(" 返回 ",-1)])),_:1,__:[1]},8,["icon"]),e("div",Ge,[e("h2",null,c(l.value.assetCode),1),e("p",null,c(l.value.productModel),1)])]),e("div",We,[a(E,{type:"primary",onClick:s,icon:u.Edit},{default:o(()=>t[2]||(t[2]=[k(" 编辑 ",-1)])),_:1,__:[2]},8,["icon"]),a(E,{type:"danger",onClick:g,icon:u.Delete},{default:o(()=>t[3]||(t[3]=[k(" 删除 ",-1)])),_:1,__:[3]},8,["icon"])])]),ke((d(),b(oe,{gutter:24},{default:o(()=>[a(W,{lg:16,md:24},{default:o(()=>[a(x,{title:"基本信息",class:"info-card"},{header:o(()=>[e("div",Xe,[t[4]||(t[4]=e("span",null,"基本信息",-1)),a(ee,{type:M(l.value.status),size:"large"},{default:o(()=>[k(c(R(l.value.status)),1)]),_:1},8,["type"])])]),default:o(()=>[e("div",Je,[e("div",Ke,[t[5]||(t[5]=e("label",null,"资产编号",-1)),e("span",null,c(l.value.assetCode||"-"),1)]),e("div",Qe,[t[6]||(t[6]=e("label",null,"序列号",-1)),e("span",null,c(l.value.serialNumber||"-"),1)]),e("div",Ze,[t[7]||(t[7]=e("label",null,"产品型号",-1)),e("span",null,c(l.value.productModel||"-"),1)]),e("div",et,[t[8]||(t[8]=e("label",null,"产品类型",-1)),e("span",null,c(l.value.productType||"-"),1)]),e("div",tt,[t[9]||(t[9]=e("label",null,"品牌",-1)),e("span",null,c(l.value.brand||"-"),1)]),e("div",lt,[t[10]||(t[10]=e("label",null,"供应商",-1)),e("span",null,c(l.value.supplier||"-"),1)]),e("div",at,[t[11]||(t[11]=e("label",null,"采购日期",-1)),e("span",null,c(B(l.value.purchaseDate)),1)]),e("div",ot,[t[12]||(t[12]=e("label",null,"采购价格",-1)),e("span",null,c($(l.value.purchasePrice)),1)]),e("div",st,[t[13]||(t[13]=e("label",null,"保修期",-1)),e("span",null,c(P(l.value.warrantyPeriod)),1)]),e("div",nt,[t[14]||(t[14]=e("label",null,"当前位置",-1)),e("span",null,c(l.value.currentLocation||"-"),1)])]),l.value.specification?(d(),f("div",it,[t[15]||(t[15]=e("label",null,"规格说明",-1)),e("div",ut,c(l.value.specification),1)])):m("",!0),l.value.notes?(d(),f("div",rt,[t[16]||(t[16]=e("label",null,"备注信息",-1)),e("div",dt,c(l.value.notes),1)])):m("",!0)]),_:1}),a(x,{title:"流转信息",class:"info-card"},{default:o(()=>[e("div",ct,[a(te,null,{default:o(()=>[l.value.createdAt?(d(),b(N,{key:0,icon:"Plus",color:"#67c23a"},{default:o(()=>[e("div",pt,[t[17]||(t[17]=e("h4",null,"资产创建",-1)),e("p",null,c(I(l.value.createdAt)),1)])]),_:1})):m("",!0),l.value.receivedAt?(d(),b(N,{key:1,icon:"Checked",color:"#409eff"},{default:o(()=>[e("div",mt,[t[18]||(t[18]=e("h4",null,"入库接收",-1)),e("p",null,c(I(l.value.receivedAt)),1),l.value.receiver?(d(),f("span",vt,"签收人: "+c(l.value.receiver),1)):m("",!0)])]),_:1})):m("",!0),l.value.installedAt?(d(),b(N,{key:2,icon:"Tools",color:"#e6a23c"},{default:o(()=>[e("div",ft,[t[19]||(t[19]=e("h4",null,"安装完成",-1)),e("p",null,c(I(l.value.installedAt)),1),l.value.installer?(d(),f("span",_t,"安装人员: "+c(l.value.installer),1)):m("",!0),l.value.installLocation?(d(),f("span",bt,"安装位置: "+c(l.value.installLocation),1)):m("",!0)])]),_:1})):m("",!0),l.value.outboundAt?(d(),b(N,{key:3,icon:"Upload",color:"#f56c6c"},{default:o(()=>[e("div",yt,[t[20]||(t[20]=e("h4",null,"出库处理",-1)),e("p",null,c(I(l.value.outboundAt)),1),l.value.outboundOperator?(d(),f("span",gt,"操作人员: "+c(l.value.outboundOperator),1)):m("",!0),l.value.outboundReason?(d(),f("span",kt,"出库原因: "+c(l.value.outboundReason),1)):m("",!0)])]),_:1})):m("",!0)]),_:1})])]),_:1})]),_:1}),a(W,{lg:8,md:24},{default:o(()=>[a(x,{class:"status-card"},{header:o(()=>t[21]||(t[21]=[e("span",null,"状态信息",-1)])),default:o(()=>[e("div",Vt,[e("div",Dt,[e("div",{class:X(["status-circle",q(l.value.status)])},null,2),e("div",wt,[e("h4",null,c(R(l.value.status)),1),e("p",null,c(n(l.value.status)),1)])])])]),_:1}),a(x,{title:"最近操作",class:"operations-card"},{default:o(()=>[e("div",Et,[(d(!0),f(C,null,G(h.value,Y=>(d(),f("div",{key:Y.id,class:"operation-item"},[e("div",{class:X(["operation-icon",Y.type])},[a(le,null,{default:o(()=>[(d(),b(Re(Y.icon)))]),_:2},1024)],2),e("div",At,[e("p",ht,c(Y.description),1),e("span",Yt,c(Y.time),1)])]))),128))]),e("div",Ct,[a(ae,{type:"primary",onClick:Z},{default:o(()=>t[22]||(t[22]=[k(" 查看完整历史记录 ",-1)])),_:1,__:[22]})])]),_:1}),a(x,{title:"快速操作",class:"quick-actions-card"},{default:o(()=>[e("div",Tt,[l.value.status==="PENDING"?(d(),b(E,{key:0,type:"success",onClick:D,icon:u.Checked,class:"action-btn"},{default:o(()=>t[23]||(t[23]=[k(" 确认入库 ",-1)])),_:1,__:[23]},8,["icon"])):m("",!0),l.value.status==="RECEIVED"?(d(),b(E,{key:1,type:"warning",onClick:U,icon:u.Tools,class:"action-btn"},{default:o(()=>t[24]||(t[24]=[k(" 标记安装 ",-1)])),_:1,__:[24]},8,["icon"])):m("",!0),["RECEIVED","INSTALLED"].includes(l.value.status)?(d(),b(E,{key:2,type:"info",onClick:F,icon:u.Location,class:"action-btn"},{default:o(()=>t[25]||(t[25]=[k(" 位置变更 ",-1)])),_:1,__:[25]},8,["icon"])):m("",!0),["RECEIVED","INSTALLED"].includes(l.value.status)?(d(),b(E,{key:3,type:"danger",onClick:H,icon:u.Upload,class:"action-btn"},{default:o(()=>t[26]||(t[26]=[k(" 资产出库 ",-1)])),_:1,__:[26]},8,["icon"])):m("",!0),l.value.status==="OUTBOUND"?(d(),b(E,{key:4,type:"success",onClick:j,icon:u.Refresh,class:"action-btn"},{default:o(()=>t[27]||(t[27]=[k(" 重新激活 ",-1)])),_:1,__:[27]},8,["icon"])):m("",!0)])]),_:1})]),_:1})]),_:1})),[[se,_.value]]),a(Pe,{modelValue:p.value,"onUpdate:modelValue":t[0]||(t[0]=Y=>p.value=Y),"operation-type":V.value,"asset-info":l.value,onSuccess:i},null,8,["modelValue","operation-type","asset-info"])])}}},Ft=K(Lt,[["__scopeId","data-v-54a49712"]]);export{Ft as default};
