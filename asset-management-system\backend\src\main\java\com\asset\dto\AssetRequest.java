package com.asset.dto;

import com.asset.entity.Asset;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class AssetRequest {
    
    private String assetCode;
    
    private String serialNumber;
    
    private String productModel;
    
    private String productType;
    
    private String brand;
    
    private String specification;
    
    private LocalDate purchaseDate;
    
    @DecimalMin(value = "0.0", message = "采购价格不能为负数")
    private BigDecimal purchasePrice;
    
    private String supplier;
    
    private Integer warrantyPeriod;
    
    private Asset.AssetStatus status;
    
    private String currentLocation;
    
    private String notes;
}