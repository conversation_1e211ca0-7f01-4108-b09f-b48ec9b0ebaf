package com.asset.service;

import com.asset.dto.PageResponse;
import com.asset.entity.User;
import com.asset.repository.UserRepository;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户管理服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;

    /**
     * 获取用户列表（分页）
     */
    public PageResponse<User> getUserList(int page, int size, String keyword, User.UserRole role, User.UserStatus status) {
        Page<User> pageRequest = new Page<>(page, size);
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        
        // 排除已删除的用户
        queryWrapper.eq("deleted", 0);
        
        // 关键词搜索
        if (StringUtils.hasText(keyword)) {
            queryWrapper.and(wrapper -> wrapper
                .like("username", keyword)
                .or().like("real_name", keyword)
                .or().like("email", keyword)
                .or().like("phone", keyword)
                .or().like("department", keyword)
            );
        }
        
        // 角色筛选
        if (role != null) {
            queryWrapper.eq("role", role);
        }
        
        // 状态筛选
        if (status != null) {
            queryWrapper.eq("status", status);
        }
        
        // 按创建时间倒序
        queryWrapper.orderByDesc("created_at");
        
        IPage<User> result = userRepository.selectPage(pageRequest, queryWrapper);
        
        // 清除密码字段
        result.getRecords().forEach(user -> user.setPassword(null));
        
        return new PageResponse<>(
            result.getRecords(),
            result.getTotal(),
            result.getCurrent(),
            result.getSize()
        );
    }

    /**
     * 根据ID获取用户
     */
    public User getUserById(Long id) {
        User user = userRepository.selectById(id);
        if (user == null || user.getDeleted() == 1) {
            throw new RuntimeException("用户不存在");
        }
        // 清除密码字段
        user.setPassword(null);
        return user;
    }

    /**
     * 创建用户
     */
    @Transactional
    public User createUser(User user) {
        // 检查用户名是否已存在
        if (!isUsernameAvailable(user.getUsername())) {
            throw new RuntimeException("用户名已存在");
        }
        
        // 检查邮箱是否已存在
        if (StringUtils.hasText(user.getEmail()) && !isEmailAvailable(user.getEmail())) {
            throw new RuntimeException("邮箱已存在");
        }
        
        // 设置默认值
        user.setId(null);
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        user.setStatus(user.getStatus() != null ? user.getStatus() : User.UserStatus.ACTIVE);
        user.setRole(user.getRole() != null ? user.getRole() : User.UserRole.USER);
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());
        user.setDeleted(0);
        
        userRepository.insert(user);
        
        // 清除密码字段
        user.setPassword(null);
        return user;
    }

    /**
     * 更新用户
     */
    @Transactional
    public User updateUser(Long id, User user) {
        User existingUser = getUserById(id);
        
        // 检查用户名是否已被其他用户使用
        if (!existingUser.getUsername().equals(user.getUsername()) && !isUsernameAvailable(user.getUsername())) {
            throw new RuntimeException("用户名已存在");
        }
        
        // 检查邮箱是否已被其他用户使用
        if (StringUtils.hasText(user.getEmail()) && 
            !user.getEmail().equals(existingUser.getEmail()) && 
            !isEmailAvailable(user.getEmail())) {
            throw new RuntimeException("邮箱已存在");
        }
        
        // 更新字段
        existingUser.setUsername(user.getUsername());
        existingUser.setRole(user.getRole());
        existingUser.setStatus(user.getStatus());
        existingUser.setEmail(user.getEmail());
        existingUser.setPhone(user.getPhone());
        existingUser.setRealName(user.getRealName());
        existingUser.setDepartment(user.getDepartment());
        existingUser.setUpdatedAt(LocalDateTime.now());
        
        // 如果提供了新密码，则更新密码
        if (StringUtils.hasText(user.getPassword())) {
            existingUser.setPassword(passwordEncoder.encode(user.getPassword()));
        }
        
        userRepository.updateById(existingUser);
        
        // 清除密码字段
        existingUser.setPassword(null);
        return existingUser;
    }

    /**
     * 删除用户（软删除）
     */
    @Transactional
    public void deleteUser(Long id) {
        User user = getUserById(id);
        user.setDeleted(1);
        user.setUpdatedAt(LocalDateTime.now());
        userRepository.updateById(user);
    }

    /**
     * 批量删除用户
     */
    @Transactional
    public void batchDeleteUsers(List<Long> ids) {
        for (Long id : ids) {
            deleteUser(id);
        }
    }

    /**
     * 重置用户密码
     */
    @Transactional
    public void resetUserPassword(Long id, String newPassword) {
        User user = getUserById(id);
        user.setPassword(passwordEncoder.encode(newPassword));
        user.setUpdatedAt(LocalDateTime.now());
        userRepository.updateById(user);
    }

    /**
     * 更新用户状态
     */
    @Transactional
    public void updateUserStatus(Long id, User.UserStatus status) {
        User user = getUserById(id);
        user.setStatus(status);
        user.setUpdatedAt(LocalDateTime.now());
        userRepository.updateById(user);
    }

    /**
     * 获取用户统计信息
     */
    public Map<String, Object> getUserStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("deleted", 0);
        
        // 总用户数
        Long totalUsers = userRepository.selectCount(queryWrapper);
        statistics.put("totalUsers", totalUsers);
        
        // 活跃用户数
        queryWrapper.eq("status", User.UserStatus.ACTIVE);
        Long activeUsers = userRepository.selectCount(queryWrapper);
        statistics.put("activeUsers", activeUsers);
        
        // 禁用用户数
        queryWrapper.clear();
        queryWrapper.eq("deleted", 0).eq("status", User.UserStatus.INACTIVE);
        Long inactiveUsers = userRepository.selectCount(queryWrapper);
        statistics.put("inactiveUsers", inactiveUsers);
        
        // 管理员数量
        queryWrapper.clear();
        queryWrapper.eq("deleted", 0).eq("role", User.UserRole.ADMIN);
        Long adminUsers = userRepository.selectCount(queryWrapper);
        statistics.put("adminUsers", adminUsers);
        
        return statistics;
    }

    /**
     * 检查用户名是否可用
     */
    public boolean isUsernameAvailable(String username) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", username).eq("deleted", 0);
        return userRepository.selectCount(queryWrapper) == 0;
    }

    /**
     * 检查邮箱是否可用
     */
    public boolean isEmailAvailable(String email) {
        if (!StringUtils.hasText(email)) {
            return true;
        }
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("email", email).eq("deleted", 0);
        return userRepository.selectCount(queryWrapper) == 0;
    }
}
