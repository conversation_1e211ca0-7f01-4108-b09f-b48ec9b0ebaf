import{_ as se}from"./_plugin-vue_export-helper-62491c14.js";/* empty css                   *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                        *//* empty css                *//* empty css                 *//* empty css                */import{u as oe,r as v,b as S,o as ne,c as y,d as w,f as u,g as t,w as l,F as c,$ as re,j as g,n as ie,E as ce,aG as de,i as r,K as ue,h as pe,M as N,a2 as _e,a3 as me,aH as fe,aI as A,H as ye,X as L,a8 as x,aJ as ge,aK as he,aB as ve,y as I,z as be,k as Ce,l as Ee,aL as we,aM as xe,p as ke,aN as Te,aE as ze,aO as De,aP as Ve,aQ as Be,aR as Se,aS as Ne}from"./index-2733c819.js";import{g as Ae,a as Le,d as Ie,b as Pe}from"./asset-f5b5b286.js";const $e={class:"assets-page"},Me={class:"page-header"},Ue={class:"header-actions"},Oe={key:0,class:"batch-actions"},Re={class:"batch-buttons"},Fe={class:"pagination-container"},je={__name:"index",setup(Ge){const b=oe(),C=v(!1),k=v([]),p=v([]),T=v([]),i=S({keyword:"",status:"",productType:""}),o=S({current:1,size:10,total:0}),z={PENDING:{text:"待处理",type:"info"},RECEIVED:{text:"已入库",type:"success"},INSTALLED:{text:"已安装",type:"primary"},OUTBOUND:{text:"已出库",type:"warning"},SCRAPPED:{text:"已报废",type:"danger"}},P=a=>{var e;return((e=z[a])==null?void 0:e.text)||a},$=a=>{var e;return((e=z[a])==null?void 0:e.type)||"info"},M=a=>a?ve(a).format("YYYY-MM-DD HH:mm"):"-",_=async()=>{C.value=!0;try{const a={page:o.current,size:o.size,...i},{data:e}=await Ae(a);k.value=e.records,o.total=e.total}catch{g.error("加载资产列表失败")}finally{C.value=!1}},U=async()=>{try{const{data:a}=await Le();T.value=a}catch(a){console.error("加载产品类型失败:",a)}},m=()=>{o.current=1,_()},O=()=>{Object.assign(i,{keyword:"",status:"",productType:""}),m()},R=a=>{o.size=a,o.current=1,_()},F=a=>{o.current=a,_()},j=a=>{p.value=a},G=()=>{b.push("/assets/create")},D=a=>{b.push(`/assets/detail/${a.id}`)},H=a=>{b.push(`/assets/edit/${a.id}`)},K=async a=>{try{await I.confirm(`确定要删除资产 "${a.assetCode}" 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await Ie(a.id),g.success("删除成功"),_()}catch(e){e!=="cancel"&&g.error("删除失败")}},Y=async()=>{try{await I.confirm(`确定要删除选中的 ${p.value.length} 个资产吗？`,"确认批量删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const a=p.value.map(e=>e.id);await Pe(a),g.success("批量删除成功"),p.value=[],_()}catch(a){a!=="cancel"&&g.error("批量删除失败")}};return ne(()=>{_(),U()}),(a,e)=>{const d=ie,J=be,Q=Ce,h=Ee,f=we,V=xe,X=ke,B=ce,q=de,n=Te,W=ze,Z=De,ee=Ve,te=Be,ae=Se,le=Ne;return y(),w("div",$e,[u("div",Me,[e[6]||(e[6]=u("div",{class:"header-title"},[u("h2",null,"资产管理"),u("p",null,"管理和跟踪企业资产信息")],-1)),u("div",Ue,[t(d,{type:"primary",icon:c(ue),onClick:G,class:"create-btn"},{default:l(()=>e[5]||(e[5]=[r(" 新增资产 ",-1)])),_:1,__:[5]},8,["icon"])])]),t(B,{class:"search-card",shadow:"never"},{default:l(()=>[t(X,{model:i,inline:"",class:"search-form"},{default:l(()=>[t(h,{label:"关键词"},{default:l(()=>[t(Q,{modelValue:i.keyword,"onUpdate:modelValue":e[0]||(e[0]=s=>i.keyword=s),placeholder:"资产编号、型号、品牌...",clearable:"",onClear:m,onKeyup:pe(m,["enter"]),class:"search-input"},{prefix:l(()=>[t(J,null,{default:l(()=>[t(c(N))]),_:1})]),_:1},8,["modelValue"])]),_:1}),t(h,{label:"状态"},{default:l(()=>[t(V,{modelValue:i.status,"onUpdate:modelValue":e[1]||(e[1]=s=>i.status=s),placeholder:"选择状态",clearable:"",onChange:m},{default:l(()=>[t(f,{label:"待处理",value:"PENDING"}),t(f,{label:"已入库",value:"RECEIVED"}),t(f,{label:"已安装",value:"INSTALLED"}),t(f,{label:"已出库",value:"OUTBOUND"}),t(f,{label:"已报废",value:"SCRAPPED"})]),_:1},8,["modelValue"])]),_:1}),t(h,{label:"产品类型"},{default:l(()=>[t(V,{modelValue:i.productType,"onUpdate:modelValue":e[2]||(e[2]=s=>i.productType=s),placeholder:"选择类型",clearable:"",onChange:m},{default:l(()=>[(y(!0),w(_e,null,me(T.value,s=>(y(),L(f,{key:s,label:s,value:s},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(h,null,{default:l(()=>[t(d,{type:"primary",onClick:m,icon:c(N)},{default:l(()=>e[7]||(e[7]=[r(" 搜索 ",-1)])),_:1,__:[7]},8,["icon"]),t(d,{onClick:O,icon:c(fe)},{default:l(()=>e[8]||(e[8]=[r(" 重置 ",-1)])),_:1,__:[8]},8,["icon"])]),_:1})]),_:1},8,["model"])]),_:1}),p.value.length>0?(y(),w("div",Oe,[t(q,{title:`已选择 ${p.value.length} 项`,type:"info","show-icon":"",closable:!1},{default:l(()=>[u("div",Re,[t(d,{type:"danger",size:"small",onClick:Y,icon:c(A)},{default:l(()=>e[9]||(e[9]=[r(" 批量删除 ",-1)])),_:1,__:[9]},8,["icon"])])]),_:1},8,["title"])])):re("",!0),t(B,{class:"table-card",shadow:"never"},{default:l(()=>[ye((y(),L(te,{data:k.value,onSelectionChange:j,class:"asset-table","empty-text":"暂无数据"},{default:l(()=>[t(n,{type:"selection",width:"50"}),t(n,{prop:"assetCode",label:"资产编号","min-width":"120"},{default:l(({row:s})=>[t(W,{type:"primary",onClick:E=>D(s)},{default:l(()=>[r(x(s.assetCode),1)]),_:2},1032,["onClick"])]),_:1}),t(n,{prop:"productModel",label:"产品型号","min-width":"150"}),t(n,{prop:"productType",label:"产品类型","min-width":"120"}),t(n,{prop:"brand",label:"品牌","min-width":"100"}),t(n,{prop:"serialNumber",label:"序列号","min-width":"150"}),t(n,{prop:"status",label:"状态",width:"100"},{default:l(({row:s})=>[t(Z,{type:$(s.status),size:"small"},{default:l(()=>[r(x(P(s.status)),1)]),_:2},1032,["type"])]),_:1}),t(n,{prop:"currentLocation",label:"当前位置","min-width":"120"}),t(n,{prop:"createdAt",label:"创建时间",width:"180"},{default:l(({row:s})=>[r(x(M(s.createdAt)),1)]),_:1}),t(n,{label:"操作",width:"180",fixed:"right"},{default:l(({row:s})=>[t(ee,null,{default:l(()=>[t(d,{type:"primary",size:"small",onClick:E=>D(s),icon:c(ge)},{default:l(()=>e[10]||(e[10]=[r(" 查看 ",-1)])),_:2,__:[10]},1032,["onClick","icon"]),t(d,{type:"success",size:"small",onClick:E=>H(s),icon:c(he)},{default:l(()=>e[11]||(e[11]=[r(" 编辑 ",-1)])),_:2,__:[11]},1032,["onClick","icon"]),t(d,{type:"danger",size:"small",onClick:E=>K(s),icon:c(A)},{default:l(()=>e[12]||(e[12]=[r(" 删除 ",-1)])),_:2,__:[12]},1032,["onClick","icon"])]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[le,C.value]]),u("div",Fe,[t(ae,{"current-page":o.current,"onUpdate:currentPage":e[3]||(e[3]=s=>o.current=s),"page-size":o.size,"onUpdate:pageSize":e[4]||(e[4]=s=>o.size=s),total:o.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:R,onCurrentChange:F},null,8,["current-page","page-size","total"])])]),_:1})])}}},rt=se(je,[["__scopeId","data-v-c93edf1a"]]);export{rt as default};
