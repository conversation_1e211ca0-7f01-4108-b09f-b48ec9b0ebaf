import{_ as le}from"./_plugin-vue_export-helper-62491c14.js";/* empty css                   *//* empty css                   *//* empty css                       *//* empty css                 *//* empty css                     *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                */import{r as _,b as w,o as te,c as V,d as ae,f,g as e,w as a,j as b,E as oe,aV as ne,F as k,M as se,i as s,aH as re,Q as de,H as ie,X as N,a8 as I,$ as z,y as ue,k as pe,l as me,aL as ce,aM as _e,z as fe,n as ge,p as be,aN as Ve,aO as Ce,aQ as ve,aR as ye,aU as Ee,aS as De}from"./index-2733c819.js";const we={class:"install-container"},ke={class:"card-header"},Ne={class:"pagination-container"},Ie={__name:"Install",setup(he){const C=_(!1),v=_(!1),h=_([]),y=_([]),g=_(!1),E=_(),u=w({assetCode:"",installLocation:"",status:""}),i=w({current:1,pageSize:20,total:0}),n=w({assetId:null,assetCode:"",productModel:"",installLocation:"",installDate:new Date,installerName:"",remarks:""}),M={installLocation:[{required:!0,message:"请输入安装位置",trigger:"blur"}],installDate:[{required:!0,message:"请选择安装日期",trigger:"change"}],installerName:[{required:!0,message:"请输入安装人员姓名",trigger:"blur"}]},c=async()=>{C.value=!0;try{h.value=[],i.total=0}catch(o){b.error("加载数据失败: "+o.message)}finally{C.value=!1}},L=()=>{i.current=1,c()},B=()=>{Object.assign(u,{assetCode:"",installLocation:"",status:""}),L()},R=o=>{y.value=o},F=o=>{Object.assign(n,{assetId:o.id,assetCode:o.assetCode,productModel:o.productModel,installLocation:"",installDate:new Date,installerName:"",remarks:""}),g.value=!0},O=async()=>{try{await E.value.validate(),v.value=!0,await new Promise(o=>setTimeout(o,1e3)),b.success("安装成功"),g.value=!1,c()}catch(o){o.message&&b.error("安装失败: "+o.message)}finally{v.value=!1}},P=()=>{if(y.value.length===0){b.warning("请选择要安装的资产");return}ue.confirm(`确定要批量安装选中的 ${y.value.length} 个资产吗？`,"批量安装确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{b.success("批量安装成功"),c()})},$=o=>{console.log("位置迁移:",o)},j=o=>{console.log("查看详情:",o)},A=()=>{var o;(o=E.value)==null||o.resetFields()},q=o=>{i.pageSize=o,c()},G=o=>{i.current=o,c()},H=o=>({PENDING:"warning",RECEIVED:"success",INSTALLED:"info",OUTBOUND:"danger"})[o]||"info",Q=o=>({PENDING:"待入库",RECEIVED:"已入库",INSTALLED:"已安装",OUTBOUND:"已出库"})[o]||o;return te(()=>{c()}),(o,l)=>{const m=pe,r=me,S=ce,X=_e,D=fe,p=ge,x=be,U=oe,d=Ve,J=Ce,K=ve,W=ye,Y=Ee,Z=ne,ee=De;return V(),ae("div",we,[l[22]||(l[22]=f("div",{class:"page-header"},[f("h2",null,"安装管理"),f("p",null,"管理资产的安装操作和位置记录")],-1)),e(U,{class:"search-card",shadow:"never"},{default:a(()=>[e(x,{model:u,inline:"",class:"search-form"},{default:a(()=>[e(r,{label:"资产编号"},{default:a(()=>[e(m,{modelValue:u.assetCode,"onUpdate:modelValue":l[0]||(l[0]=t=>u.assetCode=t),placeholder:"请输入资产编号",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(r,{label:"安装位置"},{default:a(()=>[e(m,{modelValue:u.installLocation,"onUpdate:modelValue":l[1]||(l[1]=t=>u.installLocation=t),placeholder:"请输入安装位置",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(r,{label:"状态"},{default:a(()=>[e(X,{modelValue:u.status,"onUpdate:modelValue":l[2]||(l[2]=t=>u.status=t),placeholder:"请选择状态",clearable:"",style:{width:"150px"}},{default:a(()=>[e(S,{label:"已入库",value:"RECEIVED"}),e(S,{label:"已安装",value:"INSTALLED"})]),_:1},8,["modelValue"])]),_:1}),e(r,null,{default:a(()=>[e(p,{type:"primary",onClick:L},{default:a(()=>[e(D,null,{default:a(()=>[e(k(se))]),_:1}),l[13]||(l[13]=s(" 搜索 ",-1))]),_:1,__:[13]}),e(p,{onClick:B},{default:a(()=>[e(D,null,{default:a(()=>[e(k(re))]),_:1}),l[14]||(l[14]=s(" 重置 ",-1))]),_:1,__:[14]})]),_:1})]),_:1},8,["model"])]),_:1}),e(U,{class:"table-card",shadow:"never"},{header:a(()=>[f("div",ke,[l[16]||(l[16]=f("span",null,"资产安装管理",-1)),e(p,{type:"primary",onClick:P},{default:a(()=>[e(D,null,{default:a(()=>[e(k(de))]),_:1}),l[15]||(l[15]=s(" 批量安装 ",-1))]),_:1,__:[15]})])]),default:a(()=>[ie((V(),N(K,{data:h.value,onSelectionChange:R,style:{width:"100%"}},{default:a(()=>[e(d,{type:"selection",width:"55"}),e(d,{prop:"assetCode",label:"资产编号",width:"120"}),e(d,{prop:"serialNumber",label:"序列号",width:"150"}),e(d,{prop:"productModel",label:"产品型号",width:"150"}),e(d,{prop:"productType",label:"产品类型",width:"120"}),e(d,{prop:"currentLocation",label:"当前位置",width:"180"}),e(d,{prop:"status",label:"状态",width:"100"},{default:a(({row:t})=>[e(J,{type:H(t.status)},{default:a(()=>[s(I(Q(t.status)),1)]),_:2},1032,["type"])]),_:1}),e(d,{prop:"installDate",label:"安装日期",width:"120"},{default:a(({row:t})=>[s(I(t.installDate||"-"),1)]),_:1}),e(d,{prop:"installerName",label:"安装人员",width:"120"},{default:a(({row:t})=>[s(I(t.installerName||"-"),1)]),_:1}),e(d,{label:"操作",width:"200",fixed:"right"},{default:a(({row:t})=>[t.status==="RECEIVED"?(V(),N(p,{key:0,type:"primary",size:"small",onClick:T=>F(t)},{default:a(()=>l[17]||(l[17]=[s(" 安装 ",-1)])),_:2,__:[17]},1032,["onClick"])):z("",!0),t.status==="INSTALLED"?(V(),N(p,{key:1,type:"warning",size:"small",onClick:T=>$(t)},{default:a(()=>l[18]||(l[18]=[s(" 迁移 ",-1)])),_:2,__:[18]},1032,["onClick"])):z("",!0),e(p,{type:"info",size:"small",onClick:T=>j(t)},{default:a(()=>l[19]||(l[19]=[s(" 详情 ",-1)])),_:2,__:[19]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ee,C.value]]),f("div",Ne,[e(W,{"current-page":i.current,"onUpdate:currentPage":l[3]||(l[3]=t=>i.current=t),"page-size":i.pageSize,"onUpdate:pageSize":l[4]||(l[4]=t=>i.pageSize=t),total:i.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:q,onCurrentChange:G},null,8,["current-page","page-size","total"])])]),_:1}),e(Z,{modelValue:g.value,"onUpdate:modelValue":l[12]||(l[12]=t=>g.value=t),title:"资产安装",width:"600px",onClose:A},{footer:a(()=>[e(p,{onClick:l[11]||(l[11]=t=>g.value=!1)},{default:a(()=>l[20]||(l[20]=[s("取消",-1)])),_:1,__:[20]}),e(p,{type:"primary",onClick:O,loading:v.value},{default:a(()=>l[21]||(l[21]=[s(" 确认安装 ",-1)])),_:1,__:[21]},8,["loading"])]),default:a(()=>[e(x,{ref_key:"installFormRef",ref:E,model:n,rules:M,"label-width":"100px"},{default:a(()=>[e(r,{label:"资产编号"},{default:a(()=>[e(m,{modelValue:n.assetCode,"onUpdate:modelValue":l[5]||(l[5]=t=>n.assetCode=t),disabled:""},null,8,["modelValue"])]),_:1}),e(r,{label:"产品型号"},{default:a(()=>[e(m,{modelValue:n.productModel,"onUpdate:modelValue":l[6]||(l[6]=t=>n.productModel=t),disabled:""},null,8,["modelValue"])]),_:1}),e(r,{label:"安装位置",prop:"installLocation"},{default:a(()=>[e(m,{modelValue:n.installLocation,"onUpdate:modelValue":l[7]||(l[7]=t=>n.installLocation=t),placeholder:"请输入安装位置"},null,8,["modelValue"])]),_:1}),e(r,{label:"安装日期",prop:"installDate"},{default:a(()=>[e(Y,{modelValue:n.installDate,"onUpdate:modelValue":l[8]||(l[8]=t=>n.installDate=t),type:"date",placeholder:"选择安装日期",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(r,{label:"安装人员",prop:"installerName"},{default:a(()=>[e(m,{modelValue:n.installerName,"onUpdate:modelValue":l[9]||(l[9]=t=>n.installerName=t),placeholder:"请输入安装人员姓名"},null,8,["modelValue"])]),_:1}),e(r,{label:"备注"},{default:a(()=>[e(m,{modelValue:n.remarks,"onUpdate:modelValue":l[10]||(l[10]=t=>n.remarks=t),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},Ae=le(Ie,[["__scopeId","data-v-6ebbcca5"]]);export{Ae as default};
