#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资产管理系统 - 超级快速启动脚本
跳过健康检查，直接启动服务并打开浏览器

使用方法:
  python quick_start.py

作者: 全栈设计大师
版本: 1.0.0
"""

import os
import sys
import subprocess
import time
import webbrowser
from pathlib import Path
from datetime import datetime

def log(message, emoji="ℹ️"):
    """简单日志输出"""
    timestamp = datetime.now().strftime('%H:%M:%S')
    print(f"[{timestamp}] {emoji} {message}")

def main():
    """主函数"""
    project_root = Path(__file__).parent.absolute()
    backend_dir = project_root / "backend"
    frontend_dir = project_root / "frontend"
    
    # 自定义工具路径
    maven_cmd = r"D:\cursor\apache-maven-3.9.9\bin\mvn.cmd"
    npm_cmd = r"D:\cursor\nodejs\npm.cmd"
    
    print("🏢 资产管理系统 - 超级快速启动")
    print("=" * 50)
    
    # 启动后端 - 优化版本
    log("启动后端服务...", "🔧")
    try:
        # 直接在新窗口启动，不等待，不捕获输出
        if os.name == 'nt':  # Windows
            backend_process = subprocess.Popen(
                f'start "后端服务" cmd /k "cd /d {backend_dir} && {maven_cmd} spring-boot:run"',
                shell=True
            )
        else:  # Linux/Mac
            backend_process = subprocess.Popen(
                [maven_cmd, "spring-boot:run"],
                cwd=backend_dir
            )
        log("后端服务启动中（在新窗口中运行）", "✅")
    except Exception as e:
        log(f"后端启动失败: {e}", "❌")
        return False

    # 智能等待 - 检测端口而不是固定等待
    log("等待后端服务就绪...", "⏳")
    backend_ready = False
    for i in range(60):  # 最多等待60秒
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', 8080))
            sock.close()
            if result == 0:
                backend_ready = True
                log(f"后端服务就绪 (耗时: {i+1}秒)", "✅")
                break
        except:
            pass

        if i % 5 == 0 and i > 0:  # 每5秒显示一次进度
            log(f"仍在等待后端启动... ({i}秒)", "⏳")
        time.sleep(1)

    if not backend_ready:
        log("后端启动超时，但继续启动前端", "⚠️")
    
    # 启动前端 - 优化版本
    log("启动前端服务...", "🎨")
    try:
        # 直接在新窗口启动
        if os.name == 'nt':  # Windows
            frontend_process = subprocess.Popen(
                f'start "前端服务" cmd /k "cd /d {frontend_dir} && {npm_cmd} run dev"',
                shell=True
            )
        else:  # Linux/Mac
            frontend_process = subprocess.Popen(
                [npm_cmd, "run", "dev"],
                cwd=frontend_dir
            )
        log("前端服务启动中（在新窗口中运行）", "✅")
    except Exception as e:
        log(f"前端启动失败: {e}", "❌")
        return False

    # 智能等待前端
    log("等待前端服务就绪...", "⏳")
    frontend_ready = False
    for i in range(30):  # 最多等待30秒
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', 3000))
            sock.close()
            if result == 0:
                frontend_ready = True
                log(f"前端服务就绪 (耗时: {i+1}秒)", "✅")
                break
        except:
            pass

        if i % 5 == 0 and i > 0:
            log(f"仍在等待前端启动... ({i}秒)", "⏳")
        time.sleep(1)

    if not frontend_ready:
        log("前端启动可能需要更多时间", "⚠️")
    
    # 显示信息
    print("\n🎉 服务启动完成！")
    print("📱 访问地址:")
    print("   🏠 系统首页: http://localhost:3000")
    print("   📊 库存统计: http://localhost:3000/statistics")
    print("   👥 用户管理: http://localhost:3000/users")
    print("   🔧 后端API: http://localhost:8080/api")
    print("\n💡 提示:")
    print("   - 后端和前端在独立窗口中运行")
    print("   - 关闭对应窗口可停止服务")
    print("   - 如果页面无法访问，请等待更长时间")
    print("=" * 50)
    
    # 询问是否打开浏览器
    try:
        open_browser = input("\n是否打开浏览器访问系统？(y/n): ").lower().strip()
        if open_browser in ['y', 'yes', '是', '']:
            log("正在打开浏览器...", "🌐")
            webbrowser.open('http://localhost:3000')
    except KeyboardInterrupt:
        print("\n")
    
    log("启动完成！", "🎉")
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("❌ 启动失败")
        input("按回车键退出...")
        sys.exit(1)
    else:
        input("按回车键退出...")
