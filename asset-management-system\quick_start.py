#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资产管理系统 - 超级快速启动脚本
跳过健康检查，直接启动服务并打开浏览器

使用方法:
  python quick_start.py

作者: 全栈设计大师
版本: 1.0.0
"""

import os
import sys
import subprocess
import time
import webbrowser
from pathlib import Path
from datetime import datetime

def log(message, emoji="ℹ️"):
    """简单日志输出"""
    timestamp = datetime.now().strftime('%H:%M:%S')
    print(f"[{timestamp}] {emoji} {message}")

def main():
    """主函数"""
    project_root = Path(__file__).parent.absolute()
    backend_dir = project_root / "backend"
    frontend_dir = project_root / "frontend"
    
    # 自定义工具路径
    maven_cmd = r"D:\cursor\apache-maven-3.9.9\bin\mvn.cmd"
    npm_cmd = r"D:\cursor\nodejs\npm.cmd"
    
    print("🏢 资产管理系统 - 超级快速启动")
    print("=" * 50)
    
    # 启动后端
    log("启动后端服务...", "🔧")
    try:
        backend_process = subprocess.Popen(
            [maven_cmd, "spring-boot:run"],
            cwd=backend_dir,
            creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
        )
        log(f"后端服务启动中 (PID: {backend_process.pid})", "✅")
    except Exception as e:
        log(f"后端启动失败: {e}", "❌")
        return False
    
    # 等待5秒
    log("等待后端初始化...", "⏳")
    time.sleep(5)
    
    # 启动前端
    log("启动前端服务...", "🎨")
    try:
        frontend_process = subprocess.Popen(
            [npm_cmd, "run", "dev"],
            cwd=frontend_dir,
            creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
        )
        log(f"前端服务启动中 (PID: {frontend_process.pid})", "✅")
    except Exception as e:
        log(f"前端启动失败: {e}", "❌")
        return False
    
    # 等待前端启动
    log("等待前端初始化...", "⏳")
    time.sleep(10)
    
    # 显示信息
    print("\n🎉 服务启动完成！")
    print("📱 访问地址:")
    print("   🏠 系统首页: http://localhost:3000")
    print("   📊 库存统计: http://localhost:3000/statistics")
    print("   👥 用户管理: http://localhost:3000/users")
    print("   🔧 后端API: http://localhost:8080/api")
    print("\n💡 提示:")
    print("   - 后端和前端在独立窗口中运行")
    print("   - 关闭对应窗口可停止服务")
    print("   - 如果页面无法访问，请等待更长时间")
    print("=" * 50)
    
    # 询问是否打开浏览器
    try:
        open_browser = input("\n是否打开浏览器访问系统？(y/n): ").lower().strip()
        if open_browser in ['y', 'yes', '是', '']:
            log("正在打开浏览器...", "🌐")
            webbrowser.open('http://localhost:3000')
    except KeyboardInterrupt:
        print("\n")
    
    log("启动完成！", "🎉")
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("❌ 启动失败")
        input("按回车键退出...")
        sys.exit(1)
    else:
        input("按回车键退出...")
