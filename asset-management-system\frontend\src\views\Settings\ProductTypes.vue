<template>
  <div class="product-types">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>产品类型管理</h2>
        <p>管理系统中的产品类型，支持添加、编辑和删除</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showAddDialog" :icon="Plus">
          新增类型
        </el-button>
      </div>
    </div>

    <!-- 产品类型列表 -->
    <el-card class="types-card" v-loading="loading">
      <div class="types-grid">
        <div 
          v-for="(type, index) in productTypes" 
          :key="type.id || index"
          class="type-item"
        >
          <div class="type-content">
            <div class="type-icon">
              <el-icon size="24"><Box /></el-icon>
            </div>
            <div class="type-info">
              <h4>{{ type.name }}</h4>
              <p v-if="type.description">{{ type.description }}</p>
              <span class="type-count">{{ type.assetCount || 0 }} 个资产</span>
            </div>
          </div>
          <div class="type-actions">
            <el-button 
              link 
              type="primary" 
              @click="editType(type)"
              :icon="Edit"
            >
              编辑
            </el-button>
            <el-button 
              link 
              type="danger" 
              @click="deleteType(type)"
              :icon="Delete"
              :disabled="type.assetCount > 0"
            >
              删除
            </el-button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <el-empty v-if="!loading && productTypes.length === 0" description="暂无产品类型">
        <el-button type="primary" @click="showAddDialog">添加第一个类型</el-button>
      </el-empty>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑产品类型' : '新增产品类型'"
      width="500px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="80px"
      >
        <el-form-item label="类型名称" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="请输入产品类型名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入类型描述（可选）"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit"
          :loading="submitting"
        >
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { Plus, Edit, Delete, Box } from '@element-plus/icons-vue'

const loading = ref(false)
const submitting = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()

// 产品类型列表
const productTypes = ref([
  { id: 1, name: '计算机设备', description: '台式机、笔记本电脑等', assetCount: 15 },
  { id: 2, name: '网络设备', description: '路由器、交换机、防火墙等', assetCount: 8 },
  { id: 3, name: '办公设备', description: '打印机、复印机、传真机等', assetCount: 12 },
  { id: 4, name: '移动设备', description: '手机、平板电脑等', assetCount: 6 },
  { id: 5, name: '服务器设备', description: '物理服务器、存储设备等', assetCount: 4 }
])

// 表单数据
const formData = reactive({
  name: '',
  description: ''
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入产品类型名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ]
}

// 显示添加对话框
const showAddDialog = () => {
  isEdit.value = false
  dialogVisible.value = true
  resetForm()
}

// 编辑类型
const editType = (type) => {
  isEdit.value = true
  formData.name = type.name
  formData.description = type.description || ''
  formData.id = type.id
  dialogVisible.value = true
}

// 删除类型
const deleteType = (type) => {
  ElMessageBox.confirm(
    `确定要删除产品类型"${type.name}"吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 这里调用删除API
    const index = productTypes.value.findIndex(t => t.id === type.id)
    if (index > -1) {
      productTypes.value.splice(index, 1)
    }
    ElMessage.success('删除成功')
  }).catch(() => {
    // 用户取消删除
  })
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (isEdit.value) {
      // 更新现有类型
      const index = productTypes.value.findIndex(t => t.id === formData.id)
      if (index > -1) {
        productTypes.value[index] = {
          ...productTypes.value[index],
          name: formData.name,
          description: formData.description
        }
      }
      ElMessage.success('更新成功')
    } else {
      // 添加新类型
      const newType = {
        id: Date.now(),
        name: formData.name,
        description: formData.description,
        assetCount: 0
      }
      productTypes.value.push(newType)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    
  } catch (error) {
    if (error.errors) {
      return // 表单验证错误
    }
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  formData.name = ''
  formData.description = ''
  formData.id = null
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 这里调用API获取产品类型列表
    await new Promise(resolve => setTimeout(resolve, 500))
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadData()
})
</script>

<style scoped lang="scss">
.product-types {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  h2 {
    margin: 0 0 4px 0;
    font-size: 24px;
    font-weight: 600;
    color: #333;
  }
  
  p {
    margin: 0;
    color: #666;
    font-size: 14px;
  }
}

.types-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.type-item {
  padding: 20px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: #409eff;
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  }
}

.type-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
}

.type-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.type-info {
  flex: 1;
  
  h4 {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }
  
  p {
    margin: 0 0 8px 0;
    color: #666;
    font-size: 14px;
    line-height: 1.4;
  }
  
  .type-count {
    font-size: 12px;
    color: #999;
    background: #f5f5f5;
    padding: 2px 8px;
    border-radius: 12px;
  }
}

.type-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .types-grid {
    grid-template-columns: 1fr;
  }
  
  .type-content {
    flex-direction: column;
    text-align: center;
  }
  
  .type-actions {
    justify-content: center;
  }
}
</style>
