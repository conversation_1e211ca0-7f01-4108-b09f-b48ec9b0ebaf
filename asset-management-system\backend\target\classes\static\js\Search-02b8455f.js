import{_ as ee}from"./_plugin-vue_export-helper-62491c14.js";/* empty css                      *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                *//* empty css                       *//* empty css               */import{u as le,r as E,b as z,o as ae,c as d,d as b,g as e,w as a,X as g,$ as te,E as oe,f as h,i as m,a2 as C,a3 as y,F as ne,M as re,a8 as A,j as R,n as se,k as ue,l as de,aD as pe,aC as ie,aL as ce,aM as me,aZ as _e,aU as fe,z as be,p as ge,aN as he,aO as Ve,aQ as ve,aR as Ee}from"./index-2733c819.js";import{h as Ce,i as ye}from"./asset-f5b5b286.js";const De={class:"asset-search"},Se={class:"card-header"},Pe={class:"card-header"},we={class:"pagination-wrapper"},Ne={__name:"Search",setup(Ue){const B=le(),S=E(!1),D=E(!1),P=E([]),w=E([]),N=E([]),o=z({assetCode:"",serialNumber:"",productModel:"",productType:"",brand:"",status:"",currentLocation:"",supplier:"",minPrice:null,maxPrice:null,purchaseDateStart:null,purchaseDateEnd:null,createdAtStart:null,createdAtEnd:null,sortBy:"createdAt",sortOrder:"desc",page:1,size:20}),r=z({page:1,size:20,total:0}),_=z({productTypes:[],brands:[],locations:[],suppliers:[],statuses:["PENDING","RECEIVED","INSTALLED","OUTBOUND","SCRAPPED"]}),k={PENDING:"待处理",RECEIVED:"已入库",INSTALLED:"已安装",OUTBOUND:"已出库",SCRAPPED:"已报废"},M={PENDING:"warning",RECEIVED:"success",INSTALLED:"primary",OUTBOUND:"info",SCRAPPED:"danger"},T=n=>k[n]||n,F=n=>M[n]||"info",j=async()=>{try{const{data:n}=await Ce();Object.assign(_,n)}catch(n){console.error("加载搜索选项失败:",n)}},G=n=>{n&&n.length===2?(o.purchaseDateStart=n[0],o.purchaseDateEnd=n[1]):(o.purchaseDateStart=null,o.purchaseDateEnd=null)},$=n=>{n&&n.length===2?(o.createdAtStart=n[0],o.createdAtEnd=n[1]):(o.createdAtStart=null,o.createdAtEnd=null)},V=async()=>{S.value=!0;try{o.page=r.page,o.size=r.size;const{data:n}=await ye(o);P.value=n.records,r.total=n.total,D.value=!0}catch(n){console.error("搜索失败:",n),R.error("搜索失败")}finally{S.value=!1}},x=()=>{Object.keys(o).forEach(n=>{n==="sortBy"?o[n]="createdAt":n==="sortOrder"?o[n]="desc":n==="page"?o[n]=1:n==="size"?o[n]=20:o[n]=n.includes("Price")?null:""}),w.value=[],N.value=[],r.page=1,r.size=20,r.total=0,D.value=!1,P.value=[]},Q=n=>{r.page=n,V()},X=n=>{r.size=n,r.page=1,V()},Z=n=>{B.push(`/assets/${n.id}`)},q=()=>{R.info("导出功能待实现")};return ae(()=>{j()}),(n,t)=>{const v=se,U=ue,u=de,s=pe,f=ie,p=ce,c=me,O=_e,I=fe,H=be,J=ge,L=oe,i=he,K=Ve,W=ve,Y=Ee;return d(),b("div",De,[e(L,{class:"search-card"},{header:a(()=>[h("div",Se,[t[17]||(t[17]=h("span",null,"高级搜索",-1)),e(v,{type:"primary",onClick:x},{default:a(()=>t[16]||(t[16]=[m("重置",-1)])),_:1,__:[16]})])]),default:a(()=>[e(J,{model:o,"label-width":"120px",inline:!1},{default:a(()=>[e(f,{gutter:20},{default:a(()=>[e(s,{span:8},{default:a(()=>[e(u,{label:"资产编号"},{default:a(()=>[e(U,{modelValue:o.assetCode,"onUpdate:modelValue":t[0]||(t[0]=l=>o.assetCode=l),placeholder:"输入资产编号",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:a(()=>[e(u,{label:"序列号"},{default:a(()=>[e(U,{modelValue:o.serialNumber,"onUpdate:modelValue":t[1]||(t[1]=l=>o.serialNumber=l),placeholder:"输入序列号",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:a(()=>[e(u,{label:"产品型号"},{default:a(()=>[e(U,{modelValue:o.productModel,"onUpdate:modelValue":t[2]||(t[2]=l=>o.productModel=l),placeholder:"输入产品型号",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(f,{gutter:20},{default:a(()=>[e(s,{span:8},{default:a(()=>[e(u,{label:"产品类型"},{default:a(()=>[e(c,{modelValue:o.productType,"onUpdate:modelValue":t[3]||(t[3]=l=>o.productType=l),placeholder:"选择产品类型",clearable:""},{default:a(()=>[(d(!0),b(C,null,y(_.productTypes,l=>(d(),g(p,{key:l,label:l,value:l},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:a(()=>[e(u,{label:"品牌"},{default:a(()=>[e(c,{modelValue:o.brand,"onUpdate:modelValue":t[4]||(t[4]=l=>o.brand=l),placeholder:"选择品牌",clearable:""},{default:a(()=>[(d(!0),b(C,null,y(_.brands,l=>(d(),g(p,{key:l,label:l,value:l},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:a(()=>[e(u,{label:"状态"},{default:a(()=>[e(c,{modelValue:o.status,"onUpdate:modelValue":t[5]||(t[5]=l=>o.status=l),placeholder:"选择状态",clearable:""},{default:a(()=>[(d(!0),b(C,null,y(_.statuses,l=>(d(),g(p,{key:l,label:T(l),value:l},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(f,{gutter:20},{default:a(()=>[e(s,{span:8},{default:a(()=>[e(u,{label:"当前位置"},{default:a(()=>[e(c,{modelValue:o.currentLocation,"onUpdate:modelValue":t[6]||(t[6]=l=>o.currentLocation=l),placeholder:"选择位置",clearable:""},{default:a(()=>[(d(!0),b(C,null,y(_.locations,l=>(d(),g(p,{key:l,label:l,value:l},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:a(()=>[e(u,{label:"供应商"},{default:a(()=>[e(c,{modelValue:o.supplier,"onUpdate:modelValue":t[7]||(t[7]=l=>o.supplier=l),placeholder:"选择供应商",clearable:""},{default:a(()=>[(d(!0),b(C,null,y(_.suppliers,l=>(d(),g(p,{key:l,label:l,value:l},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:a(()=>[e(u,{label:"价格范围"},{default:a(()=>[e(f,{gutter:10},{default:a(()=>[e(s,{span:12},{default:a(()=>[e(O,{modelValue:o.minPrice,"onUpdate:modelValue":t[8]||(t[8]=l=>o.minPrice=l),placeholder:"最低价格",min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(s,{span:12},{default:a(()=>[e(O,{modelValue:o.maxPrice,"onUpdate:modelValue":t[9]||(t[9]=l=>o.maxPrice=l),placeholder:"最高价格",min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),e(f,{gutter:20},{default:a(()=>[e(s,{span:12},{default:a(()=>[e(u,{label:"采购日期"},{default:a(()=>[e(I,{modelValue:w.value,"onUpdate:modelValue":t[10]||(t[10]=l=>w.value=l),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",onChange:G,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:12},{default:a(()=>[e(u,{label:"创建时间"},{default:a(()=>[e(I,{modelValue:N.value,"onUpdate:modelValue":t[11]||(t[11]=l=>N.value=l),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",onChange:$,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(f,null,{default:a(()=>[e(s,{span:24},{default:a(()=>[e(u,null,{default:a(()=>[e(v,{type:"primary",onClick:V,loading:S.value},{default:a(()=>[e(H,null,{default:a(()=>[e(ne(re))]),_:1}),t[18]||(t[18]=m(" 搜索 ",-1))]),_:1,__:[18]},8,["loading"]),e(v,{onClick:x},{default:a(()=>t[19]||(t[19]=[m("重置",-1)])),_:1,__:[19]}),e(v,{onClick:q,disabled:!D.value},{default:a(()=>t[20]||(t[20]=[m("导出结果",-1)])),_:1,__:[20]},8,["disabled"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),D.value?(d(),g(L,{key:0,class:"result-card"},{header:a(()=>[h("div",Pe,[h("span",null,"搜索结果 (共 "+A(r.total)+" 条)",1),h("div",null,[e(c,{modelValue:o.sortBy,"onUpdate:modelValue":t[12]||(t[12]=l=>o.sortBy=l),onChange:V,style:{width:"120px","margin-right":"10px"}},{default:a(()=>[e(p,{label:"创建时间",value:"createdAt"}),e(p,{label:"资产编号",value:"assetCode"}),e(p,{label:"采购日期",value:"purchaseDate"}),e(p,{label:"采购价格",value:"purchasePrice"})]),_:1},8,["modelValue"]),e(c,{modelValue:o.sortOrder,"onUpdate:modelValue":t[13]||(t[13]=l=>o.sortOrder=l),onChange:V,style:{width:"80px"}},{default:a(()=>[e(p,{label:"降序",value:"desc"}),e(p,{label:"升序",value:"asc"})]),_:1},8,["modelValue"])])])]),default:a(()=>[e(W,{data:P.value,stripe:""},{default:a(()=>[e(i,{prop:"assetCode",label:"资产编号",width:"120"}),e(i,{prop:"serialNumber",label:"序列号",width:"120"}),e(i,{prop:"productModel",label:"产品型号",width:"150"}),e(i,{prop:"productType",label:"产品类型",width:"120"}),e(i,{prop:"brand",label:"品牌",width:"100"}),e(i,{prop:"currentLocation",label:"当前位置",width:"120"}),e(i,{prop:"status",label:"状态",width:"100"},{default:a(({row:l})=>[e(K,{type:F(l.status)},{default:a(()=>[m(A(T(l.status)),1)]),_:2},1032,["type"])]),_:1}),e(i,{prop:"purchasePrice",label:"采购价格",width:"120"},{default:a(({row:l})=>[m(" ¥"+A(l.purchasePrice?l.purchasePrice.toFixed(2):"0.00"),1)]),_:1}),e(i,{prop:"purchaseDate",label:"采购日期",width:"120"}),e(i,{label:"操作",width:"120",fixed:"right"},{default:a(({row:l})=>[e(v,{type:"primary",size:"small",onClick:ze=>Z(l)},{default:a(()=>t[21]||(t[21]=[m(" 查看 ",-1)])),_:2,__:[21]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),h("div",we,[e(Y,{"current-page":r.page,"onUpdate:currentPage":t[14]||(t[14]=l=>r.page=l),"page-size":r.size,"onUpdate:pageSize":t[15]||(t[15]=l=>r.size=l),total:r.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:X,onCurrentChange:Q},null,8,["current-page","page-size","total"])])]),_:1})):te("",!0)])}}},Qe=ee(Ne,[["__scopeId","data-v-a82b215a"]]);export{Qe as default};
