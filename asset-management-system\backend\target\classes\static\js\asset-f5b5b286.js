import{aT as e}from"./index-2733c819.js";function a(t){return e({url:"/assets",method:"get",params:t})}function n(t){return e({url:`/assets/${t}`,method:"get"})}function u(t){return e({url:"/assets",method:"post",data:t})}function o(t,s){return e({url:`/assets/${t}`,method:"put",data:s})}function c(t){return e({url:`/assets/${t}`,method:"delete"})}function d(t){return e({url:"/assets/batch",method:"delete",data:t})}function i(){return e({url:"/assets/product-types",method:"get"})}function h(){return e({url:"/assets/locations",method:"get"})}function l(t){return e({url:"/assets/search",method:"post",data:t})}function f(){return e({url:"/assets/search-options",method:"get"})}function g(){return e({url:"/assets/warranty-expiry",method:"get"})}export{i as a,d as b,h as c,c as d,n as e,u as f,a as g,f as h,l as i,g as j,o as u};
