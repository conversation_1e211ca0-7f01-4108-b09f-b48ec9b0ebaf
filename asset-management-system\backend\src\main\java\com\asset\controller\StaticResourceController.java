package com.asset.controller;

import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * 静态资源控制器 - 专门处理前端静态资源
 * 解决MIME类型问题
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
public class StaticResourceController {

    /**
     * 处理CSS文件请求
     */
    @GetMapping("/assets/{filename:.+\\.css}")
    public ResponseEntity<Resource> getCssFile(@PathVariable String filename) {
        try {
            Resource resource = new ClassPathResource("static/assets/" + filename);
            if (resource.exists()) {
                return ResponseEntity.ok()
                        .header(HttpHeaders.CONTENT_TYPE, "text/css")
                        .body(resource);
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return ResponseEntity.notFound().build();
    }

    /**
     * 处理JS文件请求
     */
    @GetMapping("/assets/{filename:.+\\.js}")
    public ResponseEntity<Resource> getJsFile(@PathVariable String filename) {
        try {
            Resource resource = new ClassPathResource("static/assets/" + filename);
            if (resource.exists()) {
                return ResponseEntity.ok()
                        .header(HttpHeaders.CONTENT_TYPE, "application/javascript")
                        .body(resource);
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return ResponseEntity.notFound().build();
    }

    /**
     * 处理JS目录下的文件请求
     */
    @GetMapping("/js/{filename:.+}")
    public ResponseEntity<Resource> getJsDirectoryFile(@PathVariable String filename) {
        try {
            Resource resource = new ClassPathResource("static/js/" + filename);
            if (resource.exists()) {
                String contentType = filename.endsWith(".js") ? "application/javascript" : "text/plain";
                return ResponseEntity.ok()
                        .header(HttpHeaders.CONTENT_TYPE, contentType)
                        .body(resource);
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return ResponseEntity.notFound().build();
    }

    /**
     * 处理图标文件
     */
    @GetMapping("/{filename:.+\\.(ico|png|jpg|jpeg|gif|svg)}")
    public ResponseEntity<Resource> getImageFile(@PathVariable String filename) {
        try {
            Resource resource = new ClassPathResource("static/" + filename);
            if (resource.exists()) {
                String contentType = getImageContentType(filename);
                return ResponseEntity.ok()
                        .header(HttpHeaders.CONTENT_TYPE, contentType)
                        .body(resource);
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return ResponseEntity.notFound().build();
    }

    /**
     * 获取图片文件的Content-Type
     */
    private String getImageContentType(String filename) {
        String ext = filename.substring(filename.lastIndexOf('.') + 1).toLowerCase();
        switch (ext) {
            case "ico": return "image/x-icon";
            case "png": return "image/png";
            case "jpg":
            case "jpeg": return "image/jpeg";
            case "gif": return "image/gif";
            case "svg": return "image/svg+xml";
            default: return "application/octet-stream";
        }
    }

    /**
     * 处理前端路由 - 返回index.html
     */
    @GetMapping(value = {"/", "/login", "/dashboard", "/assets/**", "/inventory/**", "/reports/**"})
    public ResponseEntity<Resource> getIndexHtml(HttpServletRequest request) {
        String path = request.getRequestURI();
        
        // 如果是静态资源请求但上面的方法没有处理到，返回404
        if (path.startsWith("/assets/") || path.startsWith("/js/") || path.contains(".")) {
            return ResponseEntity.notFound().build();
        }
        
        // 前端路由，返回index.html
        try {
            Resource resource = new ClassPathResource("static/index.html");
            if (resource.exists()) {
                return ResponseEntity.ok()
                        .header(HttpHeaders.CONTENT_TYPE, "text/html")
                        .body(resource);
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return ResponseEntity.notFound().build();
    }
}
