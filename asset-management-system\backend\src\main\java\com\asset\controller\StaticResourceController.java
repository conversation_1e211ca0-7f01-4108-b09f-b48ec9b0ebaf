package com.asset.controller;

import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * 静态资源控制器 - 专门处理前端路由
 * 让Spring Boot默认处理静态资源，这里只处理前端路由
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
public class StaticResourceController {

    /**
     * 处理根路径
     */
    @GetMapping("/")
    public ResponseEntity<Resource> getRoot() {
        try {
            Resource resource = new ClassPathResource("static/index.html");
            if (resource.exists()) {
                return ResponseEntity.ok()
                        .header(HttpHeaders.CONTENT_TYPE, "text/html; charset=UTF-8")
                        .body(resource);
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return ResponseEntity.notFound().build();
    }
    
    /**
     * 处理前端路由 - 返回index.html
     * 只处理特定的前端路由路径，不包含assets等静态资源路径
     */
    @GetMapping(value = {"/login", "/dashboard", "/inventory", "/reports", "/users", "/settings"})
    public ResponseEntity<Resource> getIndexHtml() {
        try {
            Resource resource = new ClassPathResource("static/index.html");
            if (resource.exists()) {
                return ResponseEntity.ok()
                        .header(HttpHeaders.CONTENT_TYPE, "text/html; charset=UTF-8")
                        .body(resource);
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return ResponseEntity.notFound().build();
    }
}
