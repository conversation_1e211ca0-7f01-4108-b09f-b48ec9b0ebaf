import{_ as le}from"./_plugin-vue_export-helper-62491c14.js";/* empty css                   *//* empty css                   *//* empty css                       *//* empty css                 *//* empty css                     *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                */import{r as _,b as w,o as te,c as D,d as ae,f,g as e,w as t,j as b,E as oe,aV as ne,F as N,M as se,i as s,aH as de,P as re,H as ue,X as S,a8 as U,$ as ie,y as pe,aB as me,k as ce,l as _e,aL as fe,aM as ge,z as be,n as ve,p as Ce,aN as ye,aO as Ve,aQ as Ee,aR as we,aU as De,aS as Ne}from"./index-2733c819.js";const ke={class:"inbound-container"},Ie={class:"card-header"},Me={class:"pagination-container"},he={__name:"Inbound",setup(Te){const v=_(!1),C=_(!1),k=_([]),y=_([]),g=_(!1),V=_(),u=w({assetCode:"",productModel:"",status:""}),d=w({current:1,pageSize:20,total:0}),n=w({assetId:null,assetCode:"",productModel:"",receiverName:"",inboundTime:new Date,remarks:""}),z={receiverName:[{required:!0,message:"请输入签收人姓名",trigger:"blur"}],inboundTime:[{required:!0,message:"请选择入库时间",trigger:"change"}]},m=async()=>{v.value=!0;try{k.value=[],d.total=0}catch(o){b.error("加载数据失败: "+o.message)}finally{v.value=!1}},I=()=>{d.current=1,m()},B=()=>{Object.assign(u,{assetCode:"",productModel:"",status:""}),I()},R=o=>{y.value=o},F=o=>{Object.assign(n,{assetId:o.id,assetCode:o.assetCode,productModel:o.productModel,receiverName:"",inboundTime:new Date,remarks:""}),g.value=!0},O=async()=>{try{await V.value.validate(),C.value=!0,b.success("入库成功"),g.value=!1,m()}catch(o){o.message&&b.error("入库失败: "+o.message)}finally{C.value=!1}},P=()=>{if(y.value.length===0){b.warning("请选择要入库的资产");return}pe.confirm(`确定要批量入库选中的 ${y.value.length} 个资产吗？`,"批量入库确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{b.success("批量入库成功"),m()})},L=o=>{console.log("查看详情:",o)},$=o=>{console.log("查看历史:",o)},j=()=>{var o;(o=V.value)==null||o.resetFields()},H=o=>{d.pageSize=o,m()},A=o=>{d.current=o,m()},G=o=>({PENDING:"warning",RECEIVED:"success",INSTALLED:"info",OUTBOUND:"danger"})[o]||"info",Y=o=>({PENDING:"待入库",RECEIVED:"已入库",INSTALLED:"已安装",OUTBOUND:"已出库"})[o]||o,q=o=>me(o).format("YYYY-MM-DD HH:mm:ss");return te(()=>{m()}),(o,l)=>{const c=ce,r=_e,M=fe,Q=ge,E=be,i=ve,h=Ce,T=oe,p=ye,X=Ve,J=Ee,K=we,W=De,Z=ne,ee=Ne;return D(),ae("div",ke,[l[21]||(l[21]=f("div",{class:"page-header"},[f("h2",null,"入库管理"),f("p",null,"管理资产的入库操作和记录")],-1)),e(T,{class:"search-card",shadow:"never"},{default:t(()=>[e(h,{model:u,inline:"",class:"search-form"},{default:t(()=>[e(r,{label:"资产编号"},{default:t(()=>[e(c,{modelValue:u.assetCode,"onUpdate:modelValue":l[0]||(l[0]=a=>u.assetCode=a),placeholder:"请输入资产编号",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(r,{label:"产品型号"},{default:t(()=>[e(c,{modelValue:u.productModel,"onUpdate:modelValue":l[1]||(l[1]=a=>u.productModel=a),placeholder:"请输入产品型号",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(r,{label:"状态"},{default:t(()=>[e(Q,{modelValue:u.status,"onUpdate:modelValue":l[2]||(l[2]=a=>u.status=a),placeholder:"请选择状态",clearable:"",style:{width:"150px"}},{default:t(()=>[e(M,{label:"待入库",value:"PENDING"}),e(M,{label:"已入库",value:"RECEIVED"})]),_:1},8,["modelValue"])]),_:1}),e(r,null,{default:t(()=>[e(i,{type:"primary",onClick:I},{default:t(()=>[e(E,null,{default:t(()=>[e(N(se))]),_:1}),l[12]||(l[12]=s(" 搜索 ",-1))]),_:1,__:[12]}),e(i,{onClick:B},{default:t(()=>[e(E,null,{default:t(()=>[e(N(de))]),_:1}),l[13]||(l[13]=s(" 重置 ",-1))]),_:1,__:[13]})]),_:1})]),_:1},8,["model"])]),_:1}),e(T,{class:"table-card",shadow:"never"},{header:t(()=>[f("div",Ie,[l[15]||(l[15]=f("span",null,"待入库资产列表",-1)),e(i,{type:"primary",onClick:P},{default:t(()=>[e(E,null,{default:t(()=>[e(N(re))]),_:1}),l[14]||(l[14]=s(" 批量入库 ",-1))]),_:1,__:[14]})])]),default:t(()=>[ue((D(),S(J,{data:k.value,onSelectionChange:R,style:{width:"100%"}},{default:t(()=>[e(p,{type:"selection",width:"55"}),e(p,{prop:"assetCode",label:"资产编号",width:"120"}),e(p,{prop:"serialNumber",label:"序列号",width:"150"}),e(p,{prop:"productModel",label:"产品型号",width:"150"}),e(p,{prop:"productType",label:"产品类型",width:"120"}),e(p,{prop:"status",label:"状态",width:"100"},{default:t(({row:a})=>[e(X,{type:G(a.status)},{default:t(()=>[s(U(Y(a.status)),1)]),_:2},1032,["type"])]),_:1}),e(p,{prop:"createdAt",label:"创建时间",width:"180"},{default:t(({row:a})=>[s(U(q(a.createdAt)),1)]),_:1}),e(p,{label:"操作",width:"200",fixed:"right"},{default:t(({row:a})=>[a.status==="PENDING"?(D(),S(i,{key:0,type:"primary",size:"small",onClick:x=>F(a)},{default:t(()=>l[16]||(l[16]=[s(" 入库 ",-1)])),_:2,__:[16]},1032,["onClick"])):ie("",!0),e(i,{type:"info",size:"small",onClick:x=>L(a)},{default:t(()=>l[17]||(l[17]=[s(" 详情 ",-1)])),_:2,__:[17]},1032,["onClick"]),e(i,{type:"success",size:"small",onClick:x=>$(a)},{default:t(()=>l[18]||(l[18]=[s(" 历史 ",-1)])),_:2,__:[18]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ee,v.value]]),f("div",Me,[e(K,{"current-page":d.current,"onUpdate:currentPage":l[3]||(l[3]=a=>d.current=a),"page-size":d.pageSize,"onUpdate:pageSize":l[4]||(l[4]=a=>d.pageSize=a),total:d.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:H,onCurrentChange:A},null,8,["current-page","page-size","total"])])]),_:1}),e(Z,{modelValue:g.value,"onUpdate:modelValue":l[11]||(l[11]=a=>g.value=a),title:"资产入库",width:"600px",onClose:j},{footer:t(()=>[e(i,{onClick:l[10]||(l[10]=a=>g.value=!1)},{default:t(()=>l[19]||(l[19]=[s("取消",-1)])),_:1,__:[19]}),e(i,{type:"primary",onClick:O,loading:C.value},{default:t(()=>l[20]||(l[20]=[s(" 确认入库 ",-1)])),_:1,__:[20]},8,["loading"])]),default:t(()=>[e(h,{ref_key:"inboundFormRef",ref:V,model:n,rules:z,"label-width":"100px"},{default:t(()=>[e(r,{label:"资产编号"},{default:t(()=>[e(c,{modelValue:n.assetCode,"onUpdate:modelValue":l[5]||(l[5]=a=>n.assetCode=a),disabled:""},null,8,["modelValue"])]),_:1}),e(r,{label:"产品型号"},{default:t(()=>[e(c,{modelValue:n.productModel,"onUpdate:modelValue":l[6]||(l[6]=a=>n.productModel=a),disabled:""},null,8,["modelValue"])]),_:1}),e(r,{label:"签收人",prop:"receiverName"},{default:t(()=>[e(c,{modelValue:n.receiverName,"onUpdate:modelValue":l[7]||(l[7]=a=>n.receiverName=a),placeholder:"请输入签收人姓名"},null,8,["modelValue"])]),_:1}),e(r,{label:"入库时间",prop:"inboundTime"},{default:t(()=>[e(W,{modelValue:n.inboundTime,"onUpdate:modelValue":l[8]||(l[8]=a=>n.inboundTime=a),type:"datetime",placeholder:"选择入库时间",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(r,{label:"备注"},{default:t(()=>[e(c,{modelValue:n.remarks,"onUpdate:modelValue":l[9]||(l[9]=a=>n.remarks=a),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},Ge=le(he,[["__scopeId","data-v-528d2dea"]]);export{Ge as default};
