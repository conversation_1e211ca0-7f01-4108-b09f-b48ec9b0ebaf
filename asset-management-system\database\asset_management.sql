/*
 Navicat Premium Dump SQL

 Source Server         : localhost_3306
 Source Server Type    : MySQL
 Source Server Version : 80041 (8.0.41)
 Source Host           : 127.0.0.1:3306
 Source Schema         : asset_management

 Target Server Type    : MySQL
 Target Server Version : 80041 (8.0.41)
 File Encoding         : 65001

 Date: 28/07/2025 01:05:21
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for asset_history
-- ----------------------------
DROP TABLE IF EXISTS `asset_history`;
CREATE TABLE `asset_history`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `asset_id` bigint NOT NULL COMMENT '资产ID',
  `operation_type` enum('CREATE','UPDATE','RECEIVE','INSTALL','MOVE','OUTBOUND','NOTE') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作类型',
  `old_value` json NULL COMMENT '变更前的值',
  `new_value` json NULL COMMENT '变更后的值',
  `operator` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作人员',
  `reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作原因',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '操作描述',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_asset_id`(`asset_id` ASC) USING BTREE,
  INDEX `idx_operation_type`(`operation_type` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  INDEX `idx_operator`(`operator` ASC) USING BTREE,
  CONSTRAINT `asset_history_ibfk_1` FOREIGN KEY (`asset_id`) REFERENCES `assets` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '资产历史记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of asset_history
-- ----------------------------
INSERT INTO `asset_history` VALUES (1, 1, 'CREATE', NULL, NULL, 'admin', NULL, '创建资产记录', '2025-07-26 22:54:30');
INSERT INTO `asset_history` VALUES (2, 2, 'CREATE', NULL, NULL, 'admin', NULL, '创建资产记录', '2025-07-26 22:54:30');
INSERT INTO `asset_history` VALUES (3, 2, 'RECEIVE', NULL, NULL, 'user', NULL, '确认接收资产', '2025-07-26 22:54:30');
INSERT INTO `asset_history` VALUES (4, 3, 'CREATE', NULL, NULL, 'admin', NULL, '创建资产记录', '2025-07-26 22:54:30');
INSERT INTO `asset_history` VALUES (5, 3, 'RECEIVE', NULL, NULL, 'user', NULL, '确认接收资产', '2025-07-26 22:54:30');
INSERT INTO `asset_history` VALUES (6, 3, 'INSTALL', NULL, NULL, 'user', NULL, '完成资产安装', '2025-07-26 22:54:30');
INSERT INTO `asset_history` VALUES (7, 4, 'CREATE', NULL, NULL, 'admin', NULL, '创建资产记录', '2025-07-26 22:54:30');
INSERT INTO `asset_history` VALUES (8, 4, 'RECEIVE', NULL, NULL, 'user', NULL, '确认接收资产', '2025-07-26 22:54:30');
INSERT INTO `asset_history` VALUES (9, 4, 'INSTALL', NULL, NULL, 'user', NULL, '完成资产安装', '2025-07-26 22:54:30');
INSERT INTO `asset_history` VALUES (10, 5, 'CREATE', NULL, NULL, 'admin', NULL, '创建资产记录', '2025-07-26 22:54:30');

-- ----------------------------
-- Table structure for asset_notes
-- ----------------------------
DROP TABLE IF EXISTS `asset_notes`;
CREATE TABLE `asset_notes`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `asset_id` bigint NOT NULL COMMENT '资产ID',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '备注内容',
  `operator` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作人员',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除标志',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_asset_id`(`asset_id` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  INDEX `idx_operator`(`operator` ASC) USING BTREE,
  CONSTRAINT `asset_notes_ibfk_1` FOREIGN KEY (`asset_id`) REFERENCES `assets` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '资产备注表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of asset_notes
-- ----------------------------

-- ----------------------------
-- Table structure for assets
-- ----------------------------
DROP TABLE IF EXISTS `assets`;
CREATE TABLE `assets`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `asset_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '资产编号',
  `serial_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '序列号',
  `product_model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '产品型号',
  `product_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '产品类型',
  `brand` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '品牌',
  `specification` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '规格说明',
  `purchase_date` date NULL DEFAULT NULL COMMENT '采购日期',
  `purchase_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '采购价格',
  `supplier` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '供应商',
  `warranty_period` int NULL DEFAULT NULL COMMENT '保修期（月）',
  `status` enum('PENDING','RECEIVED','INSTALLED','OUTBOUND','SCRAPPED') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'PENDING' COMMENT '资产状态',
  `current_location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '当前位置',
  `receiver` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '签收人',
  `received_at` timestamp NULL DEFAULT NULL COMMENT '入库时间',
  `installer` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '安装人员',
  `installed_at` timestamp NULL DEFAULT NULL COMMENT '安装时间',
  `install_location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '安装位置',
  `outbound_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '出库原因',
  `outbound_operator` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '出库操作人',
  `outbound_at` timestamp NULL DEFAULT NULL COMMENT '出库时间',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注信息',
  `user_id` bigint NOT NULL COMMENT '所属用户ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除标志',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_asset_code`(`asset_code` ASC) USING BTREE,
  INDEX `idx_serial_number`(`serial_number` ASC) USING BTREE,
  INDEX `idx_product_type`(`product_type` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_current_location`(`current_location` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `assets_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '资产表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of assets
-- ----------------------------
INSERT INTO `assets` VALUES (1, 'IT-2024-001', 'SN001234567', 'ThinkPad X1 Carbon', '笔记本电脑', '联想', NULL, NULL, NULL, NULL, NULL, 'PENDING', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '2025-07-26 22:54:30', '2025-07-26 22:54:30', 0);
INSERT INTO `assets` VALUES (2, 'IT-2024-002', 'SN001234568', 'OptiPlex 7090', '台式电脑', '戴尔', NULL, NULL, NULL, NULL, NULL, 'RECEIVED', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '2025-07-26 22:54:30', '2025-07-26 22:54:30', 0);
INSERT INTO `assets` VALUES (3, 'IT-2024-003', 'SN001234569', 'LaserJet Pro M404n', '激光打印机', 'HP', NULL, NULL, NULL, NULL, NULL, 'INSTALLED', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '2025-07-26 22:54:30', '2025-07-26 22:54:30', 0);
INSERT INTO `assets` VALUES (4, 'IT-2024-004', 'SN001234570', 'U2722DE', '显示器', '戴尔', NULL, NULL, NULL, NULL, NULL, 'INSTALLED', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '2025-07-26 22:54:30', '2025-07-26 22:54:30', 0);
INSERT INTO `assets` VALUES (5, 'IT-2024-005', 'SN001234571', 'Surface Pro 9', '平板电脑', '微软', NULL, NULL, NULL, NULL, NULL, 'PENDING', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, '2025-07-26 22:54:30', '2025-07-26 22:54:30', 0);

-- ----------------------------
-- Table structure for location_history
-- ----------------------------
DROP TABLE IF EXISTS `location_history`;
CREATE TABLE `location_history`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `asset_id` bigint NOT NULL COMMENT '资产ID',
  `from_location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '原位置',
  `to_location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '新位置',
  `move_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '移动原因',
  `operator` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作人员',
  `moved_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '移动时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_asset_id`(`asset_id` ASC) USING BTREE,
  INDEX `idx_moved_at`(`moved_at` ASC) USING BTREE,
  INDEX `idx_operator`(`operator` ASC) USING BTREE,
  CONSTRAINT `location_history_ibfk_1` FOREIGN KEY (`asset_id`) REFERENCES `assets` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '位置历史表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of location_history
-- ----------------------------

-- ----------------------------
-- Table structure for permission_config
-- ----------------------------
DROP TABLE IF EXISTS `permission_config`;
CREATE TABLE `permission_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `module_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '功能模块名称',
  `user_permission` tinyint(1) NULL DEFAULT 0 COMMENT '普通用户权限',
  `admin_permission` tinyint(1) NULL DEFAULT 1 COMMENT '管理员权限',
  `required_permission` tinyint(1) NULL DEFAULT 0 COMMENT '是否必需权限',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `module_name`(`module_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '权限配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of permission_config
-- ----------------------------
INSERT INTO `permission_config` VALUES (1, '资产管理', 1, 1, 0, '2025-07-28 00:47:37', '2025-07-28 00:50:30');
INSERT INTO `permission_config` VALUES (2, '资产搜索', 1, 1, 0, '2025-07-28 00:47:37', '2025-07-28 00:50:31');
INSERT INTO `permission_config` VALUES (3, '位置跟踪', 1, 1, 0, '2025-07-28 00:47:37', '2025-07-28 00:50:31');
INSERT INTO `permission_config` VALUES (4, '导入导出', 1, 1, 0, '2025-07-28 00:47:37', '2025-07-28 00:50:31');
INSERT INTO `permission_config` VALUES (5, '资产操作', 1, 1, 0, '2025-07-28 00:47:37', '2025-07-28 00:50:31');
INSERT INTO `permission_config` VALUES (6, '统计报表', 1, 1, 0, '2025-07-28 00:47:37', '2025-07-28 00:50:31');
INSERT INTO `permission_config` VALUES (7, '库存统计', 1, 1, 0, '2025-07-28 00:47:37', '2025-07-28 00:50:31');
INSERT INTO `permission_config` VALUES (8, '用户管理', 0, 1, 1, '2025-07-28 00:47:37', '2025-07-28 00:50:31');
INSERT INTO `permission_config` VALUES (9, '系统设置', 0, 1, 1, '2025-07-28 00:47:37', '2025-07-28 00:50:31');

-- ----------------------------
-- Table structure for system_config
-- ----------------------------
DROP TABLE IF EXISTS `system_config`;
CREATE TABLE `system_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '配置键',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '配置值',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '配置描述',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `config_key`(`config_key` ASC) USING BTREE,
  INDEX `idx_config_key`(`config_key` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of system_config
-- ----------------------------
INSERT INTO `system_config` VALUES (1, 'asset_code_prefix', 'IT', '资产编号前缀', '2025-07-26 22:54:30', '2025-07-26 22:54:30');
INSERT INTO `system_config` VALUES (2, 'asset_code_format', 'IT-YYYY-NNN', '资产编号格式', '2025-07-26 22:54:30', '2025-07-26 22:54:30');
INSERT INTO `system_config` VALUES (3, 'default_warranty_period', '36', '默认保修期（月）', '2025-07-26 22:54:30', '2025-07-26 22:54:30');
INSERT INTO `system_config` VALUES (4, 'max_upload_size', '50', '最大上传文件大小（MB）', '2025-07-26 22:54:30', '2025-07-26 22:54:30');
INSERT INTO `system_config` VALUES (5, 'export_batch_size', '1000', '导出批处理大小', '2025-07-26 22:54:30', '2025-07-26 22:54:30');

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '密码（加密存储）',
  `role` enum('USER','ADMIN') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'USER' COMMENT '用户角色',
  `status` enum('ACTIVE','INACTIVE') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'ACTIVE' COMMENT '用户状态',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '电话',
  `real_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '真实姓名',
  `department` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '部门',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '逻辑删除标志',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username` ASC) USING BTREE,
  INDEX `idx_username`(`username` ASC) USING BTREE,
  INDEX `idx_role`(`role` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO `users` VALUES (2, 'user', '$2a$10$8.7T8wKKKXNKJyY2R3Q8qe8BoT9J.z4.9Y5UFK8VXYxJKGQx5YxnO', 'USER', 'ACTIVE', '<EMAIL>', NULL, '测试用户', 'IT部门', '2025-07-26 22:54:30', '2025-07-26 22:54:30', 0);
INSERT INTO `users` VALUES (3, 'admin', '$2a$10$Kus1sm/MWEglPZohPiGNg.1X1GMsMJIafKH13RyfVfehHzWDcIBM2', 'ADMIN', 'ACTIVE', '<EMAIL>', NULL, '系统管理员', NULL, '2025-07-27 11:59:31', '2025-07-27 12:03:02', 0);
INSERT INTO `users` VALUES (4, 'xp666', '$2a$10$PMRzhMH5RPIoAaTATFFxBucqHD0zk3xJMPfSV.F3mQjXziEEBWdqO', 'USER', 'ACTIVE', '<EMAIL>', '', 'xp', '', '2025-07-28 00:16:52', '2025-07-28 00:16:52', 0);

-- ----------------------------
-- View structure for asset_statistics
-- ----------------------------
DROP VIEW IF EXISTS `asset_statistics`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `asset_statistics` AS select `u`.`username` AS `username`,`u`.`real_name` AS `real_name`,count(`a`.`id`) AS `total_assets`,count((case when (`a`.`status` = 'PENDING') then 1 end)) AS `pending_count`,count((case when (`a`.`status` = 'RECEIVED') then 1 end)) AS `received_count`,count((case when (`a`.`status` = 'INSTALLED') then 1 end)) AS `installed_count`,count((case when (`a`.`status` = 'OUTBOUND') then 1 end)) AS `outbound_count`,count((case when (`a`.`status` = 'SCRAPPED') then 1 end)) AS `scrapped_count` from (`users` `u` left join `assets` `a` on(((`u`.`id` = `a`.`user_id`) and (`a`.`deleted` = 0)))) where (`u`.`deleted` = 0) group by `u`.`id`,`u`.`username`,`u`.`real_name`;

SET FOREIGN_KEY_CHECKS = 1;
