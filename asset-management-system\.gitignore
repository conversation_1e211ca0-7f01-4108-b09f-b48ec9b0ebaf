# 编译输出
backend/target/
backend/build/
frontend/dist/
frontend/build/

# 依赖目录
backend/.mvn/
frontend/node_modules/
frontend/.pnpm-store/

# IDE文件
.idea/
.vscode/
*.iml
*.ipr
*.iws
.project
.classpath
.settings/

# 系统文件
.DS_Store
Thumbs.db
*.log
*.tmp
*.temp
*~
NUL

# 环境配置
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 数据库
*.db
*.sqlite
*.sqlite3

# 缓存文件
.cache/
*.cache
.npm/
.yarn/

# 临时文件
*.swp
*.swo
*~

# Java相关
*.class
*.jar
*.war
*.ear
hs_err_pid*

# Node.js相关
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# 前端构建相关
frontend/.nuxt/
frontend/.next/
frontend/.vuepress/dist/
frontend/.serverless/
frontend/.fusebox/
frontend/.dynamodb/

# 测试覆盖率
coverage/
*.lcov
.nyc_output/

# 运行时文件
*.pid
*.seed
*.pid.lock

# 可选的npm缓存目录
.npm

# 可选的eslint缓存
.eslintcache

# 微信小程序
.miniprogram/

# TypeScript
*.tsbuildinfo

# Vite
frontend/vite.config.js.timestamp-*
