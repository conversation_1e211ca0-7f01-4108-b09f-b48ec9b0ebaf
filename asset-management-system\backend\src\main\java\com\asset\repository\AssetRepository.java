package com.asset.repository;

import com.asset.entity.Asset;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Mapper
public interface AssetRepository extends BaseMapper<Asset> {

    /**
     * 根据资产编号查找资产
     */
    @Select("SELECT * FROM assets WHERE asset_code = #{assetCode} AND deleted = 0")
    Optional<Asset> findByAssetCode(@Param("assetCode") String assetCode);

    /**
     * 根据序列号查找资产
     */
    @Select("SELECT * FROM assets WHERE serial_number = #{serialNumber} AND deleted = 0")
    Optional<Asset> findBySerialNumber(@Param("serialNumber") String serialNumber);

    /**
     * 分页查询用户的资产列表
     */
    @Select("SELECT * FROM assets WHERE user_id = #{userId} AND deleted = 0 " +
            "AND (#{keyword} IS NULL OR asset_code LIKE CONCAT('%', #{keyword}, '%') " +
            "OR serial_number LIKE CONCAT('%', #{keyword}, '%') " +
            "OR product_model LIKE CONCAT('%', #{keyword}, '%') " +
            "OR product_type LIKE CONCAT('%', #{keyword}, '%') " +
            "OR brand LIKE CONCAT('%', #{keyword}, '%')) " +
            "AND (#{status} IS NULL OR status = #{status}) " +
            "AND (#{productType} IS NULL OR product_type = #{productType}) " +
            "ORDER BY created_at DESC")
    IPage<Asset> findUserAssetsWithPagination(
            Page<Asset> page,
            @Param("userId") Long userId,
            @Param("keyword") String keyword,
            @Param("status") Asset.AssetStatus status,
            @Param("productType") String productType
    );

    /**
     * 分页查询所有资产列表（管理员使用）
     */
    @Select("SELECT a.*, u.username, u.real_name FROM assets a " +
            "LEFT JOIN users u ON a.user_id = u.id " +
            "WHERE a.deleted = 0 " +
            "AND (#{keyword} IS NULL OR a.asset_code LIKE CONCAT('%', #{keyword}, '%') " +
            "OR a.serial_number LIKE CONCAT('%', #{keyword}, '%') " +
            "OR a.product_model LIKE CONCAT('%', #{keyword}, '%') " +
            "OR a.product_type LIKE CONCAT('%', #{keyword}, '%') " +
            "OR a.brand LIKE CONCAT('%', #{keyword}, '%') " +
            "OR u.username LIKE CONCAT('%', #{keyword}, '%') " +
            "OR u.real_name LIKE CONCAT('%', #{keyword}, '%')) " +
            "AND (#{status} IS NULL OR a.status = #{status}) " +
            "AND (#{productType} IS NULL OR a.product_type = #{productType}) " +
            "AND (#{userId} IS NULL OR a.user_id = #{userId}) " +
            "ORDER BY a.created_at DESC")
    IPage<Asset> findAllAssetsWithPagination(
            Page<Asset> page,
            @Param("keyword") String keyword,
            @Param("status") Asset.AssetStatus status,
            @Param("productType") String productType,
            @Param("userId") Long userId
    );

    /**
     * 根据状态统计资产数量
     */
    @Select("SELECT status, COUNT(1) as count FROM assets " +
            "WHERE deleted = 0 " +
            "GROUP BY status")
    List<Object> countAssetsByStatus();

    /**
     * 根据产品类型统计资产数量
     */
    @Select("SELECT product_type, COUNT(1) as count FROM assets " +
            "WHERE deleted = 0 AND product_type IS NOT NULL " +
            "GROUP BY product_type ORDER BY count DESC")
    List<Object> countAssetsByProductType();

    /**
     * 根据当前位置统计资产数量
     */
    @Select("SELECT current_location, COUNT(1) as count FROM assets " +
            "WHERE deleted = 0 AND current_location IS NOT NULL " +
            "GROUP BY current_location ORDER BY count DESC")
    List<Object> countAssetsByLocation();

    /**
     * 检查资产编号是否存在
     */
    @Select("SELECT COUNT(1) FROM assets WHERE asset_code = #{assetCode} AND deleted = 0 AND id != #{excludeId}")
    int countByAssetCodeAndIdNot(@Param("assetCode") String assetCode, @Param("excludeId") Long excludeId);

    /**
     * 检查序列号是否存在
     */
    @Select("SELECT COUNT(1) FROM assets WHERE serial_number = #{serialNumber} AND deleted = 0 AND id != #{excludeId}")
    int countBySerialNumberAndIdNot(@Param("serialNumber") String serialNumber, @Param("excludeId") Long excludeId);

    /**
     * 获取用户的资产总数
     */
    @Select("SELECT COUNT(1) FROM assets WHERE user_id = #{userId} AND deleted = 0")
    long countByUserId(@Param("userId") Long userId);

    /**
     * 获取系统总资产数
     */
    @Select("SELECT COUNT(1) FROM assets WHERE deleted = 0")
    long countAllAssets();

    /**
     * 根据时间范围统计资产变化
     */
    @Select("<script>" +
            "SELECT DATE(created_at) as date, COUNT(1) as count FROM assets " +
            "WHERE deleted = 0 AND created_at BETWEEN #{startTime} AND #{endTime} " +
            "<if test='userId != null'> AND user_id = #{userId} </if>" +
            "GROUP BY DATE(created_at) ORDER BY date" +
            "</script>")
    List<Object> countAssetsByDateRange(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("userId") Long userId
    );

    /**
     * 获取即将过保的资产
     */
    @Select("SELECT * FROM assets " +
            "WHERE deleted = 0 AND warranty_period IS NOT NULL " +
            "AND DATEDIFF(DATE_ADD(purchase_date, INTERVAL warranty_period MONTH), CURDATE()) BETWEEN 0 AND 30 " +
            "AND (#{userId} IS NULL OR user_id = #{userId}) " +
            "ORDER BY purchase_date")
    List<Asset> findAssetsNearWarrantyExpiry(@Param("userId") Long userId);

    /**
     * 获取用户的所有产品类型
     */
    @Select("SELECT DISTINCT product_type FROM assets " +
            "WHERE deleted = 0 AND product_type IS NOT NULL " +
            "AND (#{userId} IS NULL OR user_id = #{userId}) " +
            "ORDER BY product_type")
    List<String> findDistinctProductTypes(@Param("userId") Long userId);

    /**
     * 获取用户的所有位置
     */
    @Select("SELECT DISTINCT current_location FROM assets " +
            "WHERE deleted = 0 AND current_location IS NOT NULL " +
            "AND (#{userId} IS NULL OR user_id = #{userId}) " +
            "ORDER BY current_location")
    List<String> findDistinctLocations(@Param("userId") Long userId);

    /**
     * 高级搜索资产 - 支持多条件查询
     * 注意：这个方法暂时注释掉，使用Service层的QueryWrapper实现
     */
    // 由于MyBatis注解中的动态SQL会导致XML解析错误，暂时移除此方法
    // 高级搜索功能将在Service层使用QueryWrapper实现

    /**
     * 获取所有品牌列表
     */
    @Select("SELECT DISTINCT brand FROM assets " +
            "WHERE deleted = 0 AND brand IS NOT NULL " +
            "AND (#{userId} IS NULL OR user_id = #{userId}) " +
            "ORDER BY brand")
    List<String> findDistinctBrands(@Param("userId") Long userId);

    /**
     * 获取所有供应商列表
     */
    @Select("SELECT DISTINCT supplier FROM assets " +
            "WHERE deleted = 0 AND supplier IS NOT NULL " +
            "AND (#{userId} IS NULL OR user_id = #{userId}) " +
            "ORDER BY supplier")
    List<String> findDistinctSuppliers(@Param("userId") Long userId);

    /**
     * 获取所有安装人员列表
     */
    @Select("SELECT DISTINCT installer FROM assets " +
            "WHERE deleted = 0 AND installer IS NOT NULL " +
            "AND (#{userId} IS NULL OR user_id = #{userId}) " +
            "ORDER BY installer")
    List<String> findDistinctInstallers(@Param("userId") Long userId);

    /**
     * 获取所有接收人列表
     */
    @Select("SELECT DISTINCT receiver FROM assets " +
            "WHERE deleted = 0 AND receiver IS NOT NULL " +
            "AND (#{userId} IS NULL OR user_id = #{userId}) " +
            "ORDER BY receiver")
    List<String> findDistinctReceivers(@Param("userId") Long userId);

    /**
     * 库存盘点 - 按位置统计资产
     */
    @Select("SELECT current_location as location, " +
            "COUNT(1) as totalCount, " +
            "SUM(CASE WHEN status = 'PENDING' THEN 1 ELSE 0 END) as pendingCount, " +
            "SUM(CASE WHEN status = 'RECEIVED' THEN 1 ELSE 0 END) as receivedCount, " +
            "SUM(CASE WHEN status = 'INSTALLED' THEN 1 ELSE 0 END) as installedCount, " +
            "SUM(CASE WHEN status = 'OUTBOUND' THEN 1 ELSE 0 END) as outboundCount, " +
            "SUM(CASE WHEN status = 'SCRAPPED' THEN 1 ELSE 0 END) as scrappedCount, " +
            "COALESCE(SUM(purchase_price), 0) as totalValue " +
            "FROM assets " +
            "WHERE deleted = 0 AND current_location IS NOT NULL " +
            "GROUP BY current_location " +
            "ORDER BY totalCount DESC")
    List<Object> getInventoryStatisticsByLocation();

    /**
     * 库存盘点 - 按产品类型统计资产
     */
    @Select("SELECT product_type as type, " +
            "COUNT(1) as totalCount, " +
            "SUM(CASE WHEN status = 'PENDING' THEN 1 ELSE 0 END) as pendingCount, " +
            "SUM(CASE WHEN status = 'RECEIVED' THEN 1 ELSE 0 END) as receivedCount, " +
            "SUM(CASE WHEN status = 'INSTALLED' THEN 1 ELSE 0 END) as installedCount, " +
            "SUM(CASE WHEN status = 'OUTBOUND' THEN 1 ELSE 0 END) as outboundCount, " +
            "SUM(CASE WHEN status = 'SCRAPPED' THEN 1 ELSE 0 END) as scrappedCount, " +
            "COALESCE(SUM(purchase_price), 0) as totalValue " +
            "FROM assets " +
            "WHERE deleted = 0 AND product_type IS NOT NULL " +
            "GROUP BY product_type " +
            "ORDER BY totalCount DESC")
    List<Object> getInventoryStatisticsByType();

    /**
     * 获取资产总数
     */
    @Select("SELECT COUNT(*) FROM assets WHERE deleted = 0 AND purchase_price IS NOT NULL")
    int getAssetCountWithPrice();

    /**
     * 获取资产总价值
     */
    @Select("SELECT COALESCE(SUM(purchase_price), 0) FROM assets WHERE deleted = 0 AND purchase_price IS NOT NULL")
    double getAssetTotalValue();

    /**
     * 获取资产平均价值
     */
    @Select("SELECT COALESCE(AVG(purchase_price), 0) FROM assets WHERE deleted = 0 AND purchase_price IS NOT NULL")
    double getAssetAvgValue();

    /**
     * 获取资产最小价值
     */
    @Select("SELECT COALESCE(MIN(purchase_price), 0) FROM assets WHERE deleted = 0 AND purchase_price IS NOT NULL")
    double getAssetMinValue();

    /**
     * 获取资产最大价值
     */
    @Select("SELECT COALESCE(MAX(purchase_price), 0) FROM assets WHERE deleted = 0 AND purchase_price IS NOT NULL")
    double getAssetMaxValue();
}