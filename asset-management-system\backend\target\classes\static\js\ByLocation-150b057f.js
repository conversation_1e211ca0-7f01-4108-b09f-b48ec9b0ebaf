import{_ as A}from"./_plugin-vue_export-helper-62491c14.js";/* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                     *//* empty css               *//* empty css               *//* empty css                */import{u as I,r,o as S,c as T,d as M,f as a,g as t,w as e,aq as O,j as C,E as q,aC as P,F as p,R as Q,a8 as v,G as $,b5 as j,W as G,P as W,i as w,ap as k,z as X,aD as H,n as J,aN as K,aQ as U}from"./index-2733c819.js";import"./index-fd3ee58d.js";import{a as Y}from"./inventory-bb5e7097.js";const Z={class:"statistics-by-location"},tt={class:"stat-content"},at={class:"stat-icon"},et={class:"stat-info"},st={class:"stat-value"},lt={class:"stat-content"},ot={class:"stat-icon"},nt={class:"stat-info"},it={class:"stat-value"},dt={class:"stat-content"},rt={class:"stat-icon"},ct={class:"stat-info"},ut={class:"stat-value"},_t={class:"stat-content"},pt={class:"stat-icon"},vt={class:"stat-info"},ft={class:"stat-value"},mt={class:"card-header"},ht={__name:"ByLocation",setup(gt){I();const m=r(),h=r();let u=null,_=null;const x=r(0),f=r(0),g=r(0),E=r(0),i=r([]),y=o=>o?Number(o).toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}):"0.00",B=()=>{if(!m.value||!i.value.length)return;u=k(m.value);const o=i.value.slice(0,10).map(l=>({name:l.location||"未知位置",value:l.totalCount||0})),s={tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"value"},yAxis:{type:"category",data:o.map(l=>l.name)},series:[{name:"资产数量",type:"bar",data:o.map(l=>l.value),itemStyle:{color:"#409EFF"}}]};u.setOption(s)},D=()=>{if(!h.value||!i.value.length)return;_=k(h.value);const o=i.value.slice(0,10).map(l=>({name:l.location||"未知位置",value:l.totalValue||0})),s={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: ¥{c}"},legend:{orient:"vertical",left:"left"},series:[{name:"资产价值",type:"pie",radius:"50%",data:o,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};_.setOption(s)},F=async()=>{try{const s=(await Y()).data;s.locationInventory&&(i.value=s.locationInventory,x.value=i.value.length,f.value=i.value.reduce((l,n)=>l+(n.totalCount||0),0),g.value=i.value.reduce((l,n)=>l+(n.totalValue||0),0),E.value=f.value>0?g.value/f.value:0,await O(),B(),D())}catch(o){console.error("加载数据失败:",o),C.error("加载数据失败")}},R=()=>{C.info("导出功能开发中...")},L=o=>{C.info(`查看 ${o.location} 详情功能开发中...`)};return S(()=>{F(),window.addEventListener("resize",()=>{u==null||u.resize(),_==null||_.resize()})}),(o,s)=>{const l=X,n=q,c=H,z=P,V=J,d=K,N=U;return T(),M("div",Z,[s[9]||(s[9]=a("div",{class:"page-header"},[a("h2",null,"位置统计"),a("p",null,"按位置查看资产分布和统计信息")],-1)),t(z,{gutter:20,class:"stats-cards"},{default:e(()=>[t(c,{span:6},{default:e(()=>[t(n,{class:"stat-card"},{default:e(()=>[a("div",tt,[a("div",at,[t(l,{size:"32",color:"#409EFF"},{default:e(()=>[t(p(Q))]),_:1})]),a("div",et,[a("div",st,v(x.value),1),s[0]||(s[0]=a("div",{class:"stat-label"},"总位置数",-1))])])]),_:1})]),_:1}),t(c,{span:6},{default:e(()=>[t(n,{class:"stat-card"},{default:e(()=>[a("div",lt,[a("div",ot,[t(l,{size:"32",color:"#67C23A"},{default:e(()=>[t(p($))]),_:1})]),a("div",nt,[a("div",it,v(f.value),1),s[1]||(s[1]=a("div",{class:"stat-label"},"总资产数",-1))])])]),_:1})]),_:1}),t(c,{span:6},{default:e(()=>[t(n,{class:"stat-card"},{default:e(()=>[a("div",dt,[a("div",rt,[t(l,{size:"32",color:"#E6A23C"},{default:e(()=>[t(p(j))]),_:1})]),a("div",ct,[a("div",ut,"¥"+v(y(g.value)),1),s[2]||(s[2]=a("div",{class:"stat-label"},"总价值",-1))])])]),_:1})]),_:1}),t(c,{span:6},{default:e(()=>[t(n,{class:"stat-card"},{default:e(()=>[a("div",_t,[a("div",pt,[t(l,{size:"32",color:"#F56C6C"},{default:e(()=>[t(p(G))]),_:1})]),a("div",vt,[a("div",ft,"¥"+v(y(E.value)),1),s[3]||(s[3]=a("div",{class:"stat-label"},"平均价值",-1))])])]),_:1})]),_:1})]),_:1}),t(z,{gutter:20,class:"charts-section"},{default:e(()=>[t(c,{span:12},{default:e(()=>[t(n,null,{header:e(()=>s[4]||(s[4]=[a("div",{class:"card-header"},[a("span",null,"位置资产数量分布")],-1)])),default:e(()=>[a("div",{ref_key:"quantityChartRef",ref:m,style:{height:"400px"}},null,512)]),_:1})]),_:1}),t(c,{span:12},{default:e(()=>[t(n,null,{header:e(()=>s[5]||(s[5]=[a("div",{class:"card-header"},[a("span",null,"位置资产价值分布")],-1)])),default:e(()=>[a("div",{ref_key:"valueChartRef",ref:h,style:{height:"400px"}},null,512)]),_:1})]),_:1})]),_:1}),t(n,{class:"table-section"},{header:e(()=>[a("div",mt,[s[7]||(s[7]=a("span",null,"位置详细统计",-1)),t(V,{type:"primary",onClick:R},{default:e(()=>[t(l,null,{default:e(()=>[t(p(W))]),_:1}),s[6]||(s[6]=w(" 导出数据 ",-1))]),_:1,__:[6]})])]),default:e(()=>[t(N,{data:i.value,stripe:"",style:{width:"100%"}},{default:e(()=>[t(d,{prop:"location",label:"位置",width:"200"}),t(d,{prop:"totalCount",label:"资产总数",width:"120",align:"center"}),t(d,{prop:"pendingCount",label:"待处理",width:"100",align:"center"}),t(d,{prop:"receivedCount",label:"已入库",width:"100",align:"center"}),t(d,{prop:"installedCount",label:"已安装",width:"100",align:"center"}),t(d,{prop:"outboundCount",label:"已出库",width:"100",align:"center"}),t(d,{prop:"scrappedCount",label:"已报废",width:"100",align:"center"}),t(d,{prop:"totalValue",label:"总价值",width:"150",align:"right"},{default:e(({row:b})=>[w(" ¥"+v(y(b.totalValue)),1)]),_:1}),t(d,{label:"操作",width:"120",align:"center"},{default:e(({row:b})=>[t(V,{type:"text",size:"small",onClick:yt=>L(b)},{default:e(()=>s[8]||(s[8]=[w(" 查看详情 ",-1)])),_:2,__:[8]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})])}}},Rt=A(ht,[["__scopeId","data-v-7d75c2b2"]]);export{Rt as default};
