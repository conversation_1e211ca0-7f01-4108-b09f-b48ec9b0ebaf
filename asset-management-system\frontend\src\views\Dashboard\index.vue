<template>
  <div class="dashboard">
    <!-- 欢迎卡片 -->
    <div class="welcome-card animate__animated animate__fadeInDown">
      <el-card class="welcome-content" shadow="hover">
        <div class="welcome-info">
          <div class="welcome-text">
            <h2>欢迎回来，{{ authStore.userName }}！</h2>
            <p>今天是 {{ currentDate }}，祝您工作愉快！</p>
          </div>
          <div class="welcome-avatar">
            <el-avatar :size="80" class="user-avatar">
              <el-icon size="40"><User /></el-icon>
            </el-avatar>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid animate__animated animate__fadeInUp animate__delay-1s">
      <el-row :gutter="24">
        <el-col :xs="12" :sm="6" v-for="(stat, index) in statsData" :key="index">
          <el-card
            class="stat-card clickable"
            shadow="hover"
            @click="handleStatCardClick(stat.route)"
          >
            <div class="stat-content">
              <div class="stat-icon" :style="{ background: stat.color }">
                <el-icon size="24">
                  <component :is="stat.icon" />
                </el-icon>
              </div>
              <div class="stat-info">
                <h3 class="stat-value">{{ stat.value }}</h3>
                <p class="stat-label">{{ stat.label }}</p>
              </div>
            </div>
            <div class="stat-trend" :class="stat.trend > 0 ? 'positive' : 'negative'">
              <el-icon>
                <TrendCharts v-if="stat.trend > 0" />
                <TrendCharts v-else />
              </el-icon>
              <span>{{ Math.abs(stat.trend) }}%</span>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section animate__animated animate__fadeInUp animate__delay-2s">
      <el-row :gutter="24">
        <!-- 资产状态分布图 -->
        <el-col :xs="24" :lg="12">
          <el-card title="资产状态分布" shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>资产状态分布</span>
                <el-button link @click="refreshChart">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </div>
            </template>
            <div class="chart-container">
              <v-chart :option="pieChartOption" class="chart" />
            </div>
          </el-card>
        </el-col>

        <!-- 月度资产变化趋势 -->
        <el-col :xs="24" :lg="12">
          <el-card title="月度变化趋势" shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>月度变化趋势</span>
                <el-button link @click="refreshChart">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </div>
            </template>
            <div class="chart-container">
              <v-chart :option="lineChartOption" class="chart" />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 快速操作和最近活动 -->
    <div class="bottom-section animate__animated animate__fadeInUp animate__delay-3s">
      <el-row :gutter="24">
        <!-- 快速操作 -->
        <el-col :xs="24" :lg="8">
          <el-card title="快速操作" shadow="hover" class="quick-actions-card">
            <div class="quick-actions">
              <el-button 
                v-for="action in quickActions" 
                :key="action.name"
                :type="action.type"
                :icon="action.icon"
                @click="handleQuickAction(action.action)"
                class="action-btn"
                size="large"
              >
                {{ action.name }}
              </el-button>
            </div>
          </el-card>
        </el-col>

        <!-- 最近活动 -->
        <el-col :xs="24" :lg="16">
          <el-card title="最近活动" shadow="hover" class="activity-card">
            <template #header>
              <div class="card-header">
                <span>最近活动</span>
                <el-link type="primary" @click="viewAllActivities">查看全部</el-link>
              </div>
            </template>
            <div class="activity-list">
              <div 
                v-for="activity in recentActivities" 
                :key="activity.id"
                class="activity-item"
              >
                <div class="activity-icon" :class="activity.type">
                  <el-icon>
                    <component :is="activity.icon" />
                  </el-icon>
                </div>
                <div class="activity-content">
                  <p class="activity-text">{{ activity.description }}</p>
                  <span class="activity-time">{{ activity.time }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { PieChart, LineChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'

// 确保ECharts组件已注册
use([
  CanvasRenderer,
  PieChart,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])
import dayjs from 'dayjs'
import { TrendCharts, Box, User, Wallet, DataAnalysis } from '@element-plus/icons-vue'

const router = useRouter()
const authStore = useAuthStore()

// 当前日期
const currentDate = computed(() => dayjs().format('YYYY年MM月DD日'))

// 统计数据
const statsData = ref([
  {
    label: '总资产',
    value: '0',
    icon: 'Box',
    color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    trend: 0,
    route: '/assets'
  },
  {
    label: '已入库',
    value: '0',
    icon: 'Checked',
    color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    trend: 0,
    route: '/operations/inbound'
  },
  {
    label: '已安装',
    value: '0',
    icon: 'Tools',
    color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    trend: 0,
    route: '/operations/install'
  },
  {
    label: '已出库',
    value: '0',
    icon: 'Upload',
    color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    trend: 0,
    route: '/operations/outbound'
  }
])

// 快速操作
const quickActions = ref([
  { name: '新增资产', type: 'primary', icon: 'Plus', action: 'addAsset' },
  { name: '批量导入', type: 'success', icon: 'Upload', action: 'importAssets' },
  { name: '导出数据', type: 'warning', icon: 'Download', action: 'exportData' },
  { name: '生成报表', type: 'info', icon: 'Document', action: 'generateReport' }
])

// 最近活动
const recentActivities = ref([])

// 饼图配置
const pieChartOption = ref({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '资产状态',
      type: 'pie',
      radius: ['50%', '70%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '18',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: []
    }
  ]
})

// 线图配置
const lineChartOption = ref({
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['入库', '出库', '安装']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: ['1月', '2月', '3月', '4月', '5月', '6月']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '入库',
      type: 'line',
      smooth: true,
      data: [12, 18, 25, 32, 28, 35]
    },
    {
      name: '出库', 
      type: 'line',
      smooth: true,
      data: [8, 15, 20, 28, 22, 30]
    },
    {
      name: '安装',
      type: 'line', 
      smooth: true,
      data: [5, 12, 18, 25, 20, 28]
    }
  ]
})

// 统计卡片点击处理
const handleStatCardClick = (route) => {
  if (route) {
    router.push(route)
  }
}

// 快速操作处理
const handleQuickAction = (action) => {
  switch (action) {
    case 'addAsset':
      router.push('/assets/create')
      break
    case 'importAssets':
      router.push('/import-export')
      break
    case 'exportData':
      ElMessage.info('导出功能开发中...')
      break
    case 'generateReport':
      router.push('/statistics')
      break
  }
}

// 刷新图表
const refreshChart = () => {
  ElMessage.success('图表已刷新')
  // 这里可以重新获取数据
}

// 查看全部活动
const viewAllActivities = () => {
  ElMessage.info('活动日志功能开发中...')
}

onMounted(() => {
  // 初始化数据
  loadDashboardData()
})

const loadDashboardData = async () => {
  try {
    // 这里调用API获取仪表板数据
    ElMessage.success('仪表板数据加载完成')
  } catch (error) {
    ElMessage.error('数据加载失败')
  }
}
</script>

<style scoped lang="scss">
.dashboard {
  padding: 0;
}

.welcome-card {
  margin-bottom: 24px;
}

.welcome-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
}

.welcome-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
}

.welcome-text h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.welcome-text p {
  margin: 0;
  opacity: 0.9;
  font-size: 16px;
}

.user-avatar {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.stats-grid {
  margin-bottom: 24px;
}

.stat-card {
  height: 120px;
  border: none;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
  }

  &.clickable {
    cursor: pointer;

    &:hover {
      transform: translateY(-6px);
      box-shadow: 0 16px 32px rgba(0, 0, 0, 0.2);
    }

    &:active {
      transform: translateY(-2px);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    }
  }
}

.stat-content {
  display: flex;
  align-items: center;
  height: 80px;
}

.stat-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: 16px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 700;
  color: #333;
}

.stat-label {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;

  &.positive {
    color: #67c23a;
  }

  &.negative {
    color: #f56c6c;
  }
}

.charts-section {
  margin-bottom: 24px;
}

.chart-card {
  border: none;
  border-radius: 12px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
}

.chart {
  width: 100%;
  height: 100%;
}

.bottom-section {
  margin-bottom: 24px;
}

.quick-actions-card {
  border: none;
  border-radius: 12px;
}

.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  align-items: stretch;
  justify-items: center;
  width: 100%;
}

.action-btn {
  width: 100%;
  max-width: 200px;
  height: 56px;
  border-radius: 8px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  margin: 0 auto;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

.activity-card {
  border: none;
  border-radius: 12px;
}

.activity-list {
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: white;

  &.create {
    background: #67c23a;
  }

  &.update {
    background: #409eff;
  }

  &.receive {
    background: #e6a23c;
  }

  &.install {
    background: #909399;
  }
}

.activity-content {
  flex: 1;
}

.activity-text {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #333;
}

.activity-time {
  font-size: 12px;
  color: #999;
}

// Element Plus 样式覆盖
:deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-card__body) {
  padding: 20px;
}

// 响应式设计
@media (max-width: 768px) {
  .welcome-info {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .stat-content {
    height: auto;
    padding: 16px 0;
  }

  .quick-actions {
    grid-template-columns: 1fr;
    gap: 8px;

    .action-btn {
      height: 48px;
    }
  }
}

@media (max-width: 480px) {
  .charts-section {
    .el-col {
      margin-bottom: 16px;
    }
  }

  .chart-container {
    height: 250px;
  }
}
</style>