# 测试配置 - 不连接数据库
server:
  port: 8080

spring:
  application:
    name: asset-management-system
  
  # 使用内存数据库进行测试
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb
    username: sa
    password: 
  
  h2:
    console:
      enabled: true
      path: /h2-console
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect

# JWT配置
jwt:
  secret: assetManagementSystemSecretKey2024
  expiration: 86400000

# 日志配置
logging:
  level:
    root: INFO
    com.asset: DEBUG
  pattern:
    console: "%d{HH:mm:ss} %-5level %logger{20} - %msg%n"
