# 资产管理系统

一个基于 Spring Boot + Vue.js + MySQL 的企业资产管理系统，支持资产全生命周期管理，包括资产信息管理、出入库操作、位置跟踪和历史记录查询。

## 🎯 项目状态

**当前版本**：v1.0.0-clean
**状态**：已完成模拟数据清理，准备真实API集成
**最后更新**：2024年1月

### 📋 清理完成项目
- ✅ 移除所有模拟数据和测试数据
- ✅ 清理无用文件和代码
- ✅ 优化项目结构和代码质量
- ✅ 添加详细的API集成指南
- ✅ 完善响应式设计（支持1920×1080和2K分辨率）

### 📖 重要文档
- 📄 [API集成指南](../API_INTEGRATION_GUIDE.md) - 真实API集成步骤
- 📄 [项目清理报告](../PROJECT_CLEANUP_REPORT.md) - 详细清理记录

## 📋 功能特性

### 🔐 用户认证与权限管理
- JWT Token 认证
- 基于角色的访问控制（RBAC）
- 用户会话管理
- 数据权限隔离

### 📦 资产基础信息管理
- 资产信息的增删改查
- 灵活的字段配置
- 资产编号自动生成
- 批量导入导出（Excel）

### 🔄 资产流转管理
- **入库管理**：记录签收人、入库时间
- **安装管理**：记录安装位置、安装人员
- **位置变更**：跟踪资产位置变化
- **出库管理**：记录出库原因、出库人员

### 📊 统计分析
- 资产状态分布统计
- 按类型/位置统计
- 库存概览仪表板
- 操作历史分析

### 💾 数据管理
- Excel 批量导入资产
- 多格式数据导出
- 完整的操作历史记录
- 数据备份与恢复

## 🏗️ 技术架构

### 后端技术栈
- **框架**：Spring Boot 2.7+
- **安全**：Spring Security + JWT
- **数据访问**：Spring Data JPA + MyBatis Plus
- **数据库**：MySQL 8.0
- **文档处理**：Apache POI
- **构建工具**：Maven

### 前端技术栈
- **框架**：Vue.js 3
- **状态管理**：Pinia
- **路由**：Vue Router 4
- **UI 组件**：Element Plus
- **HTTP 客户端**：Axios
- **图表**：ECharts
- **构建工具**：Vite

## 🚀 快速开始

### 环境要求
- JDK 11+
- Node.js 16+
- MySQL 8.0+
- Maven 3.6+

### 后端启动

1. **克隆项目**
```bash
git clone https://github.com/your-repo/asset-management-system.git
cd asset-management-system
```

2. **配置数据库**
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE asset_management DEFAULT CHARACTER SET utf8mb4 DEFAULT COLLATE utf8mb4_unicode_ci;

# 导入数据表结构
mysql -u root -p asset_management < database/schema.sql
```

3. **修改配置文件**
```yaml
# backend/src/main/resources/application.yml
spring:
  datasource:
    url: ********************************************
    username: your_username
    password: your_password
```

4. **启动后端服务**
```bash
cd backend
mvn clean install
mvn spring-boot:run
```

后端服务将在 `http://localhost:8080` 启动

### 前端启动

1. **安装依赖**
```bash
cd frontend
npm install
```

2. **启动开发服务器**
```bash
npm run dev
```

前端服务将在 `http://localhost:3000` 启动

### 默认账号
- **管理员账号**：admin / admin123
- **普通用户**：user / user123

## 📁 项目结构

```
asset-management-system/
├── backend/                    # 后端 Spring Boot 项目
│   ├── src/main/java/com/asset/
│   │   ├── controller/         # 控制器层
│   │   ├── service/           # 服务层
│   │   ├── repository/        # 数据访问层
│   │   ├── entity/            # 实体类
│   │   ├── dto/               # 数据传输对象
│   │   ├── config/            # 配置类
│   │   ├── utils/             # 工具类
│   │   └── exception/         # 异常处理
│   └── src/main/resources/
│       ├── application.yml    # 应用配置
│       └── static/           # 静态资源
├── frontend/                  # 前端 Vue.js 项目
│   ├── src/
│   │   ├── components/       # 通用组件
│   │   ├── views/            # 页面组件
│   │   ├── stores/           # 状态管理
│   │   ├── router/           # 路由配置
│   │   ├── api/              # API 接口
│   │   ├── utils/            # 工具函数
│   │   └── styles/           # 样式文件
│   ├── public/               # 公共资源
│   └── package.json          # 项目依赖
├── database/                 # 数据库相关
│   └── schema.sql           # 数据库表结构
└── docs/                    # 项目文档
```

## 🎨 界面预览

### 登录页面
- 炫酷的渐变背景动画
- 响应式设计
- 表单验证

### 仪表板
- 资产统计卡片
- 实时图表展示
- 快速操作入口
- 最近活动记录

### 资产管理
- 资产列表展示
- 高级搜索功能
- 资产详情查看
- 批量操作支持

## 🔧 开发指南

### API 接口文档
启动后端服务后，访问 Swagger 文档：
```
http://localhost:8080/swagger-ui.html
```

### 数据库设计
主要表结构：
- `users` - 用户表
- `assets` - 资产表
- `asset_history` - 资产历史记录表
- `location_history` - 位置历史表
- `asset_notes` - 资产备注表

### 权限设计
- **USER**：普通用户，只能查看和管理自己的资产
- **ADMIN**：管理员，可以查看所有用户的资产信息

## 🧪 测试

### 后端测试
```bash
cd backend
mvn test
```

### 前端测试
```bash
cd frontend
npm run test
```

## 📦 部署

### 生产环境部署

1. **后端打包**
```bash
cd backend
mvn clean package -Pprod
```

2. **前端打包**
```bash
cd frontend
npm run build
```

3. **Docker 部署**
```bash
docker-compose up -d
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目基于 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目链接：[https://github.com/your-repo/asset-management-system](https://github.com/your-repo/asset-management-system)
- 问题反馈：[Issues](https://github.com/your-repo/asset-management-system/issues)

## 🙏 致谢

感谢以下开源项目：
- [Spring Boot](https://spring.io/projects/spring-boot)
- [Vue.js](https://vuejs.org/)
- [Element Plus](https://element-plus.org/)
- [MyBatis Plus](https://baomidou.com/)
- [ECharts](https://echarts.apache.org/)