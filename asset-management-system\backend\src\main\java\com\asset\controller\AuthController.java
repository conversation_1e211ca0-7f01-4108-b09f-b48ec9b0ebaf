package com.asset.controller;

import com.asset.dto.ApiResponse;
import com.asset.dto.LoginRequest;
import com.asset.dto.LoginResponse;
import com.asset.service.AuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@Slf4j
@RestController
@RequestMapping("/auth")
@Validated
public class AuthController extends BaseController {

    @Autowired
    private AuthService authService;

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public ApiResponse<LoginResponse> login(@Valid @RequestBody LoginRequest loginRequest) {
        try {
            LoginResponse loginResponse = authService.login(loginRequest);
            return ApiResponse.success("登录成功", loginResponse);
        } catch (Exception e) {
            log.error("登录失败: {}", e.getMessage());
            return ApiResponse.error(401, e.getMessage());
        }
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/profile")
    public ApiResponse<LoginResponse.UserInfo> getCurrentUser() {
        try {
            LoginResponse.UserInfo userInfo = authService.getCurrentUser();
            return ApiResponse.success(userInfo);
        } catch (Exception e) {
            log.error("获取用户信息失败: {}", e.getMessage());
            return ApiResponse.error(401, "获取用户信息失败");
        }
    }

    /**
     * 刷新Token
     */
    @PostMapping("/refresh")
    public ApiResponse<LoginResponse> refreshToken(HttpServletRequest request) {
        try {
            String authHeader = request.getHeader("Authorization");
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                return ApiResponse.error(401, "缺少有效的Token");
            }
            
            String token = authHeader.substring(7);
            LoginResponse loginResponse = authService.refreshToken(token);
            return ApiResponse.success("Token刷新成功", loginResponse);
        } catch (Exception e) {
            log.error("Token刷新失败: {}", e.getMessage());
            return ApiResponse.error(401, e.getMessage());
        }
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public ApiResponse<Void> logout() {
        try {
            authService.logout();
            return ApiResponse.success("退出登录成功");
        } catch (Exception e) {
            log.error("退出登录失败: {}", e.getMessage());
            return ApiResponse.error("退出登录失败");
        }
    }

    /**
     * 验证Token有效性
     */
    @PostMapping("/validate")
    public ApiResponse<Boolean> validateToken(HttpServletRequest request) {
        try {
            String authHeader = request.getHeader("Authorization");
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                return ApiResponse.success("Token验证", false);
            }
            
            String token = authHeader.substring(7);
            boolean isValid = authService.validateToken(token);
            return ApiResponse.success("Token验证", isValid);
        } catch (Exception e) {
            log.error("Token验证失败: {}", e.getMessage());
            return ApiResponse.success("Token验证", false);
        }
    }
}