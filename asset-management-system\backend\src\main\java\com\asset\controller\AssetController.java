package com.asset.controller;

import com.asset.dto.ApiResponse;
import com.asset.dto.AssetRequest;
import com.asset.dto.AssetResponse;
import com.asset.dto.AssetSearchRequest;
import com.asset.dto.PageResponse;
import com.asset.entity.Asset;
import com.asset.service.AssetService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/assets")
@Validated
public class AssetController {

    @Autowired
    private AssetService assetService;

    /**
     * 分页查询资产列表
     */
    @GetMapping
    public ApiResponse<PageResponse<AssetResponse>> getAssetList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Asset.AssetStatus status,
            @RequestParam(required = false) String productType) {
        
        try {
            PageResponse<AssetResponse> result = assetService.getAssetList(page, size, keyword, status, productType);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("查询资产列表失败: {}", e.getMessage());
            return ApiResponse.error("查询资产列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取资产详情
     */
    @GetMapping("/{id}")
    public ApiResponse<AssetResponse> getAssetById(@PathVariable Long id) {
        try {
            AssetResponse asset = assetService.getAssetById(id);
            return ApiResponse.success(asset);
        } catch (Exception e) {
            log.error("获取资产详情失败: {}", e.getMessage());
            return ApiResponse.error("获取资产详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建资产
     */
    @PostMapping
    public ApiResponse<AssetResponse> createAsset(@Valid @RequestBody AssetRequest request) {
        try {
            AssetResponse asset = assetService.createAsset(request);
            return ApiResponse.success("资产创建成功", asset);
        } catch (Exception e) {
            log.error("创建资产失败: {}", e.getMessage());
            return ApiResponse.error("创建资产失败: " + e.getMessage());
        }
    }

    /**
     * 更新资产
     */
    @PutMapping("/{id}")
    public ApiResponse<AssetResponse> updateAsset(@PathVariable Long id, 
                                                 @Valid @RequestBody AssetRequest request) {
        try {
            AssetResponse asset = assetService.updateAsset(id, request);
            return ApiResponse.success("资产更新成功", asset);
        } catch (Exception e) {
            log.error("更新资产失败: {}", e.getMessage());
            return ApiResponse.error("更新资产失败: " + e.getMessage());
        }
    }

    /**
     * 删除资产
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteAsset(@PathVariable Long id) {
        try {
            assetService.deleteAsset(id);
            return ApiResponse.success("资产删除成功");
        } catch (Exception e) {
            log.error("删除资产失败: {}", e.getMessage());
            return ApiResponse.error("删除资产失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除资产
     */
    @DeleteMapping("/batch")
    public ApiResponse<Void> batchDeleteAssets(@RequestBody List<Long> ids) {
        try {
            assetService.batchDeleteAssets(ids);
            return ApiResponse.success("批量删除成功");
        } catch (Exception e) {
            log.error("批量删除资产失败: {}", e.getMessage());
            return ApiResponse.error("批量删除资产失败: " + e.getMessage());
        }
    }

    /**
     * 获取资产统计信息
     */
    @GetMapping("/statistics")
    public ApiResponse<Object> getAssetStatistics() {
        try {
            Object statistics = assetService.getAssetStatistics();
            return ApiResponse.success(statistics);
        } catch (Exception e) {
            log.error("获取资产统计失败: {}", e.getMessage());
            return ApiResponse.error("获取资产统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取产品类型列表
     */
    @GetMapping("/product-types")
    public ApiResponse<List<String>> getProductTypes() {
        try {
            List<String> productTypes = assetService.getProductTypes();
            return ApiResponse.success(productTypes);
        } catch (Exception e) {
            log.error("获取产品类型列表失败: {}", e.getMessage());
            return ApiResponse.error("获取产品类型列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取位置列表
     */
    @GetMapping("/locations")
    public ApiResponse<List<String>> getLocations() {
        try {
            List<String> locations = assetService.getLocations();
            return ApiResponse.success(locations);
        } catch (Exception e) {
            log.error("获取位置列表失败: {}", e.getMessage());
            return ApiResponse.error("获取位置列表失败: " + e.getMessage());
        }
    }

    /**
     * 高级搜索资产
     */
    @PostMapping("/search")
    public ApiResponse<PageResponse<AssetResponse>> advancedSearchAssets(@RequestBody AssetSearchRequest request) {
        try {
            PageResponse<AssetResponse> result = assetService.advancedSearchAssets(request);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("高级搜索资产失败: {}", e.getMessage());
            return ApiResponse.error("高级搜索资产失败: " + e.getMessage());
        }
    }

    /**
     * 获取搜索选项数据
     */
    @GetMapping("/search-options")
    public ApiResponse<Map<String, Object>> getSearchOptions() {
        try {
            Map<String, Object> options = assetService.getSearchOptions();
            return ApiResponse.success(options);
        } catch (Exception e) {
            log.error("获取搜索选项失败: {}", e.getMessage());
            return ApiResponse.error("获取搜索选项失败: " + e.getMessage());
        }
    }

    /**
     * 获取库存统计详情
     */
    @GetMapping("/inventory-statistics")
    public ApiResponse<Map<String, Object>> getInventoryStatistics() {
        try {
            Map<String, Object> statistics = assetService.getInventoryStatistics();
            return ApiResponse.success(statistics);
        } catch (Exception e) {
            log.error("获取库存统计失败: {}", e.getMessage());
            return ApiResponse.error("获取库存统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取即将过保的资产
     */
    @GetMapping("/warranty-expiry")
    public ApiResponse<List<AssetResponse>> getAssetsNearWarrantyExpiry() {
        try {
            List<AssetResponse> assets = assetService.getAssetsNearWarrantyExpiry();
            return ApiResponse.success(assets);
        } catch (Exception e) {
            log.error("获取即将过保资产失败: {}", e.getMessage());
            return ApiResponse.error("获取即将过保资产失败: " + e.getMessage());
        }
    }

    /**
     * 根据时间范围统计资产变化趋势
     */
    @GetMapping("/trends")
    public ApiResponse<List<Object>> getAssetTrends(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            List<Object> trends = assetService.getAssetTrendsByDateRange(startTime, endTime);
            return ApiResponse.success(trends);
        } catch (Exception e) {
            log.error("获取资产趋势失败: {}", e.getMessage());
            return ApiResponse.error("获取资产趋势失败: " + e.getMessage());
        }
    }
}