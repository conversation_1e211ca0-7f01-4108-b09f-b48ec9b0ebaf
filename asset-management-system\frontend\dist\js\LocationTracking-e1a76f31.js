import{_ as te}from"./_plugin-vue_export-helper-62491c14.js";/* empty css                   *//* empty css                   *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                *//* empty css                      *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                       */import{aT as A,u as le,r as u,b as E,o as ne,c as g,d as R,g as o,w as l,j as C,E as se,aV as re,f as v,F as U,R as ie,i as p,M as de,H as ue,X as x,a8 as M,a2 as pe,a3 as me,aB as ce,z as _e,n as ge,k as fe,l as ve,aU as ye,p as he,aN as we,aO as Ce,aQ as ke,aR as be,aM as Le,aS as Ve,aL as Te}from"./index-2733c819.js";import{g as ze}from"./asset-f5b5b286.js";const Ee=(y,k)=>A({url:`/location-tracking/assets/${y}/change-location`,method:"post",data:k}),Re=y=>A({url:"/location-tracking/history",method:"get",params:y});const xe={class:"location-tracking"},Ie={class:"card-header"},De={class:"search-section"},Fe={key:0},Ue={class:"pagination-wrapper"},Me={class:"dialog-footer"},Ae={__name:"LocationTracking",setup(y){const k=le(),b=u(!1),L=u(!1),f=u(!1),I=u([]),V=u([]),h=u(""),T=u([]),z=u(),s=E({keyword:"",startTime:null,endTime:null}),r=E({page:1,size:20,total:0}),n=E({assetId:null,toLocation:"",moveReason:""}),S={assetId:[{required:!0,message:"请选择资产",trigger:"change"}],toLocation:[{required:!0,message:"请输入新位置",trigger:"blur"}]},B=a=>a?ce(a).format("YYYY-MM-DD HH:mm:ss"):"",c=async()=>{b.value=!0;try{const a={page:r.page,size:r.size,keyword:s.keyword,startTime:s.startTime,endTime:s.endTime},{data:e}=await Re(a);I.value=e.records,r.total=e.total}catch(a){console.error("加载位置历史失败:",a),C.error("加载位置历史失败")}finally{b.value=!1}},H=async()=>{try{const{data:a}=await ze({size:1e3});V.value=a.records}catch(a){console.error("加载资产列表失败:",a)}},$=a=>{a&&a.length===2?(s.startTime=a[0],s.endTime=a[1]):(s.startTime=null,s.endTime=null)},N=()=>{s.keyword="",s.startTime=null,s.endTime=null,T.value=[],r.page=1,c()},O=a=>{r.page=a,c()},P=a=>{r.size=a,r.page=1,c()},Y=()=>{f.value=!0,H()},j=a=>{const e=V.value.find(i=>i.id===a);h.value=e?e.currentLocation||"暂无位置信息":""},D=()=>{var a;n.assetId=null,n.toLocation="",n.moveReason="",h.value="",(a=z.value)==null||a.resetFields()},q=async()=>{var a,e;try{await z.value.validate(),L.value=!0,await Ee(n.assetId,{toLocation:n.toLocation,moveReason:n.moveReason}),C.success("位置变更成功"),f.value=!1,D(),c()}catch(i){console.error("位置变更失败:",i),(e=(a=i.response)==null?void 0:a.data)!=null&&e.message?C.error(i.response.data.message):C.error("位置变更失败")}finally{L.value=!1}},Q=a=>{k.push(`/assets/${a}`)};return ne(()=>{c()}),(a,e)=>{const i=_e,_=ge,w=fe,m=ve,X=ye,F=he,d=we,G=Ce,J=ke,K=be,W=se,Z=Te,ee=Le,oe=re,ae=Ve;return g(),R("div",xe,[o(W,null,{header:l(()=>[v("div",Ie,[e[11]||(e[11]=v("span",null,"位置跟踪管理",-1)),o(_,{type:"primary",onClick:Y},{default:l(()=>[o(i,null,{default:l(()=>[o(U(ie))]),_:1}),e[10]||(e[10]=p(" 变更位置 ",-1))]),_:1,__:[10]})])]),default:l(()=>[v("div",De,[o(F,{inline:!0},{default:l(()=>[o(m,{label:"关键词"},{default:l(()=>[o(w,{modelValue:s.keyword,"onUpdate:modelValue":e[0]||(e[0]=t=>s.keyword=t),placeholder:"资产编号、型号、位置",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),o(m,{label:"时间范围"},{default:l(()=>[o(X,{modelValue:T.value,"onUpdate:modelValue":e[1]||(e[1]=t=>T.value=t),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",onChange:$},null,8,["modelValue"])]),_:1}),o(m,null,{default:l(()=>[o(_,{type:"primary",onClick:c},{default:l(()=>[o(i,null,{default:l(()=>[o(U(de))]),_:1}),e[12]||(e[12]=p(" 查询 ",-1))]),_:1,__:[12]}),o(_,{onClick:N},{default:l(()=>e[13]||(e[13]=[p("重置",-1)])),_:1,__:[13]})]),_:1})]),_:1})]),ue((g(),x(J,{data:I.value,stripe:""},{default:l(()=>[o(d,{prop:"assetCode",label:"资产编号",width:"120"}),o(d,{prop:"productModel",label:"产品型号",width:"150"}),o(d,{prop:"fromLocation",label:"原位置",width:"150"},{default:l(({row:t})=>[t.fromLocation?(g(),R("span",Fe,M(t.fromLocation),1)):(g(),x(G,{key:1,type:"info",size:"small"},{default:l(()=>e[14]||(e[14]=[p("无",-1)])),_:1,__:[14]}))]),_:1}),o(d,{prop:"toLocation",label:"新位置",width:"150"}),o(d,{prop:"moveReason",label:"变更原因","min-width":"200","show-overflow-tooltip":""}),o(d,{prop:"operator",label:"操作人",width:"100"}),o(d,{prop:"movedAt",label:"变更时间",width:"180"},{default:l(({row:t})=>[p(M(B(t.movedAt)),1)]),_:1}),o(d,{label:"操作",width:"120",fixed:"right"},{default:l(({row:t})=>[o(_,{type:"primary",size:"small",onClick:Se=>Q(t.assetId)},{default:l(()=>e[15]||(e[15]=[p(" 查看资产 ",-1)])),_:2,__:[15]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ae,b.value]]),v("div",Ue,[o(K,{"current-page":r.page,"onUpdate:currentPage":e[2]||(e[2]=t=>r.page=t),"page-size":r.size,"onUpdate:pageSize":e[3]||(e[3]=t=>r.size=t),total:r.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:P,onCurrentChange:O},null,8,["current-page","page-size","total"])])]),_:1}),o(oe,{modelValue:f.value,"onUpdate:modelValue":e[9]||(e[9]=t=>f.value=t),title:"变更资产位置",width:"500px",onClose:D},{footer:l(()=>[v("span",Me,[o(_,{onClick:e[8]||(e[8]=t=>f.value=!1)},{default:l(()=>e[16]||(e[16]=[p("取消",-1)])),_:1,__:[16]}),o(_,{type:"primary",onClick:q,loading:L.value},{default:l(()=>e[17]||(e[17]=[p(" 确定变更 ",-1)])),_:1,__:[17]},8,["loading"])])]),default:l(()=>[o(F,{model:n,rules:S,ref_key:"locationChangeFormRef",ref:z,"label-width":"100px"},{default:l(()=>[o(m,{label:"选择资产",prop:"assetId"},{default:l(()=>[o(ee,{modelValue:n.assetId,"onUpdate:modelValue":e[4]||(e[4]=t=>n.assetId=t),placeholder:"请选择资产",filterable:"",onChange:j,style:{width:"100%"}},{default:l(()=>[(g(!0),R(pe,null,me(V.value,t=>(g(),x(Z,{key:t.id,label:`${t.assetCode} - ${t.productModel}`,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(m,{label:"当前位置"},{default:l(()=>[o(w,{modelValue:h.value,"onUpdate:modelValue":e[5]||(e[5]=t=>h.value=t),readonly:"",placeholder:"暂无位置信息"},null,8,["modelValue"])]),_:1}),o(m,{label:"新位置",prop:"toLocation"},{default:l(()=>[o(w,{modelValue:n.toLocation,"onUpdate:modelValue":e[6]||(e[6]=t=>n.toLocation=t),placeholder:"请输入新位置"},null,8,["modelValue"])]),_:1}),o(m,{label:"变更原因"},{default:l(()=>[o(w,{modelValue:n.moveReason,"onUpdate:modelValue":e[7]||(e[7]=t=>n.moveReason=t),type:"textarea",rows:3,placeholder:"请输入变更原因（可选）"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},Ze=te(Ae,[["__scopeId","data-v-a885a128"]]);export{Ze as default};
