package com.asset.repository;

import com.asset.entity.AssetHistory;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface AssetHistoryRepository extends BaseMapper<AssetHistory> {

    /**
     * 分页查询资产历史记录
     */
    @Select("SELECT h.*, a.asset_code, a.product_model FROM asset_history h " +
            "LEFT JOIN assets a ON h.asset_id = a.id " +
            "WHERE h.asset_id = #{assetId} " +
            "ORDER BY h.created_at DESC")
    IPage<AssetHistory> findByAssetIdWithPagination(Page<AssetHistory> page, @Param("assetId") Long assetId);

    /**
     * 查询资产的所有历史记录
     */
    @Select("SELECT * FROM asset_history WHERE asset_id = #{assetId} ORDER BY created_at DESC")
    List<AssetHistory> findByAssetId(@Param("assetId") Long assetId);

    /**
     * 根据操作类型查询历史记录
     */
    @Select("SELECT h.*, a.asset_code, a.product_model FROM asset_history h " +
            "LEFT JOIN assets a ON h.asset_id = a.id " +
            "WHERE h.operation_type = #{operationType} " +
            "ORDER BY h.created_at DESC")
    List<AssetHistory> findByOperationType(@Param("operationType") AssetHistory.OperationType operationType);

    /**
     * 根据操作人查询历史记录
     */
    @Select("SELECT h.*, a.asset_code, a.product_model FROM asset_history h " +
            "LEFT JOIN assets a ON h.asset_id = a.id " +
            "WHERE h.operator = #{operator} " +
            "ORDER BY h.created_at DESC")
    List<AssetHistory> findByOperator(@Param("operator") String operator);

    /**
     * 分页查询用户的资产历史记录
     */
    @Select("SELECT h.*, a.asset_code, a.product_model FROM asset_history h " +
            "LEFT JOIN assets a ON h.asset_id = a.id " +
            "WHERE a.user_id = #{userId} " +
            "AND (#{operationType} IS NULL OR h.operation_type = #{operationType}) " +
            "AND (#{startTime} IS NULL OR h.created_at >= #{startTime}) " +
            "AND (#{endTime} IS NULL OR h.created_at <= #{endTime}) " +
            "ORDER BY h.created_at DESC")
    IPage<AssetHistory> findUserHistoryWithPagination(
            Page<AssetHistory> page,
            @Param("userId") Long userId,
            @Param("operationType") AssetHistory.OperationType operationType,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 分页查询所有历史记录（管理员使用）
     */
    @Select("SELECT h.*, a.asset_code, a.product_model, u.username, u.real_name FROM asset_history h " +
            "LEFT JOIN assets a ON h.asset_id = a.id " +
            "LEFT JOIN users u ON a.user_id = u.id " +
            "WHERE (#{keyword} IS NULL OR a.asset_code LIKE CONCAT('%', #{keyword}, '%') " +
            "OR a.product_model LIKE CONCAT('%', #{keyword}, '%') " +
            "OR h.operator LIKE CONCAT('%', #{keyword}, '%') " +
            "OR u.username LIKE CONCAT('%', #{keyword}, '%')) " +
            "AND (#{operationType} IS NULL OR h.operation_type = #{operationType}) " +
            "AND (#{startTime} IS NULL OR h.created_at >= #{startTime}) " +
            "AND (#{endTime} IS NULL OR h.created_at <= #{endTime}) " +
            "ORDER BY h.created_at DESC")
    IPage<AssetHistory> findAllHistoryWithPagination(
            Page<AssetHistory> page,
            @Param("keyword") String keyword,
            @Param("operationType") AssetHistory.OperationType operationType,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 统计操作类型分布
     */
    @Select("SELECT h.operation_type, COUNT(1) as count FROM asset_history h " +
            "LEFT JOIN assets a ON h.asset_id = a.id " +
            "WHERE (#{userId} IS NULL OR a.user_id = #{userId}) " +
            "GROUP BY h.operation_type ORDER BY count DESC")
    List<Object> countHistoryByOperationType(@Param("userId") Long userId);

    /**
     * 统计最近活动（最近7天）
     */
    @Select("SELECT h.*, a.asset_code, a.product_model FROM asset_history h " +
            "LEFT JOIN assets a ON h.asset_id = a.id " +
            "WHERE h.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) " +
            "AND (#{userId} IS NULL OR a.user_id = #{userId}) " +
            "ORDER BY h.created_at DESC " +
            "LIMIT #{limit}")
    List<AssetHistory> findRecentActivities(@Param("userId") Long userId, @Param("limit") int limit);

    /**
     * 根据时间范围统计操作数量
     */
    @Select("SELECT DATE(h.created_at) as date, COUNT(1) as count FROM asset_history h " +
            "LEFT JOIN assets a ON h.asset_id = a.id " +
            "WHERE h.created_at BETWEEN #{startTime} AND #{endTime} " +
            "AND (#{userId} IS NULL OR a.user_id = #{userId}) " +
            "GROUP BY DATE(h.created_at) ORDER BY date")
    List<Object> countHistoryByDateRange(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("userId") Long userId
    );

    /**
     * 删除资产相关的所有历史记录
     */
    @Select("DELETE FROM asset_history WHERE asset_id = #{assetId}")
    void deleteByAssetId(@Param("assetId") Long assetId);
}