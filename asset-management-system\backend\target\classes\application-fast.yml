# 快速启动配置 - 优化启动速度
server:
  port: 8080
  servlet:
    context-path: /api
  # 优化Tomcat启动
  tomcat:
    threads:
      min-spare: 4
      max: 50
    connection-timeout: 5000
    accept-count: 10

spring:
  application:
    name: asset-management-system
  
  # 数据源配置 - 优化连接池
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************************************************************************************************************************************************************************************************
    username: root
    password: 123456
    hikari:
      minimum-idle: 2  # 减少最小连接数
      maximum-pool-size: 8  # 减少最大连接数
      auto-commit: true
      idle-timeout: 30000
      pool-name: FastHikariCP
      max-lifetime: 1800000
      connection-timeout: 10000
      connection-init-sql: SELECT 1  # 连接初始化SQL
      
  # JPA配置 - 快速启动
  jpa:
    hibernate:
      ddl-auto: none  # 禁用DDL自动执行
    show-sql: false  # 禁用SQL日志
    open-in-view: false  # 禁用OpenInView
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: false
        jdbc:
          batch_size: 50
          fetch_size: 50
        cache:
          use_second_level_cache: false
          use_query_cache: false
        temp:
          use_jdbc_metadata_defaults: false

  # 文件上传
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

  # 禁用不必要的自动配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration

# MyBatis Plus配置 - 快速启动
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl  # 禁用SQL日志
    cache-enabled: false  # 禁用二级缓存
    lazy-loading-enabled: false  # 禁用延迟加载
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: ""
  type-aliases-package: com.asset.entity

# JWT配置
jwt:
  secret: assetManagementSystemSecretKey2024
  expiration: 86400000

# 日志配置 - 最小化日志输出
logging:
  level:
    root: WARN
    com.asset: INFO  # 只显示应用日志
    org.springframework: WARN
    org.hibernate: WARN
    com.zaxxer.hikari: WARN
    org.apache.ibatis: WARN
  pattern:
    console: "%d{HH:mm:ss} %-5level %logger{20} - %msg%n"

# 管理端点配置
management:
  endpoints:
    enabled-by-default: false  # 禁用所有端点
  endpoint:
    health:
      enabled: true  # 只启用健康检查
  server:
    port: -1  # 禁用管理端口
