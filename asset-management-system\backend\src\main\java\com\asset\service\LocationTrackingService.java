package com.asset.service;

import com.asset.dto.LocationChangeRequest;
import com.asset.dto.LocationHistoryResponse;
import com.asset.dto.PageResponse;
import com.asset.entity.Asset;
import com.asset.entity.LocationHistory;
import com.asset.exception.BusinessException;
import com.asset.exception.ResourceNotFoundException;
import com.asset.repository.AssetRepository;
import com.asset.repository.LocationHistoryRepository;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LocationTrackingService {

    @Autowired
    private AssetRepository assetRepository;

    @Autowired
    private LocationHistoryRepository locationHistoryRepository;

    @Autowired
    private AuthService authService;

    /**
     * 变更资产位置
     */
    @Transactional
    public void changeAssetLocation(Long assetId, LocationChangeRequest request) {
        // 获取资产信息
        Asset asset = assetRepository.selectById(assetId);
        if (asset == null) {
            throw new ResourceNotFoundException("资产不存在");
        }

        // 检查权限
        checkAssetPermission(asset);

        String currentUsername = authService.getCurrentUser().getUsername();
        String fromLocation = asset.getCurrentLocation();
        String toLocation = request.getToLocation();

        // 检查位置是否有变化
        if (fromLocation != null && fromLocation.equals(toLocation)) {
            throw new BusinessException("新位置与当前位置相同");
        }

        // 更新资产的当前位置
        asset.setCurrentLocation(toLocation);
        asset.setUpdatedAt(LocalDateTime.now());
        assetRepository.updateById(asset);

        // 记录位置变更历史
        LocationHistory history = new LocationHistory();
        history.setAssetId(assetId);
        history.setFromLocation(fromLocation);
        history.setToLocation(toLocation);
        history.setMoveReason(request.getMoveReason());
        history.setOperator(currentUsername);
        history.setMovedAt(LocalDateTime.now());

        locationHistoryRepository.insert(history);

        log.info("用户 {} 将资产 {} 从 {} 移动到 {}", 
                currentUsername, asset.getAssetCode(), fromLocation, toLocation);
    }

    /**
     * 获取资产位置历史
     */
    public PageResponse<LocationHistoryResponse> getAssetLocationHistory(Long assetId, int page, int size) {
        // 获取资产信息并检查权限
        Asset asset = assetRepository.selectById(assetId);
        if (asset == null) {
            throw new ResourceNotFoundException("资产不存在");
        }
        checkAssetPermission(asset);

        Page<LocationHistory> pageParam = new Page<>(page, size);
        IPage<LocationHistory> result = locationHistoryRepository.findByAssetIdWithPagination(pageParam, assetId);

        List<LocationHistoryResponse> records = result.getRecords().stream()
                .map(LocationHistoryResponse::fromLocationHistory)
                .collect(Collectors.toList());

        return PageResponse.of(records, result.getTotal(), result.getSize(), result.getCurrent());
    }

    /**
     * 获取位置变更历史列表（分页）
     */
    public PageResponse<LocationHistoryResponse> getLocationHistory(int page, int size, String keyword, 
                                                                   LocalDateTime startTime, LocalDateTime endTime) {
        Long currentUserId = authService.getCurrentUserId();
        boolean isAdmin = authService.isCurrentUserAdmin();

        Page<LocationHistory> pageParam = new Page<>(page, size);
        IPage<LocationHistory> result;

        if (isAdmin) {
            // 管理员可以查看所有位置历史
            result = locationHistoryRepository.findAllLocationHistoryWithPagination(
                    pageParam, keyword, startTime, endTime);
        } else {
            // 普通用户只能查看自己的位置历史
            result = locationHistoryRepository.findUserLocationHistoryWithPagination(
                    pageParam, currentUserId, startTime, endTime);
        }

        List<LocationHistoryResponse> records = result.getRecords().stream()
                .map(LocationHistoryResponse::fromLocationHistory)
                .collect(Collectors.toList());

        return PageResponse.of(records, result.getTotal(), result.getSize(), result.getCurrent());
    }

    /**
     * 获取最近的位置变更记录
     */
    public List<LocationHistoryResponse> getRecentLocationChanges(int limit) {
        Long currentUserId = authService.isCurrentUserAdmin() ? null : authService.getCurrentUserId();
        
        List<LocationHistory> histories = locationHistoryRepository.findRecentLocationChanges(currentUserId, limit);
        
        return histories.stream()
                .map(LocationHistoryResponse::fromLocationHistory)
                .collect(Collectors.toList());
    }

    /**
     * 根据位置查询历史记录
     */
    public List<LocationHistoryResponse> getLocationHistoryByLocation(String location) {
        List<LocationHistory> histories = locationHistoryRepository.findByLocation(location);
        
        // 过滤权限
        Long currentUserId = authService.getCurrentUserId();
        boolean isAdmin = authService.isCurrentUserAdmin();
        
        if (!isAdmin) {
            histories = histories.stream()
                    .filter(history -> {
                        Asset asset = assetRepository.selectById(history.getAssetId());
                        return asset != null && asset.getUserId().equals(currentUserId);
                    })
                    .collect(Collectors.toList());
        }
        
        return histories.stream()
                .map(LocationHistoryResponse::fromLocationHistory)
                .collect(Collectors.toList());
    }

    /**
     * 获取位置变更统计
     */
    public List<Object> getLocationChangeStatistics() {
        Long currentUserId = authService.isCurrentUserAdmin() ? null : authService.getCurrentUserId();
        return locationHistoryRepository.countLocationChanges(currentUserId);
    }

    /**
     * 根据时间范围统计位置变更数量
     */
    public List<Object> getLocationChangesByDateRange(LocalDateTime startTime, LocalDateTime endTime) {
        Long currentUserId = authService.isCurrentUserAdmin() ? null : authService.getCurrentUserId();
        return locationHistoryRepository.countLocationChangesByDateRange(startTime, endTime, currentUserId);
    }

    /**
     * 检查资产权限
     */
    private void checkAssetPermission(Asset asset) {
        if (!authService.isCurrentUserAdmin() && 
            !asset.getUserId().equals(authService.getCurrentUserId())) {
            throw new BusinessException(403, "无权限访问该资产");
        }
    }
}