import request from './request'

// 获取用户列表
export const getUserList = (params) => {
  return request({
    url: '/users',
    method: 'get',
    params
  })
}

// 获取用户详情
export const getUserDetail = (id) => {
  return request({
    url: `/users/${id}`,
    method: 'get'
  })
}

// 创建用户
export const createUser = (data) => {
  return request({
    url: '/users',
    method: 'post',
    data
  })
}

// 更新用户
export const updateUser = (id, data) => {
  return request({
    url: `/users/${id}`,
    method: 'put',
    data
  })
}

// 删除用户
export const deleteUser = (id) => {
  return request({
    url: `/users/${id}`,
    method: 'delete'
  })
}

// 批量删除用户
export const batchDeleteUsers = (ids) => {
  return request({
    url: '/users/batch',
    method: 'delete',
    data: ids
  })
}

// 重置用户密码
export const resetUserPassword = (id, newPassword) => {
  return request({
    url: `/users/${id}/reset-password`,
    method: 'put',
    data: { newPassword }
  })
}

// 更新用户状态
export const updateUserStatus = (id, status) => {
  return request({
    url: `/users/${id}/status`,
    method: 'put',
    data: { status }
  })
}

// 获取用户统计信息
export const getUserStatistics = () => {
  return request({
    url: '/users/statistics',
    method: 'get'
  })
}

// 检查用户名是否可用
export const checkUsername = (username) => {
  return request({
    url: '/users/check-username',
    method: 'get',
    params: { username }
  })
}

// 检查邮箱是否可用
export const checkEmail = (email) => {
  return request({
    url: '/users/check-email',
    method: 'get',
    params: { email }
  })
}

// 保存权限配置
export const savePermissionConfig = (permissions) => {
  return request({
    url: '/users/permissions/save',
    method: 'post',
    data: permissions
  })
}

// 获取权限配置
export const getPermissionConfig = () => {
  return request({
    url: '/users/permissions',
    method: 'get'
  })
}
