package com.asset.utils;

import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicInteger;

@Component
public class AssetCodeGenerator {
    
    private static final String PREFIX = "IT";
    private static final AtomicInteger SEQUENCE = new AtomicInteger(1);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy");
    
    /**
     * 生成资产编号
     * 格式: IT-YYYY-NNN (如: IT-2024-001)
     */
    public synchronized String generateAssetCode() {
        String year = LocalDateTime.now().format(DATE_FORMATTER);
        int sequence = SEQUENCE.getAndIncrement();
        
        // 如果序号超过999，重置为1
        if (sequence > 999) {
            SEQUENCE.set(1);
            sequence = 1;
        }
        
        return String.format("%s-%s-%03d", PREFIX, year, sequence);
    }
    
    /**
     * 生成带自定义前缀的资产编号
     */
    public synchronized String generateAssetCode(String customPrefix) {
        String year = LocalDateTime.now().format(DATE_FORMATTER);
        int sequence = SEQUENCE.getAndIncrement();
        
        if (sequence > 999) {
            SEQUENCE.set(1);
            sequence = 1;
        }
        
        return String.format("%s-%s-%03d", customPrefix, year, sequence);
    }
    
    /**
     * 验证资产编号格式
     */
    public boolean validateAssetCode(String assetCode) {
        if (assetCode == null || assetCode.trim().isEmpty()) {
            return false;
        }
        
        // 正则表达式验证格式: XX-YYYY-NNN
        String pattern = "^[A-Z]{2,5}-\\d{4}-\\d{3}$";
        return assetCode.matches(pattern);
    }
}