import{aT as s}from"./index-2733c819.js";const u=e=>s({url:"/users",method:"get",params:e}),a=e=>s({url:`/users/${e}`,method:"get"}),o=e=>s({url:"/users",method:"post",data:e}),n=(e,t)=>s({url:`/users/${e}`,method:"put",data:t}),c=e=>s({url:`/users/${e}`,method:"delete"}),m=e=>s({url:"/users/batch",method:"delete",data:e}),d=(e,t)=>s({url:`/users/${e}/status`,method:"put",data:{status:t}}),i=()=>s({url:"/users/statistics",method:"get"}),h=e=>s({url:"/users/check-username",method:"get",params:{username:e}}),l=e=>s({url:"/users/check-email",method:"get",params:{email:e}}),g=e=>s({url:"/users/permissions/save",method:"post",data:e}),p=()=>s({url:"/users/permissions",method:"get"});export{a,m as b,h as c,c as d,l as e,n as f,u as g,o as h,i,p as j,g as s,d as u};
