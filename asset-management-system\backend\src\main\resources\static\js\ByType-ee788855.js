import{_ as U}from"./_plugin-vue_export-helper-62491c14.js";/* empty css                   *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                     *//* empty css               *//* empty css                  *//* empty css                        *//* empty css               *//* empty css                */import{r as u,b as X,o as $,c as T,d as j,f as s,g as t,w as e,aq as q,j as v,E as H,aC as Q,F as m,G as J,a8 as n,b7 as K,a_ as W,b8 as Y,i,P as Z,H as tt,X as et,ap as B,z as at,aD as st,n as lt,aP as ot,aL as nt,aM as it,aN as dt,aO as rt,aQ as ct,aS as ut}from"./index-2733c819.js";import"./index-fd3ee58d.js";const _t={class:"statistics-by-type"},pt={class:"stat-content"},ft={class:"stat-icon server"},vt={class:"stat-info"},mt={class:"stat-value"},gt={class:"stat-content"},ht={class:"stat-icon network"},yt={class:"stat-info"},bt={class:"stat-value"},wt={class:"stat-content"},Ct={class:"stat-icon storage"},xt={class:"stat-info"},kt={class:"stat-value"},zt={class:"stat-content"},Et={class:"stat-icon other"},Tt={class:"stat-info"},Bt={class:"stat-value"},Dt={class:"card-header"},Rt={class:"card-header"},Vt={class:"card-header"},Lt={__name:"ByType",setup(Nt){const y=u(!1),k=u("30d"),b=u(),w=u(),p=u(null),C=u(null),r=X({server:0,network:0,storage:0,other:0}),D=u([]),z=()=>{q(()=>{R(),V()})},R=()=>{if(!b.value)return;p.value=B(b.value);const l={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left"},series:[{name:"产品类型",type:"pie",radius:"50%",data:[{value:r.server,name:"服务器"},{value:r.network,name:"网络设备"},{value:r.storage,name:"存储设备"},{value:r.other,name:"其他设备"}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};p.value.setOption(l)},V=()=>{if(!w.value)return;C.value=B(w.value);const l={tooltip:{trigger:"axis"},legend:{data:["服务器","网络设备","存储设备","其他设备"]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:["1月","2月","3月","4月","5月","6月","7月"]},yAxis:{type:"value"},series:[{name:"服务器",type:"line",stack:"Total",data:[12,15,18,22,28,35,45]},{name:"网络设备",type:"line",stack:"Total",data:[8,12,15,18,22,28,32]},{name:"存储设备",type:"line",stack:"Total",data:[5,8,12,15,20,25,28]},{name:"其他设备",type:"line",stack:"Total",data:[2,4,6,8,10,12,15]}]};C.value.setOption(l)},L=()=>{v.success("图表已刷新"),z()},N=()=>{if(p.value){const l=p.value.getDataURL({pixelRatio:2,backgroundColor:"#fff"}),a=document.createElement("a");a.href=l,a.download="产品类型分布图.png",a.click()}},O=()=>{v.success("数据导出功能开发中...")},S=l=>{v.info(`查看 ${l.productType} 的详细信息`)},G=l=>new Intl.NumberFormat("zh-CN").format(l),I=async()=>{y.value=!0;try{v.success("数据加载完成")}catch(l){v.error("加载数据失败: "+l.message)}finally{y.value=!1}};return $(()=>{I(),z(),window.addEventListener("resize",()=>{var l,a;(l=p.value)==null||l.resize(),(a=C.value)==null||a.resize()})}),(l,a)=>{const f=at,c=H,_=st,E=Q,g=lt,M=ot,x=nt,P=it,d=dt,h=rt,A=ct,F=ut;return T(),j("div",_t,[a[12]||(a[12]=s("div",{class:"page-header"},[s("h2",null,"分类统计"),s("p",null,"按产品类型统计资产分布情况")],-1)),t(E,{gutter:20,class:"stats-cards"},{default:e(()=>[t(_,{xs:24,sm:12,md:6,lg:6,xl:6},{default:e(()=>[t(c,{class:"stat-card"},{default:e(()=>[s("div",pt,[s("div",ft,[t(f,{size:"32"},{default:e(()=>[t(m(J))]),_:1})]),s("div",vt,[s("div",mt,n(r.server||0),1),a[1]||(a[1]=s("div",{class:"stat-label"},"服务器",-1))])])]),_:1})]),_:1}),t(_,{xs:24,sm:12,md:6,lg:6,xl:6},{default:e(()=>[t(c,{class:"stat-card"},{default:e(()=>[s("div",gt,[s("div",ht,[t(f,{size:"32"},{default:e(()=>[t(m(K))]),_:1})]),s("div",yt,[s("div",bt,n(r.network||0),1),a[2]||(a[2]=s("div",{class:"stat-label"},"网络设备",-1))])])]),_:1})]),_:1}),t(_,{xs:24,sm:12,md:6,lg:6,xl:6},{default:e(()=>[t(c,{class:"stat-card"},{default:e(()=>[s("div",wt,[s("div",Ct,[t(f,{size:"32"},{default:e(()=>[t(m(W))]),_:1})]),s("div",xt,[s("div",kt,n(r.storage||0),1),a[3]||(a[3]=s("div",{class:"stat-label"},"存储设备",-1))])])]),_:1})]),_:1}),t(_,{xs:24,sm:12,md:6,lg:6,xl:6},{default:e(()=>[t(c,{class:"stat-card"},{default:e(()=>[s("div",zt,[s("div",Et,[t(f,{size:"32"},{default:e(()=>[t(m(Y))]),_:1})]),s("div",Tt,[s("div",Bt,n(r.other||0),1),a[4]||(a[4]=s("div",{class:"stat-label"},"其他设备",-1))])])]),_:1})]),_:1})]),_:1}),t(E,{gutter:20,class:"chart-row"},{default:e(()=>[t(_,{xs:24,lg:12},{default:e(()=>[t(c,{class:"chart-card",shadow:"never"},{header:e(()=>[s("div",Dt,[a[7]||(a[7]=s("span",null,"产品类型分布",-1)),t(M,null,{default:e(()=>[t(g,{size:"small",onClick:L},{default:e(()=>a[5]||(a[5]=[i("刷新",-1)])),_:1,__:[5]}),t(g,{size:"small",onClick:N},{default:e(()=>a[6]||(a[6]=[i("导出",-1)])),_:1,__:[6]})]),_:1})])]),default:e(()=>[s("div",{ref_key:"pieChartRef",ref:b,class:"chart-container"},null,512)]),_:1})]),_:1}),t(_,{xs:24,lg:12},{default:e(()=>[t(c,{class:"chart-card",shadow:"never"},{header:e(()=>[s("div",Rt,[a[8]||(a[8]=s("span",null,"类型趋势分析",-1)),t(P,{modelValue:k.value,"onUpdate:modelValue":a[0]||(a[0]=o=>k.value=o),size:"small",style:{width:"120px"}},{default:e(()=>[t(x,{label:"最近7天",value:"7d"}),t(x,{label:"最近30天",value:"30d"}),t(x,{label:"最近90天",value:"90d"})]),_:1},8,["modelValue"])])]),default:e(()=>[s("div",{ref_key:"lineChartRef",ref:w,class:"chart-container"},null,512)]),_:1})]),_:1})]),_:1}),t(c,{class:"table-card",shadow:"never"},{header:e(()=>[s("div",Vt,[a[10]||(a[10]=s("span",null,"详细统计数据",-1)),t(g,{type:"primary",size:"small",onClick:O},{default:e(()=>[t(f,null,{default:e(()=>[t(m(Z))]),_:1}),a[9]||(a[9]=i(" 导出数据 ",-1))]),_:1,__:[9]})])]),default:e(()=>[tt((T(),et(A,{data:D.value,style:{width:"100%"}},{default:e(()=>[t(d,{prop:"productType",label:"产品类型",width:"150"}),t(d,{prop:"totalCount",label:"总数量",width:"100",align:"center"}),t(d,{prop:"pendingCount",label:"待入库",width:"100",align:"center"},{default:e(({row:o})=>[t(h,{type:"warning",size:"small"},{default:e(()=>[i(n(o.pendingCount),1)]),_:2},1024)]),_:1}),t(d,{prop:"receivedCount",label:"已入库",width:"100",align:"center"},{default:e(({row:o})=>[t(h,{type:"success",size:"small"},{default:e(()=>[i(n(o.receivedCount),1)]),_:2},1024)]),_:1}),t(d,{prop:"installedCount",label:"已安装",width:"100",align:"center"},{default:e(({row:o})=>[t(h,{type:"info",size:"small"},{default:e(()=>[i(n(o.installedCount),1)]),_:2},1024)]),_:1}),t(d,{prop:"outboundCount",label:"已出库",width:"100",align:"center"},{default:e(({row:o})=>[t(h,{type:"danger",size:"small"},{default:e(()=>[i(n(o.outboundCount),1)]),_:2},1024)]),_:1}),t(d,{prop:"totalValue",label:"总价值",width:"120",align:"right"},{default:e(({row:o})=>[i(" ¥"+n(G(o.totalValue)),1)]),_:1}),t(d,{prop:"percentage",label:"占比",width:"100",align:"center"},{default:e(({row:o})=>[i(n(o.percentage)+"% ",1)]),_:1}),t(d,{label:"操作",width:"150",fixed:"right"},{default:e(({row:o})=>[t(g,{type:"primary",size:"small",onClick:Ot=>S(o)},{default:e(()=>a[11]||(a[11]=[i(" 查看详情 ",-1)])),_:2,__:[11]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[F,y.value]])]),_:1})])}}},Qt=U(Lt,[["__scopeId","data-v-39a76057"]]);export{Qt as default};
