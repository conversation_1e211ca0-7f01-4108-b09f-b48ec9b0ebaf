@echo off
chcp 65001 >nul
echo 🚀 启动资产管理系统...
echo.

:: 检查Java环境
echo 🔍 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo ❌ Java未安装或未配置到PATH环境变量
    echo 💡 请安装Java 8或更高版本
    pause
    exit /b 1
)

:: 检查JAR文件
echo 🔍 检查JAR文件...
if not exist "backend\target\asset-management-backend-1.0.0.jar" (
    echo ❌ JAR文件不存在，请先运行构建脚本
    echo 💡 运行: python build_all.py
    pause
    exit /b 1
)

echo ✅ 环境检查通过
echo.
echo 📱 访问地址: http://localhost:8080
echo 🔧 后端API: http://localhost:8080/api
echo.
echo 💡 按 Ctrl+C 停止服务
echo.

:: 启动应用
java -jar "backend\target\asset-management-backend-1.0.0.jar"

echo.
echo 👋 应用已停止
pause
