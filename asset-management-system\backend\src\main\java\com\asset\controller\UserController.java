package com.asset.controller;

import com.asset.dto.ApiResponse;
import com.asset.dto.PageResponse;
import com.asset.entity.User;
import com.asset.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 用户管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;

    /**
     * 获取用户列表（分页）
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<PageResponse<User>> getUserList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) User.UserRole role,
            @RequestParam(required = false) User.UserStatus status) {
        try {
            PageResponse<User> result = userService.getUserList(page, size, keyword, role, status);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取用户列表失败: {}", e.getMessage());
            return ApiResponse.error("获取用户列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户详情
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<User> getUserDetail(@PathVariable Long id) {
        try {
            User user = userService.getUserById(id);
            return ApiResponse.success(user);
        } catch (Exception e) {
            log.error("获取用户详情失败: {}", e.getMessage());
            return ApiResponse.error("获取用户详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建用户
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<User> createUser(@Valid @RequestBody User user) {
        try {
            User createdUser = userService.createUser(user);
            return ApiResponse.success("用户创建成功", createdUser);
        } catch (Exception e) {
            log.error("创建用户失败: {}", e.getMessage());
            return ApiResponse.error("创建用户失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<User> updateUser(@PathVariable Long id, @Valid @RequestBody User user) {
        try {
            User updatedUser = userService.updateUser(id, user);
            return ApiResponse.success("用户更新成功", updatedUser);
        } catch (Exception e) {
            log.error("更新用户失败: {}", e.getMessage());
            return ApiResponse.error("更新用户失败: " + e.getMessage());
        }
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Void> deleteUser(@PathVariable Long id) {
        try {
            userService.deleteUser(id);
            return ApiResponse.success("用户删除成功");
        } catch (Exception e) {
            log.error("删除用户失败: {}", e.getMessage());
            return ApiResponse.error("删除用户失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除用户
     */
    @DeleteMapping("/batch")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Void> batchDeleteUsers(@RequestBody List<Long> ids) {
        try {
            userService.batchDeleteUsers(ids);
            return ApiResponse.success("批量删除成功");
        } catch (Exception e) {
            log.error("批量删除用户失败: {}", e.getMessage());
            return ApiResponse.error("批量删除用户失败: " + e.getMessage());
        }
    }

    /**
     * 重置用户密码
     */
    @PutMapping("/{id}/reset-password")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Void> resetUserPassword(@PathVariable Long id, @RequestBody Map<String, String> request) {
        try {
            String newPassword = request.get("newPassword");
            userService.resetUserPassword(id, newPassword);
            return ApiResponse.success("密码重置成功");
        } catch (Exception e) {
            log.error("重置用户密码失败: {}", e.getMessage());
            return ApiResponse.error("重置用户密码失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户状态
     */
    @PutMapping("/{id}/status")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Void> updateUserStatus(@PathVariable Long id, @RequestBody Map<String, String> request) {
        try {
            User.UserStatus status = User.UserStatus.valueOf(request.get("status"));
            userService.updateUserStatus(id, status);
            return ApiResponse.success("用户状态更新成功");
        } catch (Exception e) {
            log.error("更新用户状态失败: {}", e.getMessage());
            return ApiResponse.error("更新用户状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户统计信息
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Map<String, Object>> getUserStatistics() {
        try {
            Map<String, Object> statistics = userService.getUserStatistics();
            return ApiResponse.success(statistics);
        } catch (Exception e) {
            log.error("获取用户统计失败: {}", e.getMessage());
            return ApiResponse.error("获取用户统计失败: " + e.getMessage());
        }
    }

    /**
     * 检查用户名是否可用
     */
    @GetMapping("/check-username")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Boolean> checkUsername(@RequestParam String username) {
        try {
            boolean available = userService.isUsernameAvailable(username);
            return ApiResponse.success(available);
        } catch (Exception e) {
            log.error("检查用户名失败: {}", e.getMessage());
            return ApiResponse.error("检查用户名失败: " + e.getMessage());
        }
    }

    /**
     * 检查邮箱是否可用
     */
    @GetMapping("/check-email")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Boolean> checkEmail(@RequestParam String email) {
        try {
            boolean available = userService.isEmailAvailable(email);
            return ApiResponse.success(available);
        } catch (Exception e) {
            log.error("检查邮箱失败: {}", e.getMessage());
            return ApiResponse.error("检查邮箱失败: " + e.getMessage());
        }
    }
}
