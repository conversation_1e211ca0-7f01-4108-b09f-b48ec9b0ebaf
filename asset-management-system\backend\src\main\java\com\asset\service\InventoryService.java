package com.asset.service;

import com.asset.repository.AssetRepository;
import com.asset.repository.LocationHistoryRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class InventoryService {

    @Autowired
    private AssetRepository assetRepository;

    @Autowired
    private LocationHistoryRepository locationHistoryRepository;

    @Autowired
    private AuthService authService;

    /**
     * 获取完整的库存统计报告
     */
    public Map<String, Object> getCompleteInventoryReport() {
        Long currentUserId = authService.isCurrentUserAdmin() ? null : authService.getCurrentUserId();
        
        Map<String, Object> report = new HashMap<>();
        
        // 基础统计
        report.put("totalAssets", assetRepository.countAllAssets());
        report.put("myAssets", currentUserId != null ? assetRepository.countByUserId(currentUserId) : null);
        
        // 按状态统计
        report.put("statusStatistics", assetRepository.countAssetsByStatus());
        
        // 按位置统计（详细）
        report.put("locationInventory", assetRepository.getInventoryStatisticsByLocation());

        // 按类型统计（详细）
        report.put("typeInventory", assetRepository.getInventoryStatisticsByType());
        
        // 资产价值统计
        report.put("valueStatistics", assetRepository.getAssetValueStatistics());
        
        // 即将过保资产
        report.put("warrantyExpiry", assetRepository.findAssetsNearWarrantyExpiry(currentUserId));
        
        // 最近位置变更统计
        report.put("recentLocationChanges", locationHistoryRepository.findRecentLocationChanges(currentUserId, 10));
        
        // 位置变更频率统计
        report.put("locationChangeFrequency", locationHistoryRepository.countLocationChanges(currentUserId));
        
        return report;
    }

    /**
     * 获取库存盘点数据
     */
    public Map<String, Object> getInventoryAuditData() {
        Long currentUserId = authService.isCurrentUserAdmin() ? null : authService.getCurrentUserId();
        
        Map<String, Object> auditData = new HashMap<>();
        
        // 按位置分组的详细库存
        auditData.put("locationInventory", assetRepository.getInventoryStatisticsByLocation());

        // 按类型分组的详细库存
        auditData.put("typeInventory", assetRepository.getInventoryStatisticsByType());
        
        // 总价值统计
        auditData.put("valueStatistics", assetRepository.getAssetValueStatistics());
        
        return auditData;
    }

    /**
     * 获取库存变化趋势
     */
    public Map<String, Object> getInventoryTrends(LocalDateTime startTime, LocalDateTime endTime) {
        Long currentUserId = authService.isCurrentUserAdmin() ? null : authService.getCurrentUserId();
        
        Map<String, Object> trends = new HashMap<>();
        
        // 资产数量变化趋势
        trends.put("assetTrends", assetRepository.countAssetsByDateRange(startTime, endTime, currentUserId));
        
        // 位置变更趋势
        trends.put("locationChangeTrends", locationHistoryRepository.countLocationChangesByDateRange(startTime, endTime, currentUserId));
        
        return trends;
    }

    /**
     * 获取库存异常报告
     */
    public Map<String, Object> getInventoryAnomalies() {
        Long currentUserId = authService.isCurrentUserAdmin() ? null : authService.getCurrentUserId();
        
        Map<String, Object> anomalies = new HashMap<>();
        
        // 即将过保资产
        anomalies.put("warrantyExpiry", assetRepository.findAssetsNearWarrantyExpiry(currentUserId));
        
        // 无位置资产
        // anomalies.put("noLocationAssets", assetRepository.findAssetsWithoutLocation(currentUserId));
        
        return anomalies;
    }

    /**
     * 生成库存摘要
     */
    public Map<String, Object> getInventorySummary() {
        Long currentUserId = authService.isCurrentUserAdmin() ? null : authService.getCurrentUserId();
        
        Map<String, Object> summary = new HashMap<>();
        
        // 基础数量
        if (currentUserId == null) {
            summary.put("totalAssets", assetRepository.countAllAssets());
        } else {
            summary.put("totalAssets", assetRepository.countByUserId(currentUserId));
        }
        
        // 状态分布
        List<Object> statusStats = assetRepository.countAssetsByStatus();
        summary.put("statusDistribution", statusStats);

        // 价值统计
        Object valueStats = assetRepository.getAssetValueStatistics();
        summary.put("valueStatistics", valueStats);
        
        // 位置数量
        List<String> locations = assetRepository.findDistinctLocations(currentUserId);
        summary.put("locationCount", locations.size());
        
        // 类型数量
        List<String> types = assetRepository.findDistinctProductTypes(currentUserId);
        summary.put("typeCount", types.size());
        
        return summary;
    }
}