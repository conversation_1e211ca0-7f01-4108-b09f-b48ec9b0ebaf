package com.asset.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "assets")
@TableName("assets")
public class Asset {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Long id;

    @Column(name = "asset_code", length = 100)
    private String assetCode;

    @Column(name = "serial_number", length = 100)
    private String serialNumber;

    @Column(name = "product_model", length = 100)
    private String productModel;

    @Column(name = "product_type", length = 100)
    private String productType;

    @Column(length = 100)
    private String brand;

    @Column(columnDefinition = "TEXT")
    private String specification;

    @Column(name = "purchase_date")
    private LocalDate purchaseDate;

    @DecimalMin(value = "0.0", message = "采购价格不能为负数")
    @Column(name = "purchase_price", precision = 10, scale = 2)
    private BigDecimal purchasePrice;

    @Column(length = 200)
    private String supplier;

    @Column(name = "warranty_period")
    private Integer warrantyPeriod;

    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    private AssetStatus status = AssetStatus.PENDING;

    @Column(name = "current_location", length = 200)
    private String currentLocation;

    @Column(length = 100)
    private String receiver;

    @Column(name = "received_at")
    private LocalDateTime receivedAt;

    @Column(length = 100)
    private String installer;

    @Column(name = "installed_at")
    private LocalDateTime installedAt;

    @Column(name = "install_location", length = 200)
    private String installLocation;

    @Column(name = "outbound_reason", length = 500)
    private String outboundReason;

    @Column(name = "outbound_operator", length = 100)
    private String outboundOperator;

    @Column(name = "outbound_at")
    private LocalDateTime outboundAt;

    @Column(columnDefinition = "TEXT")
    private String notes;

    @NotNull(message = "所属用户不能为空")
    @Column(name = "user_id", nullable = false)
    private Long userId;

    @TableField(fill = FieldFill.INSERT)
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @TableLogic
    @Column(name = "deleted")
    private Integer deleted = 0;

    // 多对一关系：多个资产属于一个用户
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    @TableField(exist = false)
    private User user;

    // 一对多关系：资产拥有多个历史记录
    @OneToMany(mappedBy = "assetId", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @TableField(exist = false)
    private List<AssetHistory> histories;

    // 一对多关系：资产拥有多个位置历史
    @OneToMany(mappedBy = "assetId", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @TableField(exist = false)
    private List<LocationHistory> locationHistories;

    // 一对多关系：资产拥有多个备注
    @OneToMany(mappedBy = "assetId", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @TableField(exist = false)
    private List<AssetNote> assetNotes;

    public enum AssetStatus {
        PENDING("待处理"),
        RECEIVED("已入库"),
        INSTALLED("已安装"),
        OUTBOUND("已出库"),
        SCRAPPED("已报废");

        private final String description;

        AssetStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}