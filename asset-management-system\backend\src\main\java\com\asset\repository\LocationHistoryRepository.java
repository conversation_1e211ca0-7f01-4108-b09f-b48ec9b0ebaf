package com.asset.repository;

import com.asset.entity.LocationHistory;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface LocationHistoryRepository extends BaseMapper<LocationHistory> {

    /**
     * 分页查询资产位置历史
     */
    @Select("SELECT lh.*, a.asset_code, a.product_model FROM location_history lh " +
            "LEFT JOIN assets a ON lh.asset_id = a.id " +
            "WHERE lh.asset_id = #{assetId} " +
            "ORDER BY lh.moved_at DESC")
    IPage<LocationHistory> findByAssetIdWithPagination(Page<LocationHistory> page, @Param("assetId") Long assetId);

    /**
     * 查询资产的所有位置历史
     */
    @Select("SELECT * FROM location_history WHERE asset_id = #{assetId} ORDER BY moved_at DESC")
    List<LocationHistory> findByAssetId(@Param("assetId") Long assetId);

    /**
     * 根据位置查询历史记录
     */
    @Select("SELECT lh.*, a.asset_code, a.product_model FROM location_history lh " +
            "LEFT JOIN assets a ON lh.asset_id = a.id " +
            "WHERE lh.from_location = #{location} OR lh.to_location = #{location} " +
            "ORDER BY lh.moved_at DESC")
    List<LocationHistory> findByLocation(@Param("location") String location);

    /**
     * 分页查询用户的位置变更历史
     */
    @Select("SELECT lh.*, a.asset_code, a.product_model FROM location_history lh " +
            "LEFT JOIN assets a ON lh.asset_id = a.id " +
            "WHERE a.user_id = #{userId} " +
            "AND (#{startTime} IS NULL OR lh.moved_at >= #{startTime}) " +
            "AND (#{endTime} IS NULL OR lh.moved_at <= #{endTime}) " +
            "ORDER BY lh.moved_at DESC")
    IPage<LocationHistory> findUserLocationHistoryWithPagination(
            Page<LocationHistory> page,
            @Param("userId") Long userId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 分页查询所有位置变更历史（管理员使用）
     */
    @Select("SELECT lh.*, a.asset_code, a.product_model, u.username, u.real_name FROM location_history lh " +
            "LEFT JOIN assets a ON lh.asset_id = a.id " +
            "LEFT JOIN users u ON a.user_id = u.id " +
            "WHERE (#{keyword} IS NULL OR a.asset_code LIKE CONCAT('%', #{keyword}, '%') " +
            "OR a.product_model LIKE CONCAT('%', #{keyword}, '%') " +
            "OR lh.from_location LIKE CONCAT('%', #{keyword}, '%') " +
            "OR lh.to_location LIKE CONCAT('%', #{keyword}, '%') " +
            "OR lh.operator LIKE CONCAT('%', #{keyword}, '%')) " +
            "AND (#{startTime} IS NULL OR lh.moved_at >= #{startTime}) " +
            "AND (#{endTime} IS NULL OR lh.moved_at <= #{endTime}) " +
            "ORDER BY lh.moved_at DESC")
    IPage<LocationHistory> findAllLocationHistoryWithPagination(
            Page<LocationHistory> page,
            @Param("keyword") String keyword,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 统计位置变更频率
     */
    @Select("SELECT to_location, COUNT(1) as count FROM location_history lh " +
            "LEFT JOIN assets a ON lh.asset_id = a.id " +
            "WHERE to_location IS NOT NULL " +
            "AND (#{userId} IS NULL OR a.user_id = #{userId}) " +
            "GROUP BY to_location ORDER BY count DESC")
    List<Object> countLocationChanges(@Param("userId") Long userId);

    /**
     * 获取最近的位置变更记录
     */
    @Select("SELECT lh.*, a.asset_code, a.product_model FROM location_history lh " +
            "LEFT JOIN assets a ON lh.asset_id = a.id " +
            "WHERE lh.moved_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) " +
            "AND (#{userId} IS NULL OR a.user_id = #{userId}) " +
            "ORDER BY lh.moved_at DESC " +
            "LIMIT #{limit}")
    List<LocationHistory> findRecentLocationChanges(@Param("userId") Long userId, @Param("limit") int limit);

    /**
     * 根据时间范围统计位置变更数量
     */
    @Select("SELECT DATE(lh.moved_at) as date, COUNT(1) as count FROM location_history lh " +
            "LEFT JOIN assets a ON lh.asset_id = a.id " +
            "WHERE lh.moved_at BETWEEN #{startTime} AND #{endTime} " +
            "AND (#{userId} IS NULL OR a.user_id = #{userId}) " +
            "GROUP BY DATE(lh.moved_at) ORDER BY date")
    List<Object> countLocationChangesByDateRange(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("userId") Long userId
    );

    /**
     * 删除资产相关的所有位置历史
     */
    @Select("DELETE FROM location_history WHERE asset_id = #{assetId}")
    void deleteByAssetId(@Param("assetId") Long assetId);
}