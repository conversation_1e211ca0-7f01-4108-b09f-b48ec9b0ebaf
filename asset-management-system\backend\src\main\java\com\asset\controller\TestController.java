package com.asset.controller;

import com.asset.entity.User;
import com.asset.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

@RestController
@RequestMapping("/test")
public class TestController {

    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private PasswordEncoder passwordEncoder;

    @GetMapping("/user/{username}")
    public String testUser(@PathVariable String username) {
        Optional<User> user = userRepository.findByUsername(username);
        if (user.isPresent()) {
            return "用户存在: " + user.get().getUsername() + ", 角色: " + user.get().getRole();
        } else {
            return "用户不存在";
        }
    }
    
    @PostMapping("/password")
    public String testPassword(@RequestParam String raw, @RequestParam String encoded) {
        boolean matches = passwordEncoder.matches(raw, encoded);
        return "密码匹配: " + matches;
    }

    @PostMapping("/reset-admin-password")
    public String resetAdminPassword() {
        Optional<User> userOpt = userRepository.findByUsername("admin");
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            String newPassword = passwordEncoder.encode("admin123");
            user.setPassword(newPassword);
            userRepository.updateById(user);
            return "管理员密码已重置为: admin123, 新哈希: " + newPassword;
        } else {
            return "管理员用户不存在";
        }
    }
}