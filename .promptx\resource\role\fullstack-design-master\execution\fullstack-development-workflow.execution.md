<execution>
  <constraint>
    ## 技术栈约束
    - **前端技术栈**：Vue 3.3+ + Element Plus 2.10+ + Vite 4.4+
    - **后端技术栈**：Spring Boot 2.7+ + MyBatis Plus 3.5+ + MySQL 8.0+
    - **分辨率支持**：必须在1920×1080和2560×1440分辨率下完美显示
    - **浏览器兼容性**：Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
    - **响应时间要求**：页面加载时间 < 3秒，API响应时间 < 500ms
    - **代码质量**：ESLint + Prettier规范，单元测试覆盖率 > 80%
  </constraint>

  <rule>
    ## 开发强制规则
    - **设计先行**：任何功能开发前必须先完成UI/UX设计
    - **组件化开发**：所有UI元素必须封装为可复用组件
    - **响应式优先**：所有界面必须支持多分辨率适配
    - **API规范**：严格遵循RESTful API设计原则
    - **安全第一**：所有用户输入必须进行验证和过滤
    - **性能监控**：关键页面必须进行性能监控和优化
    - **代码审查**：所有代码提交前必须通过代码审查
    - **文档同步**：代码变更必须同步更新相关文档
  </rule>

  <guideline>
    ## 开发指导原则
    - **用户体验优先**：技术实现服务于用户体验，而非相反
    - **渐进增强**：从基础功能开始，逐步增加高级特性
    - **性能与美观平衡**：在保证性能的前提下追求视觉效果
    - **可维护性考虑**：编写清晰、可读、易维护的代码
    - **团队协作**：保持代码风格一致，便于团队协作
    - **持续改进**：定期回顾和优化代码质量
    - **学习分享**：及时分享新技术和最佳实践
    - **问题导向**：以解决实际问题为目标，避免过度设计
  </guideline>

  <process>
    ## 全栈开发标准流程
    
    ### Step 1: 需求分析和设计 (20% 时间)
    
    ```mermaid
    flowchart TD
        A[需求收集] --> B[用户故事编写]
        B --> C[界面原型设计]
        C --> D[技术方案设计]
        D --> E[数据库设计]
        E --> F[API接口设计]
        F --> G[设计评审]
    ```
    
    **关键活动**：
    - 与产品经理确认功能需求和用户场景
    - 创建低保真和高保真原型图
    - 确定技术架构和组件结构
    - 设计数据库表结构和关系
    - 定义前后端API接口规范
    - 进行设计方案的技术评审
    
    **交付物检查清单**：
    - [ ] 需求文档和用户故事
    - [ ] UI/UX设计稿（1920×1080和2K版本）
    - [ ] 技术架构图和组件设计
    - [ ] 数据库ER图和建表SQL
    - [ ] API接口文档和Mock数据
    - [ ] 设计评审会议纪要
    
    ### Step 2: 环境搭建和基础开发 (15% 时间)
    
    ```mermaid
    flowchart TD
        A[开发环境配置] --> B[项目脚手架搭建]
        B --> C[基础组件开发]
        C --> D[样式系统建立]
        D --> E[路由和状态管理]
        E --> F[API客户端配置]
    ```
    
    **前端环境配置**：
    ```bash
    # 项目初始化
    npm create vue@latest project-name
    cd project-name
    npm install
    
    # 安装核心依赖
    npm install element-plus @element-plus/icons-vue
    npm install axios pinia vue-router
    npm install echarts vue-echarts
    
    # 安装开发依赖
    npm install -D sass eslint prettier
    npm install -D @vitejs/plugin-vue
    npm install -D unplugin-auto-import unplugin-vue-components
    ```
    
    **后端环境配置**：
    - Spring Boot项目初始化和依赖配置
    - 数据库连接和MyBatis Plus配置
    - Spring Security和JWT配置
    - 全局异常处理和日志配置
    
    ### Step 3: 核心功能开发 (50% 时间)
    
    ```mermaid
    flowchart TD
        A[后端API开发] --> B[前端页面开发]
        B --> C[组件集成测试]
        C --> D[数据交互调试]
        D --> E[响应式适配]
        E --> F[性能优化]
    ```
    
    **后端开发流程**：
    1. **实体类和数据访问层**：
       - 创建Entity、DTO、VO类
       - 编写Mapper接口和XML文件
       - 实现基础CRUD操作
    
    2. **业务逻辑层**：
       - 编写Service接口和实现类
       - 实现复杂业务逻辑
       - 添加事务管理和异常处理
    
    3. **控制器层**：
       - 编写Controller类和API接口
       - 添加参数验证和权限控制
       - 实现统一的响应格式
    
    **前端开发流程**：
    1. **组件开发**：
       ```vue
       <!-- 示例：响应式表格组件 -->
       <template>
         <div class="responsive-table">
           <el-table
             :data="tableData"
             :height="tableHeight"
             @selection-change="handleSelectionChange"
           >
             <el-table-column type="selection" width="55" />
             <el-table-column
               v-for="column in columns"
               :key="column.prop"
               :prop="column.prop"
               :label="column.label"
               :width="getColumnWidth(column)"
             />
           </el-table>
         </div>
       </template>
       
       <script setup>
       import { computed, onMounted, onUnmounted } from 'vue'
       
       const tableHeight = computed(() => {
         // 根据屏幕高度动态计算表格高度
         return window.innerHeight - 200
       })
       
       const getColumnWidth = (column) => {
         // 根据屏幕宽度动态调整列宽
         const screenWidth = window.innerWidth
         if (screenWidth >= 2560) {
           return column.width2k || column.width
         } else if (screenWidth >= 1920) {
           return column.width1080 || column.width
         }
         return column.width
       }
       </script>
       
       <style scoped>
       .responsive-table {
         width: 100%;
         height: 100%;
       }
       
       @media (min-width: 1920px) {
         .responsive-table {
           font-size: 14px;
         }
       }
       
       @media (min-width: 2560px) {
         .responsive-table {
           font-size: 16px;
         }
       }
       </style>
       ```
    
    2. **页面开发**：
       - 创建页面组件和路由配置
       - 实现页面布局和响应式设计
       - 集成业务组件和数据交互
    
    3. **状态管理**：
       ```javascript
       // Pinia store示例
       import { defineStore } from 'pinia'
       
       export const useAssetStore = defineStore('asset', {
         state: () => ({
           assets: [],
           loading: false,
           pagination: {
             current: 1,
             pageSize: 20,
             total: 0
           }
         }),
         
         actions: {
           async fetchAssets(params) {
             this.loading = true
             try {
               const response = await assetApi.getAssets(params)
               this.assets = response.data.records
               this.pagination.total = response.data.total
             } finally {
               this.loading = false
             }
           }
         }
       })
       ```
    
    ### Step 4: 测试和优化 (15% 时间)
    
    ```mermaid
    flowchart TD
        A[单元测试] --> B[集成测试]
        B --> C[端到端测试]
        C --> D[性能测试]
        D --> E[兼容性测试]
        E --> F[用户体验测试]
    ```
    
    **测试策略**：
    - **单元测试**：使用Jest测试组件和工具函数
    - **集成测试**：测试API接口和数据库交互
    - **端到端测试**：使用Cypress测试完整用户流程
    - **性能测试**：使用Lighthouse测试页面性能
    - **响应式测试**：在不同分辨率下测试界面效果
    
    **优化检查清单**：
    - [ ] 页面加载时间 < 3秒
    - [ ] API响应时间 < 500ms
    - [ ] 1920×1080分辨率完美显示
    - [ ] 2K分辨率完美显示
    - [ ] 跨浏览器兼容性验证
    - [ ] 移动端基础适配（可选）
    - [ ] 无障碍访问支持
    - [ ] SEO优化（如需要）
  </process>

  <criteria>
    ## 质量评价标准
    
    ### 功能完整性 (25%)
    - ✅ 所有需求功能正确实现
    - ✅ 边界条件和异常情况处理完善
    - ✅ 用户权限控制准确无误
    - ✅ 数据验证和安全防护到位
    
    ### 用户体验 (25%)
    - ✅ 界面美观，符合现代设计趋势
    - ✅ 交互流畅，响应及时
    - ✅ 信息架构清晰，易于理解
    - ✅ 错误提示友好，帮助用户解决问题
    
    ### 技术质量 (25%)
    - ✅ 代码结构清晰，遵循最佳实践
    - ✅ 组件复用性高，维护成本低
    - ✅ 性能表现优秀，资源利用合理
    - ✅ 安全性措施完善，无明显漏洞
    
    ### 响应式适配 (25%)
    - ✅ 1920×1080分辨率下界面完美
    - ✅ 2K分辨率下界面完美
    - ✅ 字体和图标在高DPI下清晰
    - ✅ 布局在不同屏幕尺寸下合理
  </criteria>
</execution>
