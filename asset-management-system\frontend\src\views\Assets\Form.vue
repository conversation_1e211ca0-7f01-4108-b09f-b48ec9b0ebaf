<template>
  <div class="asset-form">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" :icon="ArrowLeft" class="back-btn">
          返回
        </el-button>
        <div class="header-title">
          <h2>{{ isEdit ? '编辑资产' : '新增资产' }}</h2>
          <p>{{ isEdit ? '修改资产信息' : '添加新的资产信息' }}</p>
        </div>
      </div>
    </div>

    <!-- 表单内容 -->
    <el-card class="form-card" v-loading="loading">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        size="large"
        class="asset-form-content"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <h3 class="section-title">基本信息</h3>
          
          <el-row :gutter="24">
            <el-col :xl="8" :lg="12" :md="24">
              <el-form-item label="资产编号" prop="assetCode">
                <el-input
                  v-model="formData.assetCode"
                  placeholder="留空自动生成"
                  clearable
                />
              </el-form-item>
            </el-col>

            <el-col :xl="8" :lg="12" :md="24">
              <el-form-item label="序列号" prop="serialNumber">
                <el-input
                  v-model="formData.serialNumber"
                  placeholder="请输入序列号"
                  clearable
                />
              </el-form-item>
            </el-col>

            <el-col :xl="8" :lg="24" :md="24">
              <el-form-item label="产品型号" prop="productModel">
                <el-input
                  v-model="formData.productModel"
                  placeholder="请输入产品型号"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :xl="8" :lg="12" :md="24">
              <el-form-item label="产品类型" prop="productType">
                <div class="product-type-input">
                  <el-select
                    v-model="formData.productType"
                    placeholder="选择或输入产品类型"
                    filterable
                    allow-create
                    default-first-option
                    class="type-select"
                  >
                    <el-option
                      v-for="type in productTypes"
                      :key="type.id || type"
                      :label="type.name || type"
                      :value="type.name || type"
                    />
                  </el-select>
                  <el-button
                    type="primary"
                    link
                    @click="openTypeManager"
                    class="manage-btn"
                    title="管理产品类型"
                  >
                    <el-icon><Setting /></el-icon>
                  </el-button>
                </div>
              </el-form-item>
            </el-col>

            <el-col :xl="8" :lg="12" :md="24">
              <el-form-item label="品牌" prop="brand">
                <el-input
                  v-model="formData.brand"
                  placeholder="请输入品牌"
                  clearable
                />
              </el-form-item>
            </el-col>

            <el-col :xl="8" :lg="24" :md="24">
              <el-form-item label="供应商" prop="supplier">
                <el-input
                  v-model="formData.supplier"
                  placeholder="请输入供应商"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="规格说明" prop="specification">
            <el-input
              v-model="formData.specification"
              type="textarea"
              :rows="3"
              placeholder="请输入规格说明"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </div>

        <!-- 采购信息 -->
        <div class="form-section">
          <h3 class="section-title">采购信息</h3>
          
          <el-row :gutter="24">
            <el-col :xl="8" :lg="12" :md="24">
              <el-form-item label="采购日期" prop="purchaseDate">
                <el-date-picker
                  v-model="formData.purchaseDate"
                  type="date"
                  placeholder="选择采购日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  class="full-width"
                />
              </el-form-item>
            </el-col>

            <el-col :xl="8" :lg="12" :md="24">
              <el-form-item label="采购价格" prop="purchasePrice">
                <el-input
                  v-model.number="formData.purchasePrice"
                  placeholder="请输入采购价格"
                  clearable
                >
                  <template #prepend>¥</template>
                </el-input>
              </el-form-item>
            </el-col>

            <el-col :xl="8" :lg="24" :md="24">
              <el-form-item label="保修期" prop="warrantyPeriod">
                <el-input
                  v-model.number="formData.warrantyPeriod"
                  placeholder="请输入保修期"
                  clearable
                >
                  <template #append>个月</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :xl="8" :lg="12" :md="24">
              <el-form-item label="资产状态" prop="status">
                <el-select
                  v-model="formData.status"
                  placeholder="选择资产状态"
                  class="full-width"
                >
                  <el-option label="待处理" value="PENDING" />
                  <el-option label="已入库" value="RECEIVED" />
                  <el-option label="已安装" value="INSTALLED" />
                  <el-option label="已出库" value="OUTBOUND" />
                  <el-option label="已报废" value="SCRAPPED" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 位置信息 -->
        <div class="form-section">
          <h3 class="section-title">位置信息</h3>
          
          <el-form-item label="当前位置" prop="currentLocation">
            <el-select
              v-model="formData.currentLocation"
              placeholder="选择或输入当前位置"
              filterable
              allow-create
              default-first-option
              class="full-width"
            >
              <el-option
                v-for="location in locations"
                :key="location"
                :label="location"
                :value="location"
              />
            </el-select>
          </el-form-item>
        </div>

        <!-- 备注信息 -->
        <div class="form-section">
          <h3 class="section-title">备注信息</h3>
          
          <el-form-item label="备注" prop="notes">
            <el-input
              v-model="formData.notes"
              type="textarea"
              :rows="4"
              placeholder="请输入备注信息"
              maxlength="1000"
              show-word-limit
            />
          </el-form-item>
        </div>

        <!-- 提交按钮 -->
        <div class="form-actions">
          <el-button @click="goBack" size="large">
            取消
          </el-button>
          <el-button 
            type="primary" 
            @click="handleSubmit"
            :loading="submitting"
            size="large"
          >
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getAssetById, createAsset, updateAsset, getProductTypes, getLocations } from '@/api/asset'
import { Setting } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

const formRef = ref()
const loading = ref(false)
const submitting = ref(false)
const productTypes = ref([])
const locations = ref([])

// 是否为编辑模式
const isEdit = computed(() => route.name === 'AssetEdit')

// 表单数据
const formData = reactive({
  assetCode: '',
  serialNumber: '',
  productModel: '',
  productType: '',
  brand: '',
  supplier: '',
  specification: '',
  purchaseDate: '',
  purchasePrice: null,
  warrantyPeriod: null,
  status: 'PENDING',
  currentLocation: '',
  notes: ''
})

// 表单验证规则
const formRules = {
  productModel: [
    { required: true, message: '请输入产品型号', trigger: 'blur' }
  ],
  productType: [
    { required: true, message: '请选择产品类型', trigger: 'change' }
  ],
  purchasePrice: [
    { type: 'number', min: 0, message: '采购价格不能为负数', trigger: 'blur' }
  ],
  warrantyPeriod: [
    { type: 'number', min: 0, message: '保修期不能为负数', trigger: 'blur' }
  ]
}

// 加载资产详情（编辑模式）
const loadAssetDetail = async () => {
  if (!isEdit.value) return
  
  loading.value = true
  try {
    const { data } = await getAssetById(route.params.id)
    
    // 填充表单数据
    Object.keys(formData).forEach(key => {
      if (data[key] !== undefined && data[key] !== null) {
        formData[key] = data[key]
      }
    })
    
  } catch (error) {
    ElMessage.error('加载资产详情失败')
    goBack()
  } finally {
    loading.value = false
  }
}

// 加载产品类型和位置选项
const loadOptions = async () => {
  try {
    const [typesResult, locationsResult] = await Promise.all([
      getProductTypes(),
      getLocations()
    ])

    // 处理产品类型数据，支持新的结构化格式
    const types = typesResult.data || []
    productTypes.value = types.map(type => {
      if (typeof type === 'string') {
        return { id: type, name: type }
      }
      return type
    })

    locations.value = locationsResult.data || []

  } catch (error) {
    console.error('加载选项失败:', error)
    // 提供默认的产品类型
    productTypes.value = [
      { id: 1, name: '计算机设备' },
      { id: 2, name: '网络设备' },
      { id: 3, name: '办公设备' },
      { id: 4, name: '移动设备' },
      { id: 5, name: '服务器设备' }
    ]
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    // 清理空值
    const submitData = {}
    Object.keys(formData).forEach(key => {
      const value = formData[key]
      if (value !== null && value !== undefined && value !== '') {
        submitData[key] = value
      }
    })
    
    if (isEdit.value) {
      await updateAsset(route.params.id, submitData)
      ElMessage.success('资产更新成功')
    } else {
      await createAsset(submitData)
      ElMessage.success('资产创建成功')
    }
    
    router.push('/assets')
    
  } catch (error) {
    if (error.errors) {
      // 表单验证错误
      return
    }
    
    ElMessage.error(isEdit.value ? '资产更新失败' : '资产创建失败')
  } finally {
    submitting.value = false
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 打开产品类型管理器
const openTypeManager = () => {
  // 在新标签页中打开产品类型管理页面
  const routeData = router.resolve('/settings/product-types')
  window.open(routeData.href, '_blank')
}

// 初始化
onMounted(() => {
  loadOptions()
  if (isEdit.value) {
    loadAssetDetail()
  }
})
</script>

<style scoped lang="scss">
.asset-form {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-btn {
  border: none;
  background: #f5f5f5;
  color: #666;
  
  &:hover {
    background: #e6e6e6;
    color: #333;
  }
}

.header-title {
  h2 {
    margin: 0 0 4px 0;
    font-size: 24px;
    font-weight: 600;
    color: #333;
  }
  
  p {
    margin: 0;
    color: #666;
    font-size: 14px;
  }
}

.form-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.asset-form-content {
  max-width: 1200px;
  width: 100%;
}

.form-section {
  margin-bottom: 32px;
  
  &:last-child {
    margin-bottom: 24px;
  }
}

.section-title {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 40px;
    height: 2px;
    background: #409eff;
  }
}

.full-width {
  width: 100%;
}

.product-type-input {
  display: flex;
  align-items: center;
  gap: 8px;

  .type-select {
    flex: 1;
  }

  .manage-btn {
    flex-shrink: 0;
    padding: 8px;

    &:hover {
      background: #f0f9ff;
    }
  }
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
  
  .el-button {
    min-width: 120px;
    height: 44px;
    font-size: 16px;
    font-weight: 500;
  }
}

// Element Plus 样式覆盖
:deep(.el-card__body) {
  padding: 32px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #333;
}

:deep(.el-input-group__prepend),
:deep(.el-input-group__append) {
  background: #f5f7fa;
  color: #666;
  border-color: #dcdfe6;
}

:deep(.el-textarea__inner) {
  resize: vertical;
  min-height: 80px;
}

:deep(.el-select .el-input__inner) {
  cursor: pointer;
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .header-left {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .asset-form-content {
    max-width: 100%;
  }
  
  .form-actions {
    flex-direction: column;
    
    .el-button {
      width: 100%;
    }
  }
}

@media (max-width: 480px) {
  :deep(.el-card__body) {
    padding: 20px;
  }
  
  :deep(.el-form--large .el-form-item__label) {
    font-size: 14px;
  }
}
</style>