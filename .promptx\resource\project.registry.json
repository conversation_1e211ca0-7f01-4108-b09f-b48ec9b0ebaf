{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-27T12:01:59.585Z", "updatedAt": "2025-07-27T12:02:04.744Z", "resourceCount": 3}, "resources": [{"id": "fullstack-development-workflow", "source": "project", "protocol": "execution", "name": "Fullstack Development Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/fullstack-design-master/execution/fullstack-development-workflow.execution.md", "metadata": {"createdAt": "2025-07-27T12:02:02.176Z", "updatedAt": "2025-07-27T12:02:02.176Z", "scannedAt": "2025-07-27T12:02:02.176Z", "path": "role/fullstack-design-master/execution/fullstack-development-workflow.execution.md"}}, {"id": "fullstack-design-master", "source": "project", "protocol": "role", "name": "Fullstack Design Master 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/fullstack-design-master/fullstack-design-master.role.md", "metadata": {"createdAt": "2025-07-27T12:02:03.368Z", "updatedAt": "2025-07-27T12:02:03.368Z", "scannedAt": "2025-07-27T12:02:03.368Z", "path": "role/fullstack-design-master/fullstack-design-master.role.md"}}, {"id": "fullstack-thinking", "source": "project", "protocol": "thought", "name": "Fullstack Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/fullstack-design-master/thought/fullstack-thinking.thought.md", "metadata": {"createdAt": "2025-07-27T12:02:04.349Z", "updatedAt": "2025-07-27T12:02:04.349Z", "scannedAt": "2025-07-27T12:02:04.349Z", "path": "role/fullstack-design-master/thought/fullstack-thinking.thought.md"}}], "stats": {"totalResources": 3, "byProtocol": {"execution": 1, "role": 1, "thought": 1}, "bySource": {"project": 3}}}