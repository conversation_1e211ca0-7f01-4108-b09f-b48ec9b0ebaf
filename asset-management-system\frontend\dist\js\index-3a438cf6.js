import{_ as U}from"./_plugin-vue_export-helper-62491c14.js";/* empty css                        *//* empty css                     *//* empty css                  */import"./el-tooltip-4ed993c7.js";import{q as j,u as G,a as H,r as K,s as x,o as P,t as Q,v as X,c as _,d as B,f as d,g as t,w as e,T as M,x as Y,j as g,y as Z,z as $,A as tt,n as et,B as st,C as at,D as lt,F as l,G as v,H as nt,I as ot,i as o,J as ut,K as C,L as I,M as dt,N as it,O as rt,P as _t,Q as ft,R as S,S as mt,U as ct,V as pt,W as bt,X as f,Y as w,Z as xt,_ as gt,$ as vt,a0 as wt,a1 as yt,a2 as kt,a3 as Et,a4 as Bt,a5 as Mt,a6 as Ct,a7 as It,a8 as D,a9 as St,aa as Dt,ab as Nt,ac as Wt,ad as ht,ae as zt,af as Ft,ag as Tt}from"./index-2733c819.js";const Vt={class:"layout-container"},At={class:"sidebar-header"},Lt={class:"logo"},qt={class:"logo-text"},Jt={class:"main-container"},Ot={class:"main-header"},Rt={class:"header-left"},Ut={class:"header-right"},jt={class:"user-info"},Gt={class:"username"},Ht={class:"main-content"},Kt={class:"content-wrapper"},Pt={__name:"index",setup(Qt){const y=j(),k=G(),c=H(),i=K(!1),N=x(()=>{const n=y.path;return n==="/"?"/":n.startsWith("/assets")||n.startsWith("/operations")?n:n.startsWith("/location-tracking")?"/location-tracking":n.startsWith("/import-export")?"/import-export":n.startsWith("/inventory")||n.startsWith("/statistics")?n:n.startsWith("/settings/product-types")?"/settings/product-types":(n.startsWith("/users"),n)});x(()=>{var n;return((n=k.options.routes.find(s=>s.path==="/"))==null?void 0:n.children)||[]});const W=x(()=>y.matched.filter(s=>{var a;return(a=s.meta)==null?void 0:a.title}).map(s=>({path:s.path,title:s.meta.title}))),h=()=>{i.value=!i.value},z=()=>{document.fullscreenElement?document.exitFullscreen():document.documentElement.requestFullscreen()},F=async n=>{switch(n){case"profile":g.info("个人中心功能开发中...");break;case"settings":g.info("系统设置功能开发中...");break;case"logout":await T();break}},T=async()=>{try{await Z.confirm("确定要退出登录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await c.logout(),k.push("/login"),g.success("已退出登录")}catch{}};return P(()=>{const n=localStorage.getItem("sidebarCollapsed");n!==null&&(i.value=JSON.parse(n))}),Q(()=>i.value,n=>{localStorage.setItem("sidebarCollapsed",JSON.stringify(n))}),(n,s)=>{const a=$,u=Nt,m=Wt,V=tt,p=et,E=ht,A=st,L=at,q=zt,b=Ft,J=Tt,O=lt,R=X("router-view");return _(),B("div",Vt,[d("aside",{class:Y(["sidebar",{collapsed:i.value}])},[d("div",At,[d("div",Lt,[t(a,{size:"32",color:"#409eff"},{default:e(()=>[t(l(v))]),_:1}),t(M,{name:"fade"},{default:e(()=>[nt(d("span",qt,"资产管理",512),[[ot,!i.value]])]),_:1})])]),t(V,{"default-active":N.value,collapse:i.value,"unique-opened":!0,router:"",class:"sidebar-menu","background-color":"#001529","text-color":"#ffffff","active-text-color":"#409eff"},{default:e(()=>{var r;return[t(u,{index:"/",class:"menu-item"},{title:e(()=>s[0]||(s[0]=[o("首页",-1)])),default:e(()=>[t(a,null,{default:e(()=>[t(l(v))]),_:1})]),_:1}),t(m,{index:"assets",class:"menu-group"},{title:e(()=>[t(a,null,{default:e(()=>[t(l(v))]),_:1}),s[1]||(s[1]=d("span",null,"资产管理",-1))]),default:e(()=>[t(u,{index:"/assets",class:"sub-menu-item"},{title:e(()=>s[2]||(s[2]=[o("资产列表",-1)])),default:e(()=>[t(a,null,{default:e(()=>[t(l(ut))]),_:1})]),_:1}),t(u,{index:"/assets/create",class:"sub-menu-item"},{title:e(()=>s[3]||(s[3]=[o("新增资产",-1)])),default:e(()=>[t(a,null,{default:e(()=>[t(l(C))]),_:1})]),_:1}),t(u,{index:"/import-export",class:"sub-menu-item"},{title:e(()=>s[4]||(s[4]=[o("批量导入",-1)])),default:e(()=>[t(a,null,{default:e(()=>[t(l(I))]),_:1})]),_:1}),t(u,{index:"/assets/search",class:"sub-menu-item"},{title:e(()=>s[5]||(s[5]=[o("高级搜索",-1)])),default:e(()=>[t(a,null,{default:e(()=>[t(l(dt))]),_:1})]),_:1}),t(u,{index:"/settings/product-types",class:"sub-menu-item"},{title:e(()=>s[6]||(s[6]=[o("产品类型管理",-1)])),default:e(()=>[t(a,null,{default:e(()=>[t(l(it))]),_:1})]),_:1})]),_:1}),t(m,{index:"operations",class:"menu-group"},{title:e(()=>[t(a,null,{default:e(()=>[t(l(rt))]),_:1}),s[7]||(s[7]=d("span",null,"资产操作",-1))]),default:e(()=>[t(u,{index:"/operations/inbound",class:"sub-menu-item"},{title:e(()=>s[8]||(s[8]=[o("入库管理",-1)])),default:e(()=>[t(a,null,{default:e(()=>[t(l(_t))]),_:1})]),_:1}),t(u,{index:"/operations/install",class:"sub-menu-item"},{title:e(()=>s[9]||(s[9]=[o("安装管理",-1)])),default:e(()=>[t(a,null,{default:e(()=>[t(l(ft))]),_:1})]),_:1}),t(u,{index:"/location-tracking",class:"sub-menu-item"},{title:e(()=>s[10]||(s[10]=[o("位置变更",-1)])),default:e(()=>[t(a,null,{default:e(()=>[t(l(S))]),_:1})]),_:1}),t(u,{index:"/operations/outbound",class:"sub-menu-item"},{title:e(()=>s[11]||(s[11]=[o("出库管理",-1)])),default:e(()=>[t(a,null,{default:e(()=>[t(l(I))]),_:1})]),_:1})]),_:1}),t(m,{index:"statistics",class:"menu-group"},{title:e(()=>[t(a,null,{default:e(()=>[t(l(mt))]),_:1}),s[12]||(s[12]=d("span",null,"库存统计",-1))]),default:e(()=>[t(u,{index:"/inventory/statistics",class:"sub-menu-item"},{title:e(()=>s[13]||(s[13]=[o("统计概览",-1)])),default:e(()=>[t(a,null,{default:e(()=>[t(l(ct))]),_:1})]),_:1}),t(u,{index:"/statistics/by-type",class:"sub-menu-item"},{title:e(()=>s[14]||(s[14]=[o("分类统计",-1)])),default:e(()=>[t(a,null,{default:e(()=>[t(l(pt))]),_:1})]),_:1}),t(u,{index:"/statistics/by-location",class:"sub-menu-item"},{title:e(()=>s[15]||(s[15]=[o("位置统计",-1)])),default:e(()=>[t(a,null,{default:e(()=>[t(l(S))]),_:1})]),_:1}),t(u,{index:"/statistics",class:"sub-menu-item"},{title:e(()=>s[16]||(s[16]=[o("状态统计",-1)])),default:e(()=>[t(a,null,{default:e(()=>[t(l(bt))]),_:1})]),_:1})]),_:1}),((r=l(c).user)==null?void 0:r.role)==="ADMIN"?(_(),f(m,{key:0,index:"users",class:"menu-group"},{title:e(()=>[t(a,null,{default:e(()=>[t(l(w))]),_:1}),s[17]||(s[17]=d("span",null,"用户管理",-1))]),default:e(()=>[t(u,{index:"/users",class:"sub-menu-item"},{title:e(()=>s[18]||(s[18]=[o("用户列表",-1)])),default:e(()=>[t(a,null,{default:e(()=>[t(l(xt))]),_:1})]),_:1}),t(u,{index:"/users/create",class:"sub-menu-item"},{title:e(()=>s[19]||(s[19]=[o("创建用户",-1)])),default:e(()=>[t(a,null,{default:e(()=>[t(l(C))]),_:1})]),_:1}),t(u,{index:"/users/permissions",class:"sub-menu-item"},{title:e(()=>s[20]||(s[20]=[o("权限管理",-1)])),default:e(()=>[t(a,null,{default:e(()=>[t(l(gt))]),_:1})]),_:1})]),_:1})):vt("",!0)]}),_:1},8,["default-active","collapse"])],2),d("div",Jt,[d("header",Ot,[d("div",Rt,[t(p,{link:"",onClick:h,class:"collapse-btn"},{default:e(()=>[t(a,{size:"20"},{default:e(()=>[i.value?(_(),f(l(wt),{key:0})):(_(),f(l(yt),{key:1}))]),_:1})]),_:1}),t(A,{separator:"/",class:"breadcrumb"},{default:e(()=>[t(E,{to:{path:"/"}},{default:e(()=>s[21]||(s[21]=[o("首页",-1)])),_:1,__:[21]}),(_(!0),B(kt,null,Et(W.value,r=>(_(),f(E,{key:r.path,to:r.path},{default:e(()=>[o(D(r.title),1)]),_:2},1032,["to"]))),128))]),_:1})]),d("div",Ut,[t(L,{value:12,class:"header-badge"},{default:e(()=>[t(p,{link:"",class:"header-btn"},{default:e(()=>[t(a,{size:"18"},{default:e(()=>[t(l(Bt))]),_:1})]),_:1})]),_:1}),t(p,{link:"",onClick:z,class:"header-btn"},{default:e(()=>[t(a,{size:"18"},{default:e(()=>[t(l(Mt))]),_:1})]),_:1}),t(O,{onCommand:F,class:"user-dropdown"},{dropdown:e(()=>[t(J,null,{default:e(()=>[t(b,{command:"profile"},{default:e(()=>[t(a,null,{default:e(()=>[t(l(w))]),_:1}),s[22]||(s[22]=o(" 个人中心 ",-1))]),_:1,__:[22]}),t(b,{command:"settings"},{default:e(()=>[t(a,null,{default:e(()=>[t(l(Ct))]),_:1}),s[23]||(s[23]=o(" 系统设置 ",-1))]),_:1,__:[23]}),t(b,{divided:"",command:"logout"},{default:e(()=>[t(a,null,{default:e(()=>[t(l(It))]),_:1}),s[24]||(s[24]=o(" 退出登录 ",-1))]),_:1,__:[24]})]),_:1})]),default:e(()=>[d("div",jt,[t(q,{size:32,class:"user-avatar"},{default:e(()=>[t(a,null,{default:e(()=>[t(l(w))]),_:1})]),_:1}),d("span",Gt,D(l(c).userName),1),t(a,{class:"dropdown-icon"},{default:e(()=>[t(l(St))]),_:1})])]),_:1})])]),d("main",Ht,[d("div",Kt,[t(R,null,{default:e(({Component:r})=>[t(M,{name:"fade-transform",mode:"out-in"},{default:e(()=>[(_(),f(Dt(r)))]),_:2},1024)]),_:1})])])])])}}},se=U(Pt,[["__scopeId","data-v-5fd22761"]]);export{se as default};
