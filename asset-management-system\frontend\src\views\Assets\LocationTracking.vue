<template>
  <div class="location-tracking">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>位置跟踪管理</span>
          <el-button type="primary" @click="showLocationChangeDialog">
            <el-icon><LocationFilled /></el-icon>
            变更位置
          </el-button>
        </div>
      </template>
      
      <!-- 搜索筛选 -->
      <div class="search-section">
        <el-form :inline="true">
          <el-form-item label="关键词">
            <el-input
              v-model="searchForm.keyword"
              placeholder="资产编号、型号、位置"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              @change="handleDateRangeChange"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="loadLocationHistory">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 位置变更历史列表 -->
      <el-table :data="locationHistory" stripe v-loading="loading">
        <el-table-column prop="assetCode" label="资产编号" width="120" />
        <el-table-column prop="productModel" label="产品型号" width="150" />
        <el-table-column prop="fromLocation" label="原位置" width="150">
          <template #default="{ row }">
            <span v-if="row.fromLocation">{{ row.fromLocation }}</span>
            <el-tag v-else type="info" size="small">无</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="toLocation" label="新位置" width="150" />
        <el-table-column prop="moveReason" label="变更原因" min-width="200" show-overflow-tooltip />
        <el-table-column prop="operator" label="操作人" width="100" />
        <el-table-column prop="movedAt" label="变更时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.movedAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewAssetDetail(row.assetId)">
              查看资产
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>
    
    <!-- 位置变更对话框 -->
    <el-dialog
      v-model="locationChangeDialogVisible"
      title="变更资产位置"
      width="500px"
      @close="resetLocationChangeForm"
    >
      <el-form :model="locationChangeForm" :rules="locationChangeRules" ref="locationChangeFormRef" label-width="100px">
        <el-form-item label="选择资产" prop="assetId">
          <el-select
            v-model="locationChangeForm.assetId"
            placeholder="请选择资产"
            filterable
            @change="handleAssetChange"
            style="width: 100%"
          >
            <el-option
              v-for="asset in assetOptions"
              :key="asset.id"
              :label="`${asset.assetCode} - ${asset.productModel}`"
              :value="asset.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="当前位置">
          <el-input v-model="currentLocation" readonly placeholder="暂无位置信息" />
        </el-form-item>
        
        <el-form-item label="新位置" prop="toLocation">
          <el-input v-model="locationChangeForm.toLocation" placeholder="请输入新位置" />
        </el-form-item>
        
        <el-form-item label="变更原因">
          <el-input
            v-model="locationChangeForm.moveReason"
            type="textarea"
            :rows="3"
            placeholder="请输入变更原因（可选）"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="locationChangeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleLocationChange" :loading="submitLoading">
            确定变更
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getLocationHistory, changeAssetLocation } from '@/api/locationTracking'
import { getAssetList } from '@/api/asset'
import dayjs from 'dayjs'
import { LocationFilled, Search } from '@element-plus/icons-vue'

const router = useRouter()
const loading = ref(false)
const submitLoading = ref(false)
const locationChangeDialogVisible = ref(false)
const locationHistory = ref([])
const assetOptions = ref([])
const currentLocation = ref('')
const dateRange = ref([])
const locationChangeFormRef = ref()

const searchForm = reactive({
  keyword: '',
  startTime: null,
  endTime: null
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const locationChangeForm = reactive({
  assetId: null,
  toLocation: '',
  moveReason: ''
})

const locationChangeRules = {
  assetId: [
    { required: true, message: '请选择资产', trigger: 'change' }
  ],
  toLocation: [
    { required: true, message: '请输入新位置', trigger: 'blur' }
  ]
}

const formatDateTime = (dateTime) => {
  return dateTime ? dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss') : ''
}

const loadLocationHistory = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      keyword: searchForm.keyword,
      startTime: searchForm.startTime,
      endTime: searchForm.endTime
    }
    
    const { data } = await getLocationHistory(params)
    locationHistory.value = data.records
    pagination.total = data.total
  } catch (error) {
    console.error('加载位置历史失败:', error)
    ElMessage.error('加载位置历史失败')
  } finally {
    loading.value = false
  }
}

const loadAssetOptions = async () => {
  try {
    const { data } = await getAssetList({ size: 1000 })
    assetOptions.value = data.records
  } catch (error) {
    console.error('加载资产列表失败:', error)
  }
}

const handleDateRangeChange = (dates) => {
  if (dates && dates.length === 2) {
    searchForm.startTime = dates[0]
    searchForm.endTime = dates[1]
  } else {
    searchForm.startTime = null
    searchForm.endTime = null
  }
}

const resetSearch = () => {
  searchForm.keyword = ''
  searchForm.startTime = null
  searchForm.endTime = null
  dateRange.value = []
  pagination.page = 1
  loadLocationHistory()
}

const handlePageChange = (page) => {
  pagination.page = page
  loadLocationHistory()
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadLocationHistory()
}

const showLocationChangeDialog = () => {
  locationChangeDialogVisible.value = true
  loadAssetOptions()
}

const handleAssetChange = (assetId) => {
  const asset = assetOptions.value.find(item => item.id === assetId)
  currentLocation.value = asset ? (asset.currentLocation || '暂无位置信息') : ''
}

const resetLocationChangeForm = () => {
  locationChangeForm.assetId = null
  locationChangeForm.toLocation = ''
  locationChangeForm.moveReason = ''
  currentLocation.value = ''
  locationChangeFormRef.value?.resetFields()
}

const handleLocationChange = async () => {
  try {
    await locationChangeFormRef.value.validate()
    
    submitLoading.value = true
    await changeAssetLocation(locationChangeForm.assetId, {
      toLocation: locationChangeForm.toLocation,
      moveReason: locationChangeForm.moveReason
    })
    
    ElMessage.success('位置变更成功')
    locationChangeDialogVisible.value = false
    resetLocationChangeForm()
    loadLocationHistory()
  } catch (error) {
    console.error('位置变更失败:', error)
    if (error.response?.data?.message) {
      ElMessage.error(error.response.data.message)
    } else {
      ElMessage.error('位置变更失败')
    }
  } finally {
    submitLoading.value = false
  }
}

const viewAssetDetail = (assetId) => {
  router.push(`/assets/${assetId}`)
}

onMounted(() => {
  loadLocationHistory()
})
</script>

<style scoped>
.location-tracking {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>