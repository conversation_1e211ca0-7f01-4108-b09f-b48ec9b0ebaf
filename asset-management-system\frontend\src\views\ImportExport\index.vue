<template>
  <div class="import-export-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-title">
        <h2>导入导出</h2>
        <p>批量导入资产信息或导出现有数据</p>
      </div>
    </div>

    <el-row :gutter="24">
      <!-- 导入功能 -->
      <el-col :lg="12" :md="24">
        <el-card class="import-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon size="24" color="#409eff">
                <Upload />
              </el-icon>
              <span>批量导入</span>
            </div>
          </template>

          <div class="import-content">
            <div class="import-description">
              <p>支持Excel格式文件(.xlsx, .xls)批量导入资产信息</p>
              <ul class="feature-list">
                <li>支持数万条数据批量导入</li>
                <li>自动验证数据格式和完整性</li>
                <li>重复数据检测和提示</li>
                <li>详细的导入结果报告</li>
              </ul>
            </div>

            <!-- 步骤指南 -->
            <div class="steps-guide">
              <el-steps :active="currentStep" direction="vertical" size="small">
                <el-step title="下载模板" description="下载标准导入模板" />
                <el-step title="填写数据" description="按模板格式填写资产信息" />
                <el-step title="上传文件" description="选择并上传Excel文件" />
                <el-step title="查看结果" description="确认导入结果" />
              </el-steps>
            </div>

            <!-- 操作按钮 -->
            <div class="import-actions">
              <el-button 
                type="primary" 
                :icon="Download" 
                @click="handleDownloadTemplate"
                :loading="downloadingTemplate"
                class="action-btn"
              >
                下载模板
              </el-button>

              <el-upload
                ref="uploadRef"
                :before-upload="beforeUpload"
                :on-success="handleUploadSuccess"
                :on-error="handleUploadError"
                :show-file-list="false"
                :auto-upload="false"
                accept=".xlsx,.xls"
                class="upload-component"
              >
                <el-button 
                  type="success" 
                  :icon="FolderOpened"
                  class="action-btn"
                >
                  选择文件
                </el-button>
              </el-upload>

              <el-button 
                v-if="selectedFile"
                type="warning" 
                :icon="Upload" 
                @click="handleImport"
                :loading="importing"
                class="action-btn"
              >
                开始导入
              </el-button>
            </div>

            <!-- 文件信息 -->
            <div v-if="selectedFile" class="file-info">
              <el-alert
                :title="`已选择文件: ${selectedFile.name}`"
                type="info"
                show-icon
                :closable="false"
              >
                <template #default>
                  <p>文件大小: {{ formatFileSize(selectedFile.size) }}</p>
                  <p>文件类型: {{ selectedFile.type || '未知' }}</p>
                </template>
              </el-alert>
            </div>

            <!-- 导入进度 -->
            <div v-if="importing" class="import-progress">
              <el-progress 
                :percentage="importProgress" 
                :stroke-width="8"
                status="success"
              />
              <p class="progress-text">正在导入数据，请稍候...</p>
            </div>

            <!-- 导入结果 -->
            <div v-if="importResult" class="import-result">
              <el-alert
                :title="getResultTitle()"
                :type="getResultType()"
                show-icon
                :closable="false"
              >
                <template #default>
                  <div class="result-summary">
                    <p>总数据量: {{ importResult.totalCount }}</p>
                    <p>成功导入: {{ importResult.successCount }}</p>
                    <p>失败数量: {{ importResult.errorCount }}</p>
                  </div>
                  
                  <div v-if="importResult.errorCount > 0" class="error-details">
                    <el-button 
                      link 
                      @click="showErrorDetails = !showErrorDetails"
                      class="error-toggle"
                    >
                      {{ showErrorDetails ? '隐藏' : '查看' }}错误详情
                    </el-button>
                    
                    <div v-if="showErrorDetails" class="error-list">
                      <el-table 
                        :data="importResult.errorList" 
                        size="small"
                        max-height="300"
                      >
                        <el-table-column prop="row" label="行号" width="80" />
                        <el-table-column prop="error" label="错误信息" />
                      </el-table>
                    </div>
                  </div>
                </template>
              </el-alert>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 导出功能 -->
      <el-col :lg="12" :md="24">
        <el-card class="export-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon size="24" color="#67c23a">
                <Download />
              </el-icon>
              <span>数据导出</span>
            </div>
          </template>

          <div class="export-content">
            <div class="export-description">
              <p>将现有资产数据导出为Excel文件</p>
              <ul class="feature-list">
                <li>支持筛选条件导出</li>
                <li>包含完整的资产信息</li>
                <li>标准Excel格式</li>
                <li>支持大数据量导出</li>
              </ul>
            </div>

            <!-- 导出筛选 -->
            <div class="export-filters">
              <h4>导出筛选</h4>
              
              <el-form :model="exportForm" size="default" class="filter-form">
                <el-form-item label="关键词">
                  <el-input
                    v-model="exportForm.keyword"
                    placeholder="资产编号、型号、品牌..."
                    clearable
                  />
                </el-form-item>

                <el-form-item label="状态">
                  <el-select 
                    v-model="exportForm.status" 
                    placeholder="选择状态"
                    clearable
                  >
                    <el-option label="待处理" value="PENDING" />
                    <el-option label="已入库" value="RECEIVED" />
                    <el-option label="已安装" value="INSTALLED" />
                    <el-option label="已出库" value="OUTBOUND" />
                    <el-option label="已报废" value="SCRAPPED" />
                  </el-select>
                </el-form-item>

                <el-form-item label="产品类型">
                  <el-select 
                    v-model="exportForm.productType" 
                    placeholder="选择类型"
                    clearable
                  >
                    <el-option 
                      v-for="type in productTypes" 
                      :key="type" 
                      :label="type" 
                      :value="type" 
                    />
                  </el-select>
                </el-form-item>
              </el-form>
            </div>

            <!-- 导出按钮 -->
            <div class="export-actions">
              <el-button 
                type="success" 
                :icon="Download" 
                @click="handleExport"
                :loading="exporting"
                size="large"
                class="export-btn"
              >
                {{ exporting ? '导出中...' : '导出Excel' }}
              </el-button>
              
              <el-button 
                @click="handleResetExportForm"
                :icon="Refresh"
              >
                重置筛选
              </el-button>
            </div>

            <!-- 导出历史 -->
            <div class="export-history">
              <h4>最近导出</h4>
              <div class="history-list">
                <div 
                  v-for="record in exportHistory" 
                  :key="record.id"
                  class="history-item"
                >
                  <div class="history-info">
                    <p class="history-name">{{ record.name }}</p>
                    <span class="history-time">{{ record.time }}</span>
                  </div>
                  <el-button 
                    link 
                    size="small"
                    @click="downloadHistoryFile(record)"
                  >
                    重新下载
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { importAssets, exportAssets, downloadTemplate } from '@/api/importExport'
import { getProductTypes } from '@/api/asset'

// 响应式数据
const uploadRef = ref()
const currentStep = ref(0)
const selectedFile = ref(null)
const importing = ref(false)
const importProgress = ref(0)
const importResult = ref(null)
const showErrorDetails = ref(false)
const downloadingTemplate = ref(false)
const exporting = ref(false)
const productTypes = ref([])

// 导出表单
const exportForm = reactive({
  keyword: '',
  status: '',
  productType: ''
})

// 导出历史（模拟数据）
const exportHistory = ref([
  {
    id: 1,
    name: '全部资产数据_20240115.xlsx',
    time: '2024-01-15 14:30:00'
  },
  {
    id: 2,
    name: '笔记本电脑类型_20240114.xlsx',
    time: '2024-01-14 09:15:00'
  }
])

// 下载模板
const handleDownloadTemplate = async () => {
  downloadingTemplate.value = true
  try {
    const response = await downloadTemplate()
    downloadFile(response, '资产导入模板.xlsx')
    currentStep.value = 1
    ElMessage.success('模板下载成功')
  } catch (error) {
    ElMessage.error('模板下载失败')
  } finally {
    downloadingTemplate.value = false
  }
}

// 文件上传前的检查
const beforeUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                  file.type === 'application/vnd.ms-excel' ||
                  file.name.endsWith('.xlsx') ||
                  file.name.endsWith('.xls')
  
  if (!isExcel) {
    ElMessage.error('只能上传Excel文件!')
    return false
  }
  
  const isLt50M = file.size / 1024 / 1024 < 50
  if (!isLt50M) {
    ElMessage.error('上传文件大小不能超过50MB!')
    return false
  }
  
  selectedFile.value = file
  currentStep.value = 2
  importResult.value = null
  
  return false // 阻止自动上传
}

// 开始导入
const handleImport = async () => {
  if (!selectedFile.value) {
    ElMessage.error('请先选择文件')
    return
  }
  
  importing.value = true
  importProgress.value = 0
  
  // 模拟进度
  const progressInterval = setInterval(() => {
    if (importProgress.value < 90) {
      importProgress.value += Math.random() * 20
    }
  }, 500)
  
  try {
    const { data } = await importAssets(selectedFile.value)
    
    clearInterval(progressInterval)
    importProgress.value = 100
    
    setTimeout(() => {
      importResult.value = data
      currentStep.value = 3
      importing.value = false
      
      if (data.errorCount === 0) {
        ElMessage.success(`导入成功！共导入 ${data.successCount} 条数据`)
      } else {
        ElMessage.warning(`导入完成！成功 ${data.successCount} 条，失败 ${data.errorCount} 条`)
      }
    }, 1000)
    
  } catch (error) {
    clearInterval(progressInterval)
    importing.value = false
    ElMessage.error('导入失败: ' + (error.message || '未知错误'))
  }
}

// 导出数据
const handleExport = async () => {
  exporting.value = true
  try {
    const response = await exportAssets(exportForm)
    
    const filename = `资产数据_${new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')}.xlsx`
    downloadFile(response, filename)
    
    // 添加到导出历史
    exportHistory.value.unshift({
      id: Date.now(),
      name: filename,
      time: new Date().toLocaleString()
    })
    
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}

// 重置导出表单
const handleResetExportForm = () => {
  Object.assign(exportForm, {
    keyword: '',
    status: '',
    productType: ''
  })
}

// 下载历史文件
const downloadHistoryFile = (record) => {
  ElMessage.info('重新下载功能开发中...')
}

// 工具函数
const formatFileSize = (size) => {
  if (size < 1024) return size + ' B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB'
  return (size / 1024 / 1024).toFixed(1) + ' MB'
}

const downloadFile = (blob, filename) => {
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

const getResultTitle = () => {
  if (!importResult.value) return ''
  
  if (importResult.value.errorCount === 0) {
    return '导入成功！'
  } else if (importResult.value.successCount === 0) {
    return '导入失败！'
  } else {
    return '导入部分成功！'
  }
}

const getResultType = () => {
  if (!importResult.value) return 'info'
  
  if (importResult.value.errorCount === 0) {
    return 'success'
  } else if (importResult.value.successCount === 0) {
    return 'error'
  } else {
    return 'warning'
  }
}

// 加载产品类型
const loadProductTypes = async () => {
  try {
    const { data } = await getProductTypes()
    productTypes.value = data
  } catch (error) {
    console.error('加载产品类型失败:', error)
  }
}

// 初始化
onMounted(() => {
  loadProductTypes()
})
</script>

<style scoped lang="scss">
.import-export-page {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-title {
  h2 {
    margin: 0 0 4px 0;
    font-size: 24px;
    font-weight: 600;
    color: #333;
  }
  
  p {
    margin: 0;
    color: #666;
    font-size: 14px;
  }
}

.import-card,
.export-card {
  height: 100%;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.import-content,
.export-content {
  padding: 16px 0;
}

.import-description,
.export-description {
  margin-bottom: 24px;
  
  p {
    margin: 0 0 12px 0;
    color: #666;
    font-size: 14px;
  }
}

.feature-list {
  margin: 0;
  padding-left: 20px;
  
  li {
    color: #888;
    font-size: 13px;
    line-height: 1.6;
    margin-bottom: 4px;
  }
}

.steps-guide {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.import-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 24px;
}

.action-btn {
  height: 40px;
  font-weight: 500;
}

.upload-component {
  display: inline-block;
}

.file-info {
  margin-bottom: 24px;
}

.import-progress {
  margin-bottom: 24px;
  
  .progress-text {
    text-align: center;
    margin-top: 8px;
    color: #666;
    font-size: 14px;
  }
}

.import-result {
  .result-summary {
    margin-bottom: 12px;
    
    p {
      margin: 4px 0;
      font-size: 14px;
    }
  }
  
  .error-toggle {
    padding: 0;
    margin-bottom: 12px;
  }
  
  .error-list {
    max-height: 300px;
    overflow-y: auto;
  }
}

.export-filters {
  margin-bottom: 24px;
  
  h4 {
    margin: 0 0 16px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
  }
}

.filter-form {
  .el-form-item {
    margin-bottom: 16px;
  }
  
  .el-form-item__label {
    font-size: 13px;
    color: #666;
  }
}

.export-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 32px;
}

.export-btn {
  flex: 1;
  height: 44px;
  font-size: 16px;
  font-weight: 600;
}

.export-history {
  h4 {
    margin: 0 0 16px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
  }
}

.history-list {
  max-height: 200px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
  
  &:last-child {
    border-bottom: none;
  }
}

.history-info {
  flex: 1;
  
  .history-name {
    margin: 0 0 4px 0;
    font-size: 14px;
    color: #333;
  }
  
  .history-time {
    font-size: 12px;
    color: #999;
  }
}

// Element Plus 样式覆盖
:deep(.el-card__body) {
  padding: 24px;
}

:deep(.el-steps--vertical) {
  .el-step__head {
    width: 24px;
    height: 24px;
  }
  
  .el-step__title {
    font-size: 13px;
    line-height: 24px;
  }
  
  .el-step__description {
    font-size: 12px;
    color: #999;
  }
}

:deep(.el-progress-bar) {
  padding-right: 0;
}

:deep(.el-alert__content) {
  padding-right: 0;
}

// 响应式设计
@media (max-width: 768px) {
  .import-actions {
    flex-direction: column;
    
    .action-btn {
      width: 100%;
    }
  }
  
  .export-actions {
    flex-direction: column;
  }
  
  .history-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>