package com.asset.controller;

import com.asset.dto.ApiResponse;
import com.asset.entity.Asset;
import com.asset.service.ExcelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/import-export")
public class ImportExportController extends BaseController {

    @Autowired
    private ExcelService excelService;

    /**
     * 导入Excel文件
     */
    @PostMapping("/import")
    public ApiResponse<Map<String, Object>> importAssets(@RequestParam("file") MultipartFile file) {
        try {
            Map<String, Object> result = excelService.importAssets(file);
            return ApiResponse.success("导入完成", result);
        } catch (Exception e) {
            log.error("导入失败: {}", e.getMessage());
            return ApiResponse.error("导入失败: " + e.getMessage());
        }
    }

    /**
     * 导出Excel文件
     */
    @GetMapping("/export")
    public void exportAssets(
            HttpServletResponse response,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Asset.AssetStatus status,
            @RequestParam(required = false) String productType) {
        
        try {
            excelService.exportAssets(response, keyword, status, productType);
        } catch (Exception e) {
            log.error("导出失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 下载导入模板
     */
    @GetMapping("/template")
    public void downloadTemplate(HttpServletResponse response) {
        try {
            excelService.downloadTemplate(response);
        } catch (Exception e) {
            log.error("下载模板失败: {}", e.getMessage());
            throw e;
        }
    }
}