package com.asset.dto;

import com.asset.entity.Asset;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class AssetResponse {
    
    private Long id;
    private String assetCode;
    private String serialNumber;
    private String productModel;
    private String productType;
    private String brand;
    private String specification;
    private LocalDate purchaseDate;
    private BigDecimal purchasePrice;
    private String supplier;
    private Integer warrantyPeriod;
    private Asset.AssetStatus status;
    private String statusDescription;
    private String currentLocation;
    private String receiver;
    private LocalDateTime receivedAt;
    private String installer;
    private LocalDateTime installedAt;
    private String installLocation;
    private String outboundReason;
    private String outboundOperator;
    private LocalDateTime outboundAt;
    private String notes;
    private Long userId;
    private String username;
    private String userRealName;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    public static AssetResponse fromAsset(Asset asset) {
        AssetResponse response = new AssetResponse();
        response.setId(asset.getId());
        response.setAssetCode(asset.getAssetCode());
        response.setSerialNumber(asset.getSerialNumber());
        response.setProductModel(asset.getProductModel());
        response.setProductType(asset.getProductType());
        response.setBrand(asset.getBrand());
        response.setSpecification(asset.getSpecification());
        response.setPurchaseDate(asset.getPurchaseDate());
        response.setPurchasePrice(asset.getPurchasePrice());
        response.setSupplier(asset.getSupplier());
        response.setWarrantyPeriod(asset.getWarrantyPeriod());
        response.setStatus(asset.getStatus());
        response.setStatusDescription(asset.getStatus() != null ? asset.getStatus().getDescription() : null);
        response.setCurrentLocation(asset.getCurrentLocation());
        response.setReceiver(asset.getReceiver());
        response.setReceivedAt(asset.getReceivedAt());
        response.setInstaller(asset.getInstaller());
        response.setInstalledAt(asset.getInstalledAt());
        response.setInstallLocation(asset.getInstallLocation());
        response.setOutboundReason(asset.getOutboundReason());
        response.setOutboundOperator(asset.getOutboundOperator());
        response.setOutboundAt(asset.getOutboundAt());
        response.setNotes(asset.getNotes());
        response.setUserId(asset.getUserId());
        response.setCreatedAt(asset.getCreatedAt());
        response.setUpdatedAt(asset.getUpdatedAt());
        
        // 如果包含用户信息
        if (asset.getUser() != null) {
            response.setUsername(asset.getUser().getUsername());
            response.setUserRealName(asset.getUser().getRealName());
        }
        
        return response;
    }
}