@echo off
chcp 65001 >nul

:: 临时设置环境变量
set "PATH=D:\cursor\apache-maven-3.9.9\bin;D:\cursor\nodejs;%PATH%"

echo 🔧 临时环境变量已设置
echo Maven路径: D:\cursor\apache-maven-3.9.9\bin
echo Node.js路径: D:\cursor\nodejs
echo.

:: 验证工具是否可用
echo 🔍 验证工具可用性:
mvn -version >nul 2>&1 && echo ✅ Maven: 可用 || echo ❌ Maven: 不可用
npm --version >nul 2>&1 && echo ✅ NPM: 可用 || echo ❌ NPM: 不可用
node --version >nul 2>&1 && echo ✅ Node.js: 可用 || echo ❌ Node.js: 不可用
echo.

:: 运行Python构建脚本
echo 🚀 启动构建脚本...
python build.py

pause
