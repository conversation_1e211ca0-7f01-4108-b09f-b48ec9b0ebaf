# 需求文档

## 介绍

资产管理系统是一个用于跟踪和管理企业资产的Web应用程序。系统基于Java+Spring Boot+Vue.js+MySQL8技术栈开发，主要功能包括用户认证、资产信息管理、出入库操作、位置跟踪和历史记录查询。系统支持多用户使用，每个用户只能查看和管理自己录入的资产信息，管理员可以查看所有用户的资产信息。系统旨在帮助用户实时了解资产状态、位置和库存情况。

## 需求

### 需求 1 - 用户认证和权限管理

**用户故事：** 作为系统用户，我希望能够通过账号密码登录系统，以便安全地访问和管理我的资产信息。

#### 验收标准

1. 当用户访问系统时，系统应显示登录页面要求用户输入账号和密码
2. 当用户输入正确的账号密码时，系统应验证用户身份并跳转到主页面
3. 当用户输入错误的账号密码时，系统应显示错误提示信息
4. 当普通用户登录时，系统应只显示该用户录入的资产信息
5. 当管理员用户登录时，系统应能够查看所有用户的资产信息
6. 当用户会话超时时，系统应自动跳转到登录页面
7. 当用户点击退出时，系统应清除会话信息并跳转到登录页面

### 需求 2 - 用户管理

**用户故事：** 作为管理员，我希望能够管理系统用户账号，以便控制系统的访问权限。

#### 验收标准

1. 当管理员创建新用户时，系统应允许设置用户名、密码和角色权限
2. 当管理员查看用户列表时，系统应显示所有用户的基本信息和状态
3. 当管理员重置用户密码时，系统应生成新密码并通知用户
4. 当管理员禁用用户时，系统应阻止该用户登录系统
5. 当管理员查看用户资产时，系统应能够切换查看不同用户的资产信息

### 需求 3 - 资产基础信息管理

**用户故事：** 作为资产管理员，我希望能够新增和管理资产的基础信息，以便在货物到达前预先录入资产数据。

#### 验收标准

1. 当用户点击"新增资产"按钮时，系统应显示资产信息录入表单
2. 当用户填写资产信息时，系统应允许用户灵活录入可用信息，无强制必填字段
3. 当用户保存资产信息时，系统应将数据存储到数据库并关联当前登录用户
4. 当用户查看资产列表时，系统应只显示该用户录入的资产信息
5. 当管理员查看资产列表时，系统应能够查看所有用户的资产信息
6. 当用户编辑资产信息时，系统应只允许编辑自己录入的资产
7. 当管理员编辑资产信息时，系统应允许编辑任何用户的资产
8. 当用户随时更新资产信息时，系统应允许用户自由修改任何字段

### 需求 4 - 资产导入导出功能

**用户故事：** 作为资产管理员，我希望能够批量导入和导出资产信息，以便快速录入大量资产数据和备份数据。

#### 验收标准

1. 当用户选择导入文件时，系统应支持Excel格式文件上传
2. 当用户上传文件时，系统应验证文件格式和数据完整性
3. 当导入数据时，系统应检查资产编号的唯一性并关联当前登录用户
4. 当导入完成时，系统应显示导入结果统计（成功、失败、重复记录数）
5. 当导入失败时，系统应提供详细的错误信息和失败记录列表
6. 当用户导出资产时，系统应只导出该用户录入的资产信息
7. 当管理员导出资产时，系统应能够选择导出所有用户或特定用户的资产信息
8. 当导出数据时，系统应支持Excel格式并包含完整的资产信息和历史记录

### 需求 5 - 资产入库管理

**用户故事：** 作为仓库管理员，我希望能够记录资产的入库信息，以便跟踪资产的接收和存储状态。

#### 验收标准

1. 当资产到货时，系统应允许录入签收人、入库时间信息
2. 当资产入库时，系统应自动记录当前时间作为入库时间
3. 当资产入库完成时，系统应更新资产状态为"已入库"
4. 当查看入库记录时，系统应显示所有入库操作的历史记录
5. 当资产重复入库时，系统应提示警告信息

### 需求 6 - 资产安装管理

**用户故事：** 作为技术人员，我希望能够记录资产的安装信息，以便跟踪资产的部署位置和安装状态。

#### 验收标准

1. 当资产安装时，系统应允许录入安装位置、安装日期、安装人员信息
2. 当安装完成时，系统应更新资产状态为"已安装"
3. 当查看安装信息时，系统应显示资产的当前安装位置和安装人员
4. 当安装位置重复时，系统应允许多个资产安装在同一位置
5. 当资产安装时，系统应自动记录安装操作到历史记录中

### 需求 7 - 资产位置变更跟踪

**用户故事：** 作为资产管理员，我希望能够更改资产位置并查看历史位置记录，以便跟踪资产的流转情况。

#### 验收标准

1. 当用户更改资产位置时，系统应记录变更前的位置信息到历史记录
2. 当位置变更时，系统应记录变更时间、变更人员和变更原因
3. 当查看位置历史时，系统应按时间倒序显示所有位置变更记录
4. 当资产未安装时，系统应禁止进行位置变更操作
5. 当位置变更完成时，系统应更新资产的当前位置信息

### 需求 8 - 资产出库管理

**用户故事：** 作为仓库管理员，我希望能够记录资产的出库信息，以便跟踪资产的流出状态。

#### 验收标准

1. 当资产出库时，系统应允许录入出库原因、出库人员、出库时间
2. 当资产出库时，系统应更新资产状态为"已出库"
3. 当资产出库后，系统应禁止对该资产进行安装和位置变更操作
4. 当查看出库记录时，系统应显示所有出库操作的历史记录
5. 当已出库资产重新入库时，系统应允许重新激活该资产

### 需求 9 - 库存统计查询

**用户故事：** 作为资产管理员，我希望能够查看资产的库存统计信息，以便了解当前资产的整体状况。

#### 验收标准

1. 当查看库存统计时，系统应显示总资产数量、已入库数量、已安装数量、已出库数量
2. 当按产品类型统计时，系统应显示每种类型资产的数量分布
3. 当按安装位置统计时，系统应显示每个位置的资产数量
4. 当查看库存详情时，系统应支持按状态、类型、位置等条件筛选
5. 当导出统计报表时，系统应支持Excel格式导出

### 需求 8 - 资产信息查询

**用户故事：** 作为用户，我希望能够快速查询和搜索资产信息，以便快速定位所需资产。

#### 验收标准

1. 当用户输入搜索关键词时，系统应支持按资产编号、序列号、产品型号进行模糊搜索
2. 当用户使用高级搜索时，系统应支持按产品类型、安装位置、状态等条件组合查询
3. 当查看资产详情时，系统应显示资产的完整信息和操作历史
4. 当搜索结果为空时，系统应显示友好的提示信息
5. 当搜索结果过多时，系统应支持分页显示

### 需求 9 - 备注信息管理

**用户故事：** 作为用户，我希望能够为资产添加和编辑备注信息，以便记录资产的特殊情况和注意事项。

#### 验收标准

1. 当用户添加备注时，系统应记录备注内容、添加时间和添加人员
2. 当用户编辑备注时，系统应保留备注的修改历史
3. 当查看备注历史时，系统应按时间倒序显示所有备注记录
4. 当备注内容过长时，系统应支持富文本编辑和格式化显示
5. 当删除备注时，系统应要求确认操作并记录删除日志