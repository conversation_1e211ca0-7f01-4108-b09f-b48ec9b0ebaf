import{_ as H}from"./_plugin-vue_export-helper-62491c14.js";/* empty css                *//* empty css                 *//* empty css               *//* empty css                       *//* empty css               *//* empty css                  *//* empty css                     */import{q as Z,u as G,s as J,r as U,b as h,o as K,c as p,d as c,f as E,g as e,w as l,j as k,gm as Q,E as W,a8 as g,F as C,gn as N,i as _,x as F,$ as I,a2 as X,a3 as Y,k as ee,z as ae,l as le,aD as se,aM as oe,go as te,aC as re,n as ne,p as de,aL as ue}from"./index-2733c819.js";import{a as me,c as ie,e as pe,f as ce,h as ge}from"./user-993d4a0e.js";const _e={class:"user-form-container"},fe={class:"page-header"},we={class:"page-title"},ve={key:0,class:"form-tip"},ye={key:0,class:"form-tip"},be={class:"form-tip info"},Ve={__name:"Form",setup(he){const v=Z(),R=G(),u=J(()=>!!v.params.id),y=U(),a=h({username:"",realName:"",email:"",phone:"",department:"",role:"USER",status:"ACTIVE",password:"",confirmPassword:""}),B=[{label:"普通用户",value:"USER"},{label:"管理员",value:"ADMIN"}],r=h({loading:!1,message:"",type:""}),n=h({loading:!1,message:"",type:""}),b=U(!1),P={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:50,message:"用户名长度在3-50个字符",trigger:"blur"},{pattern:/^[a-zA-Z0-9_]+$/,message:"用户名只能包含字母、数字和下划线",trigger:"blur"}],realName:[{required:!0,message:"请输入真实姓名",trigger:"blur"},{max:50,message:"姓名长度不能超过50个字符",trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}],phone:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}],role:[{required:!0,message:"请选择角色",trigger:"change"}],password:[...u.value?[]:[{required:!0,message:"请输入密码",trigger:"blur"}],{min:6,message:"密码长度至少6位",trigger:"blur"}],confirmPassword:[{validator:(t,s,w)=>{a.password&&s!==a.password?w(new Error("两次输入密码不一致")):w()},trigger:"blur"}]},A=async()=>{if(!(!a.username||u.value)){r.loading=!0,r.message="";try{(await ie(a.username)).data?(r.message="用户名可用",r.type="success"):(r.message="用户名已存在",r.type="error")}catch{r.message="检查失败",r.type="error"}finally{r.loading=!1}}},D=async()=>{if(a.email){n.loading=!0,n.message="";try{(await pe(a.email)).data?(n.message="邮箱可用",n.type="success"):(n.message="邮箱已存在",n.type="error")}catch{n.message="检查失败",n.type="error"}finally{n.loading=!1}}},S=async()=>{if(u.value)try{const t=await me(v.params.id);Object.assign(a,t.data),a.password="",a.confirmPassword=""}catch{k.error("加载用户数据失败"),f()}},q=async()=>{try{await y.value.validate(),b.value=!0;const t={...a};delete t.confirmPassword,u.value&&!t.password&&delete t.password,u.value?(await ce(v.params.id,t),k.success("用户更新成功")):(await ge(t),k.success("用户创建成功")),f()}catch(t){console.error("提交失败:",t)}finally{b.value=!1}},M=()=>{y.value.resetFields(),r.message="",n.message=""},f=()=>{R.push("/users")};return K(()=>{S()}),(t,s)=>{const w=Q,i=ee,x=ae,d=le,m=se,T=ue,z=oe,L=te,O=re,V=ne,$=de,j=W;return p(),c("div",_e,[E("div",fe,[e(w,{onBack:f},{content:l(()=>[E("span",we,g(u.value?"编辑用户":"创建用户"),1)]),_:1})]),e(j,null,{default:l(()=>[e($,{ref_key:"formRef",ref:y,model:a,rules:P,"label-width":"120px",class:"user-form"},{default:l(()=>[e(O,{gutter:24},{default:l(()=>[e(m,{span:12},{default:l(()=>[e(d,{label:"用户名",prop:"username"},{default:l(()=>[e(i,{modelValue:a.username,"onUpdate:modelValue":s[0]||(s[0]=o=>a.username=o),placeholder:"请输入用户名",disabled:u.value,maxlength:"50","show-word-limit":"",onBlur:A},null,8,["modelValue","disabled"]),r.loading?(p(),c("div",ve,[e(x,{class:"loading"},{default:l(()=>[e(C(N))]),_:1}),s[9]||(s[9]=_(" 正在检查用户名... ",-1))])):r.message?(p(),c("div",{key:1,class:F(["form-tip",r.type])},g(r.message),3)):I("",!0)]),_:1})]),_:1}),e(m,{span:12},{default:l(()=>[e(d,{label:"真实姓名",prop:"realName"},{default:l(()=>[e(i,{modelValue:a.realName,"onUpdate:modelValue":s[1]||(s[1]=o=>a.realName=o),placeholder:"请输入真实姓名",maxlength:"50","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1}),e(m,{span:12},{default:l(()=>[e(d,{label:"邮箱",prop:"email"},{default:l(()=>[e(i,{modelValue:a.email,"onUpdate:modelValue":s[2]||(s[2]=o=>a.email=o),placeholder:"请输入邮箱",type:"email",maxlength:"100",onBlur:D},null,8,["modelValue"]),n.loading?(p(),c("div",ye,[e(x,{class:"loading"},{default:l(()=>[e(C(N))]),_:1}),s[10]||(s[10]=_(" 正在检查邮箱... ",-1))])):n.message?(p(),c("div",{key:1,class:F(["form-tip",n.type])},g(n.message),3)):I("",!0)]),_:1})]),_:1}),e(m,{span:12},{default:l(()=>[e(d,{label:"电话",prop:"phone"},{default:l(()=>[e(i,{modelValue:a.phone,"onUpdate:modelValue":s[3]||(s[3]=o=>a.phone=o),placeholder:"请输入电话号码",maxlength:"20"},null,8,["modelValue"])]),_:1})]),_:1}),e(m,{span:12},{default:l(()=>[e(d,{label:"部门",prop:"department"},{default:l(()=>[e(i,{modelValue:a.department,"onUpdate:modelValue":s[4]||(s[4]=o=>a.department=o),placeholder:"请输入部门",maxlength:"100"},null,8,["modelValue"])]),_:1})]),_:1}),e(m,{span:12},{default:l(()=>[e(d,{label:"角色",prop:"role"},{default:l(()=>[e(z,{modelValue:a.role,"onUpdate:modelValue":s[5]||(s[5]=o=>a.role=o),placeholder:"请选择角色",style:{width:"100%"}},{default:l(()=>[(p(),c(X,null,Y(B,o=>e(T,{key:o.value,label:o.label,value:o.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(m,{span:12},{default:l(()=>[e(d,{label:"状态",prop:"status"},{default:l(()=>[e(L,{modelValue:a.status,"onUpdate:modelValue":s[6]||(s[6]=o=>a.status=o),"active-text":"正常","inactive-text":"禁用","active-value":"ACTIVE","inactive-value":"INACTIVE"},null,8,["modelValue"])]),_:1})]),_:1}),e(m,{span:12},{default:l(()=>[e(d,{label:"密码",prop:"password"},{default:l(()=>[e(i,{modelValue:a.password,"onUpdate:modelValue":s[7]||(s[7]=o=>a.password=o),type:"password",placeholder:"请输入密码","show-password":"",maxlength:"50"},null,8,["modelValue"]),E("div",be,g(u.value?"留空表示不修改密码":"密码长度至少6位"),1)]),_:1})]),_:1}),e(m,{span:12},{default:l(()=>[e(d,{label:"确认密码",prop:"confirmPassword"},{default:l(()=>[e(i,{modelValue:a.confirmPassword,"onUpdate:modelValue":s[8]||(s[8]=o=>a.confirmPassword=o),type:"password",placeholder:"请再次输入密码","show-password":"",maxlength:"50"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(d,null,{default:l(()=>[e(V,{type:"primary",onClick:q,loading:b.value},{default:l(()=>[_(g(u.value?"更新用户":"创建用户"),1)]),_:1},8,["loading"]),e(V,{onClick:M},{default:l(()=>s[11]||(s[11]=[_("重置",-1)])),_:1,__:[11]}),e(V,{onClick:f},{default:l(()=>s[12]||(s[12]=[_("取消",-1)])),_:1,__:[12]})]),_:1})]),_:1},8,["model"])]),_:1})])}}},Pe=H(Ve,[["__scopeId","data-v-c5d6d36a"]]);export{Pe as default};
