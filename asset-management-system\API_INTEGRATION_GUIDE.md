# API集成指南

本文档说明了如何将前端页面与真实的后端API进行集成。

## 🎯 已清理的模拟数据

### 前端页面清理状态

#### ✅ Dashboard页面 (`/views/Dashboard/index.vue`)
- **清理内容**：统计数据、最近活动、图表数据
- **需要集成的API**：
  - `GET /api/dashboard/stats` - 获取统计概览
  - `GET /api/dashboard/activities` - 获取最近活动
  - `GET /api/dashboard/charts` - 获取图表数据

#### ✅ 入库管理页面 (`/views/Operations/Inbound.vue`)
- **清理内容**：模拟的入库数据和API调用
- **需要集成的API**：
  - `GET /api/operations/inbound` - 获取待入库资产列表
  - `POST /api/operations/inbound/confirm` - 确认入库操作

#### ✅ 安装管理页面 (`/views/Operations/Install.vue`)
- **清理内容**：模拟的安装数据和API调用
- **需要集成的API**：
  - `GET /api/operations/install` - 获取可安装资产列表
  - `POST /api/operations/install/confirm` - 确认安装操作

#### ✅ 出库管理页面 (`/views/Operations/Outbound.vue`)
- **清理内容**：模拟的出库数据和API调用
- **需要集成的API**：
  - `GET /api/operations/outbound` - 获取可出库资产列表
  - `POST /api/operations/outbound/confirm` - 确认出库操作

#### ✅ 分类统计页面 (`/views/Statistics/ByType.vue`)
- **清理内容**：硬编码的统计数据
- **需要集成的API**：
  - `GET /api/statistics/by-type` - 获取按类型统计数据

## 🔧 API集成步骤

### 1. 创建新的API方法

在对应的API文件中添加新的方法：

```javascript
// src/api/operations.js (新建文件)
import request from './request'

// 入库管理API
export const getInboundAssets = (params) => {
  return request({
    url: '/api/operations/inbound',
    method: 'get',
    params
  })
}

export const confirmInbound = (data) => {
  return request({
    url: '/api/operations/inbound/confirm',
    method: 'post',
    data
  })
}

// 安装管理API
export const getInstallAssets = (params) => {
  return request({
    url: '/api/operations/install',
    method: 'get',
    params
  })
}

export const confirmInstall = (data) => {
  return request({
    url: '/api/operations/install/confirm',
    method: 'post',
    data
  })
}

// 出库管理API
export const getOutboundAssets = (params) => {
  return request({
    url: '/api/operations/outbound',
    method: 'get',
    params
  })
}

export const confirmOutbound = (data) => {
  return request({
    url: '/api/operations/outbound/confirm',
    method: 'post',
    data
  })
}
```

### 2. 更新页面中的TODO注释

在每个页面的loadData方法中，取消注释并导入对应的API方法：

```javascript
// 示例：入库管理页面
import { getInboundAssets, confirmInbound } from '@/api/operations'

const loadData = async () => {
  loading.value = true
  try {
    const response = await getInboundAssets({
      ...searchForm,
      current: pagination.current,
      pageSize: pagination.pageSize
    })
    tableData.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('加载数据失败: ' + error.message)
  } finally {
    loading.value = false
  }
}
```

### 3. 后端API开发

需要在后端创建对应的Controller和Service方法：

#### 操作管理Controller
```java
@RestController
@RequestMapping("/api/operations")
public class OperationsController {
    
    @GetMapping("/inbound")
    public ApiResponse<PageResponse<AssetResponse>> getInboundAssets(
        @RequestParam(defaultValue = "1") int current,
        @RequestParam(defaultValue = "20") int pageSize,
        // 其他查询参数
    ) {
        // 实现逻辑
    }
    
    @PostMapping("/inbound/confirm")
    public ApiResponse<Void> confirmInbound(@RequestBody InboundRequest request) {
        // 实现逻辑
    }
    
    // 其他操作方法...
}
```

## 📋 待集成的API列表

### Dashboard相关
- [ ] `GET /api/dashboard/stats` - 统计概览
- [ ] `GET /api/dashboard/activities` - 最近活动
- [ ] `GET /api/dashboard/charts` - 图表数据

### 操作管理相关
- [ ] `GET /api/operations/inbound` - 入库列表
- [ ] `POST /api/operations/inbound/confirm` - 确认入库
- [ ] `GET /api/operations/install` - 安装列表
- [ ] `POST /api/operations/install/confirm` - 确认安装
- [ ] `GET /api/operations/outbound` - 出库列表
- [ ] `POST /api/operations/outbound/confirm` - 确认出库

### 统计分析相关
- [ ] `GET /api/statistics/by-type` - 按类型统计
- [ ] `GET /api/statistics/by-location` - 按位置统计
- [ ] `GET /api/statistics/by-status` - 按状态统计

## 🎯 集成优先级

1. **高优先级**：Dashboard统计数据
2. **中优先级**：操作管理功能
3. **低优先级**：详细统计分析

## 📝 注意事项

1. **错误处理**：确保所有API调用都有适当的错误处理
2. **加载状态**：保持loading状态的正确显示
3. **数据验证**：前端表单验证要与后端验证保持一致
4. **权限控制**：确保API调用符合用户权限要求
5. **响应式设计**：确保在1920×1080和2K分辨率下的完美显示

## 🔄 测试建议

1. **单元测试**：为新的API方法编写测试
2. **集成测试**：测试前后端数据交互
3. **用户体验测试**：在不同分辨率下测试界面效果
4. **性能测试**：确保API响应时间符合要求

---

**注意**：所有TODO注释都已在代码中标记，按照本指南逐步替换即可完成真实API的集成。
