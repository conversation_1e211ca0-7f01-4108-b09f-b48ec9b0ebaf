package com.asset.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

@Data
public class AssetOperationRequest {
    
    // 通用字段
    @NotBlank(message = "操作人员不能为空")
    private String operator;
    
    private String reason;
    
    private LocalDateTime operationTime;
    
    // 入库相关
    private String receiver;
    private LocalDateTime receivedAt;
    
    // 安装相关
    private String installer;
    private String installLocation;
    private LocalDateTime installedAt;
    
    // 位置变更相关
    private String fromLocation;
    private String toLocation;
    private String moveReason;
    
    // 出库相关
    private String outboundReason;
    private String outboundOperator;
    private LocalDateTime outboundAt;
}