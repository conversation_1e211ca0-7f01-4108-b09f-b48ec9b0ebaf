import{_ as j}from"./_plugin-vue_export-helper-62491c14.js";/* empty css                   *//* empty css                *//* empty css                 *//* empty css                       *//* empty css                     *//* empty css               *//* empty css                  *//* empty css               */import{q,u as G,r as f,s as H,b as W,o as X,c,d as P,f as r,g as l,w as a,F as C,a8 as k,H as J,X as U,j as g,n as K,E as Q,aS as Z,i as _,aW as $,a2 as N,a3 as T,a6 as ee,k as le,l as ae,aD as te,aC as oe,aL as se,aM as re,z as ue,aU as de,p as ne}from"./index-2733c819.js";import{e as ie,a as pe,c as me,u as ce,f as fe}from"./asset-f5b5b286.js";const _e={class:"asset-form"},be={class:"page-header"},ge={class:"header-left"},ve={class:"header-title"},Ve={class:"form-section"},ye={class:"product-type-input"},we={class:"form-section"},Ee={class:"form-section"},De={class:"form-section"},xe={class:"form-actions"},he={__name:"Form",setup(Pe){const v=q(),V=G(),y=f(),w=f(!1),E=f(!1),D=f([]),M=f([]),p=H(()=>v.name==="AssetEdit"),o=W({assetCode:"",serialNumber:"",productModel:"",productType:"",brand:"",supplier:"",specification:"",purchaseDate:"",purchasePrice:null,warrantyPeriod:null,status:"PENDING",currentLocation:"",notes:""}),L={productModel:[{required:!0,message:"请输入产品型号",trigger:"blur"}],productType:[{required:!0,message:"请选择产品类型",trigger:"change"}],purchasePrice:[{type:"number",min:0,message:"采购价格不能为负数",trigger:"blur"}],warrantyPeriod:[{type:"number",min:0,message:"保修期不能为负数",trigger:"blur"}]},R=async()=>{if(p.value){w.value=!0;try{const{data:s}=await ie(v.params.id);Object.keys(o).forEach(e=>{s[e]!==void 0&&s[e]!==null&&(o[e]=s[e])})}catch{g.error("加载资产详情失败"),x()}finally{w.value=!1}}},I=async()=>{try{const[s,e]=await Promise.all([pe(),me()]),n=s.data||[];D.value=n.map(u=>typeof u=="string"?{id:u,name:u}:u),M.value=e.data||[]}catch(s){console.error("加载选项失败:",s),D.value=[{id:1,name:"计算机设备"},{id:2,name:"网络设备"},{id:3,name:"办公设备"},{id:4,name:"移动设备"},{id:5,name:"服务器设备"}]}},B=async()=>{if(y.value)try{await y.value.validate(),E.value=!0;const s={};Object.keys(o).forEach(e=>{const n=o[e];n!=null&&n!==""&&(s[e]=n)}),p.value?(await ce(v.params.id,s),g.success("资产更新成功")):(await fe(s),g.success("资产创建成功")),V.push("/assets")}catch(s){if(s.errors)return;g.error(p.value?"资产更新失败":"资产创建失败")}finally{E.value=!1}},x=()=>{V.back()},Y=()=>{const s=V.resolve("/settings/product-types");window.open(s.href,"_blank")};return X(()=>{I(),p.value&&R()}),(s,e)=>{const n=K,u=le,d=ae,i=te,b=oe,m=se,h=re,A=ue,F=de,O=ne,S=Q,z=Z;return c(),P("div",_e,[r("div",be,[r("div",ge,[l(n,{onClick:x,icon:C($),class:"back-btn"},{default:a(()=>e[13]||(e[13]=[_(" 返回 ",-1)])),_:1,__:[13]},8,["icon"]),r("div",ve,[r("h2",null,k(p.value?"编辑资产":"新增资产"),1),r("p",null,k(p.value?"修改资产信息":"添加新的资产信息"),1)])])]),J((c(),U(S,{class:"form-card"},{default:a(()=>[l(O,{ref_key:"formRef",ref:y,model:o,rules:L,"label-width":"120px",size:"large",class:"asset-form-content"},{default:a(()=>[r("div",Ve,[e[14]||(e[14]=r("h3",{class:"section-title"},"基本信息",-1)),l(b,{gutter:24},{default:a(()=>[l(i,{xl:8,lg:12,md:24},{default:a(()=>[l(d,{label:"资产编号",prop:"assetCode"},{default:a(()=>[l(u,{modelValue:o.assetCode,"onUpdate:modelValue":e[0]||(e[0]=t=>o.assetCode=t),placeholder:"留空自动生成",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),l(i,{xl:8,lg:12,md:24},{default:a(()=>[l(d,{label:"序列号",prop:"serialNumber"},{default:a(()=>[l(u,{modelValue:o.serialNumber,"onUpdate:modelValue":e[1]||(e[1]=t=>o.serialNumber=t),placeholder:"请输入序列号",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),l(i,{xl:8,lg:24,md:24},{default:a(()=>[l(d,{label:"产品型号",prop:"productModel"},{default:a(()=>[l(u,{modelValue:o.productModel,"onUpdate:modelValue":e[2]||(e[2]=t=>o.productModel=t),placeholder:"请输入产品型号",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(b,{gutter:24},{default:a(()=>[l(i,{xl:8,lg:12,md:24},{default:a(()=>[l(d,{label:"产品类型",prop:"productType"},{default:a(()=>[r("div",ye,[l(h,{modelValue:o.productType,"onUpdate:modelValue":e[3]||(e[3]=t=>o.productType=t),placeholder:"选择或输入产品类型",filterable:"","allow-create":"","default-first-option":"",class:"type-select"},{default:a(()=>[(c(!0),P(N,null,T(D.value,t=>(c(),U(m,{key:t.id||t,label:t.name||t,value:t.name||t},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),l(n,{type:"primary",link:"",onClick:Y,class:"manage-btn",title:"管理产品类型"},{default:a(()=>[l(A,null,{default:a(()=>[l(C(ee))]),_:1})]),_:1})])]),_:1})]),_:1}),l(i,{xl:8,lg:12,md:24},{default:a(()=>[l(d,{label:"品牌",prop:"brand"},{default:a(()=>[l(u,{modelValue:o.brand,"onUpdate:modelValue":e[4]||(e[4]=t=>o.brand=t),placeholder:"请输入品牌",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),l(i,{xl:8,lg:24,md:24},{default:a(()=>[l(d,{label:"供应商",prop:"supplier"},{default:a(()=>[l(u,{modelValue:o.supplier,"onUpdate:modelValue":e[5]||(e[5]=t=>o.supplier=t),placeholder:"请输入供应商",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(d,{label:"规格说明",prop:"specification"},{default:a(()=>[l(u,{modelValue:o.specification,"onUpdate:modelValue":e[6]||(e[6]=t=>o.specification=t),type:"textarea",rows:3,placeholder:"请输入规格说明",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1})]),r("div",we,[e[17]||(e[17]=r("h3",{class:"section-title"},"采购信息",-1)),l(b,{gutter:24},{default:a(()=>[l(i,{xl:8,lg:12,md:24},{default:a(()=>[l(d,{label:"采购日期",prop:"purchaseDate"},{default:a(()=>[l(F,{modelValue:o.purchaseDate,"onUpdate:modelValue":e[7]||(e[7]=t=>o.purchaseDate=t),type:"date",placeholder:"选择采购日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",class:"full-width"},null,8,["modelValue"])]),_:1})]),_:1}),l(i,{xl:8,lg:12,md:24},{default:a(()=>[l(d,{label:"采购价格",prop:"purchasePrice"},{default:a(()=>[l(u,{modelValue:o.purchasePrice,"onUpdate:modelValue":e[8]||(e[8]=t=>o.purchasePrice=t),modelModifiers:{number:!0},placeholder:"请输入采购价格",clearable:""},{prepend:a(()=>e[15]||(e[15]=[_("¥",-1)])),_:1},8,["modelValue"])]),_:1})]),_:1}),l(i,{xl:8,lg:24,md:24},{default:a(()=>[l(d,{label:"保修期",prop:"warrantyPeriod"},{default:a(()=>[l(u,{modelValue:o.warrantyPeriod,"onUpdate:modelValue":e[9]||(e[9]=t=>o.warrantyPeriod=t),modelModifiers:{number:!0},placeholder:"请输入保修期",clearable:""},{append:a(()=>e[16]||(e[16]=[_("个月",-1)])),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(b,{gutter:24},{default:a(()=>[l(i,{xl:8,lg:12,md:24},{default:a(()=>[l(d,{label:"资产状态",prop:"status"},{default:a(()=>[l(h,{modelValue:o.status,"onUpdate:modelValue":e[10]||(e[10]=t=>o.status=t),placeholder:"选择资产状态",class:"full-width"},{default:a(()=>[l(m,{label:"待处理",value:"PENDING"}),l(m,{label:"已入库",value:"RECEIVED"}),l(m,{label:"已安装",value:"INSTALLED"}),l(m,{label:"已出库",value:"OUTBOUND"}),l(m,{label:"已报废",value:"SCRAPPED"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),r("div",Ee,[e[18]||(e[18]=r("h3",{class:"section-title"},"位置信息",-1)),l(d,{label:"当前位置",prop:"currentLocation"},{default:a(()=>[l(h,{modelValue:o.currentLocation,"onUpdate:modelValue":e[11]||(e[11]=t=>o.currentLocation=t),placeholder:"选择或输入当前位置",filterable:"","allow-create":"","default-first-option":"",class:"full-width"},{default:a(()=>[(c(!0),P(N,null,T(M.value,t=>(c(),U(m,{key:t,label:t,value:t},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),r("div",De,[e[19]||(e[19]=r("h3",{class:"section-title"},"备注信息",-1)),l(d,{label:"备注",prop:"notes"},{default:a(()=>[l(u,{modelValue:o.notes,"onUpdate:modelValue":e[12]||(e[12]=t=>o.notes=t),type:"textarea",rows:4,placeholder:"请输入备注信息",maxlength:"1000","show-word-limit":""},null,8,["modelValue"])]),_:1})]),r("div",xe,[l(n,{onClick:x,size:"large"},{default:a(()=>e[20]||(e[20]=[_(" 取消 ",-1)])),_:1,__:[20]}),l(n,{type:"primary",onClick:B,loading:E.value,size:"large"},{default:a(()=>[_(k(p.value?"更新":"创建"),1)]),_:1},8,["loading"])])]),_:1},8,["model"])]),_:1})),[[z,w.value]])])}}},Ae=j(he,[["__scopeId","data-v-3fff0625"]]);export{Ae as default};
