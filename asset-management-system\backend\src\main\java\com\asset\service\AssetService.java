package com.asset.service;

import com.asset.dto.AssetRequest;
import com.asset.dto.AssetResponse;
import com.asset.dto.AssetSearchRequest;
import com.asset.dto.PageResponse;
import com.asset.entity.Asset;
import com.asset.entity.AssetHistory;
import com.asset.entity.User;
import com.asset.exception.BusinessException;
import com.asset.exception.ResourceNotFoundException;
import com.asset.repository.AssetRepository;
import com.asset.repository.AssetHistoryRepository;
import com.asset.utils.AssetCodeGenerator;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AssetService {

    @Autowired
    private AssetRepository assetRepository;

    @Autowired
    private AssetHistoryRepository assetHistoryRepository;

    @Autowired
    private AuthService authService;

    @Autowired
    private AssetCodeGenerator assetCodeGenerator;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 分页查询资产列表
     */
    public PageResponse<AssetResponse> getAssetList(int page, int size, String keyword, 
                                                   Asset.AssetStatus status, String productType) {
        
        Long currentUserId = authService.getCurrentUserId();
        boolean isAdmin = authService.isCurrentUserAdmin();
        
        Page<Asset> pageParam = new Page<>(page, size);
        IPage<Asset> result;
        
        if (isAdmin) {
            // 管理员可以查看所有资产
            result = assetRepository.findAllAssetsWithPagination(
                pageParam, keyword, status, productType, null);
        } else {
            // 普通用户只能查看自己的资产
            result = assetRepository.findUserAssetsWithPagination(
                pageParam, currentUserId, keyword, status, productType);
        }
        
        List<AssetResponse> records = result.getRecords().stream()
                .map(AssetResponse::fromAsset)
                .collect(Collectors.toList());
        
        return PageResponse.of(records, result.getTotal(), result.getSize(), result.getCurrent());
    }

    /**
     * 根据ID获取资产详情
     */
    public AssetResponse getAssetById(Long id) {
        Asset asset = assetRepository.selectById(id);
        if (asset == null) {
            throw new ResourceNotFoundException("资产不存在");
        }
        
        // 检查权限
        checkAssetPermission(asset);
        
        return AssetResponse.fromAsset(asset);
    }

    /**
     * 创建资产
     */
    @Transactional
    public AssetResponse createAsset(AssetRequest request) {
        Long currentUserId = authService.getCurrentUserId();
        String currentUsername = authService.getCurrentUser().getUsername();
        
        // 验证资产编号唯一性
        if (StringUtils.hasText(request.getAssetCode()) && 
            assetRepository.countByAssetCodeAndIdNot(request.getAssetCode(), 0L) > 0) {
            throw new BusinessException("资产编号已存在");
        }
        
        // 验证序列号唯一性
        if (StringUtils.hasText(request.getSerialNumber()) && 
            assetRepository.countBySerialNumberAndIdNot(request.getSerialNumber(), 0L) > 0) {
            throw new BusinessException("序列号已存在");
        }
        
        Asset asset = new Asset();
        BeanUtils.copyProperties(request, asset);
        
        // 设置资产编号
        if (!StringUtils.hasText(asset.getAssetCode())) {
            asset.setAssetCode(assetCodeGenerator.generateAssetCode());
        }
        
        // 设置默认状态
        if (asset.getStatus() == null) {
            asset.setStatus(Asset.AssetStatus.PENDING);
        }
        
        asset.setUserId(currentUserId);
        asset.setCreatedAt(LocalDateTime.now());
        asset.setUpdatedAt(LocalDateTime.now());
        
        assetRepository.insert(asset);
        
        // 记录历史
        recordAssetHistory(asset.getId(), AssetHistory.OperationType.CREATE, 
                          null, assetToJson(asset), currentUsername, "创建资产");
        
        log.info("用户 {} 创建资产: {}", currentUsername, asset.getAssetCode());
        
        return AssetResponse.fromAsset(asset);
    }

    /**
     * 更新资产
     */
    @Transactional
    public AssetResponse updateAsset(Long id, AssetRequest request) {
        Asset existingAsset = assetRepository.selectById(id);
        if (existingAsset == null) {
            throw new ResourceNotFoundException("资产不存在");
        }
        
        // 检查权限
        checkAssetPermission(existingAsset);
        
        String currentUsername = authService.getCurrentUser().getUsername();
        String oldValue = assetToJson(existingAsset);
        
        // 验证资产编号唯一性
        if (StringUtils.hasText(request.getAssetCode()) && 
            !request.getAssetCode().equals(existingAsset.getAssetCode()) &&
            assetRepository.countByAssetCodeAndIdNot(request.getAssetCode(), id) > 0) {
            throw new BusinessException("资产编号已存在");
        }
        
        // 验证序列号唯一性
        if (StringUtils.hasText(request.getSerialNumber()) && 
            !request.getSerialNumber().equals(existingAsset.getSerialNumber()) &&
            assetRepository.countBySerialNumberAndIdNot(request.getSerialNumber(), id) > 0) {
            throw new BusinessException("序列号已存在");
        }
        
        // 更新字段
        BeanUtils.copyProperties(request, existingAsset, "id", "userId", "createdAt");
        existingAsset.setUpdatedAt(LocalDateTime.now());
        
        assetRepository.updateById(existingAsset);
        
        // 记录历史
        recordAssetHistory(id, AssetHistory.OperationType.UPDATE, 
                          oldValue, assetToJson(existingAsset), currentUsername, "更新资产信息");
        
        log.info("用户 {} 更新资产: {}", currentUsername, existingAsset.getAssetCode());
        
        return AssetResponse.fromAsset(existingAsset);
    }

    /**
     * 删除资产
     */
    @Transactional
    public void deleteAsset(Long id) {
        Asset asset = assetRepository.selectById(id);
        if (asset == null) {
            throw new ResourceNotFoundException("资产不存在");
        }
        
        // 检查权限
        checkAssetPermission(asset);
        
        String currentUsername = authService.getCurrentUser().getUsername();
        
        // 逻辑删除
        assetRepository.deleteById(id);
        
        // 记录历史
        recordAssetHistory(id, AssetHistory.OperationType.UPDATE, 
                          assetToJson(asset), null, currentUsername, "删除资产");
        
        log.info("用户 {} 删除资产: {}", currentUsername, asset.getAssetCode());
    }

    /**
     * 批量删除资产
     */
    @Transactional
    public void batchDeleteAssets(List<Long> ids) {
        String currentUsername = authService.getCurrentUser().getUsername();
        
        for (Long id : ids) {
            Asset asset = assetRepository.selectById(id);
            if (asset != null) {
                checkAssetPermission(asset);
                assetRepository.deleteById(id);
                recordAssetHistory(id, AssetHistory.OperationType.UPDATE, 
                                  assetToJson(asset), null, currentUsername, "批量删除资产");
            }
        }
        
        log.info("用户 {} 批量删除 {} 个资产", currentUsername, ids.size());
    }

    /**
     * 获取资产统计信息
     */
    public Object getAssetStatistics() {
        Long currentUserId = authService.isCurrentUserAdmin() ? null : authService.getCurrentUserId();
        
        // 按状态统计
        List<Object> statusStats = assetRepository.countAssetsByStatus(currentUserId);
        
        // 按类型统计
        List<Object> typeStats = assetRepository.countAssetsByProductType(currentUserId);
        
        // 按位置统计
        List<Object> locationStats = assetRepository.countAssetsByLocation(currentUserId);
        
        java.util.Map<String, Object> result = new java.util.HashMap<>();
        result.put("statusStatistics", statusStats);
        result.put("typeStatistics", typeStats);
        result.put("locationStatistics", locationStats);
        return result;
    }

    /**
     * 获取产品类型列表
     */
    public List<String> getProductTypes() {
        Long currentUserId = authService.isCurrentUserAdmin() ? null : authService.getCurrentUserId();
        return assetRepository.findDistinctProductTypes(currentUserId);
    }

    /**
     * 获取位置列表
     */
    public List<String> getLocations() {
        Long currentUserId = authService.isCurrentUserAdmin() ? null : authService.getCurrentUserId();
        return assetRepository.findDistinctLocations(currentUserId);
    }

    /**
     * 检查资产权限
     */
    private void checkAssetPermission(Asset asset) {
        if (!authService.isCurrentUserAdmin() && 
            !asset.getUserId().equals(authService.getCurrentUserId())) {
            throw new BusinessException(403, "无权限访问该资产");
        }
    }

    /**
     * 记录资产历史
     */
    private void recordAssetHistory(Long assetId, AssetHistory.OperationType operationType,
                                   String oldValue, String newValue, String operator, String description) {
        try {
            AssetHistory history = new AssetHistory();
            history.setAssetId(assetId);
            history.setOperationType(operationType);
            history.setOldValue(oldValue);
            history.setNewValue(newValue);
            history.setOperator(operator);
            history.setDescription(description);
            history.setCreatedAt(LocalDateTime.now());
            
            assetHistoryRepository.insert(history);
        } catch (Exception e) {
            log.error("记录资产历史失败: {}", e.getMessage());
        }
    }

    /**
     * 高级搜索资产
     */
    public PageResponse<AssetResponse> advancedSearchAssets(AssetSearchRequest request) {
        Long currentUserId = authService.getCurrentUserId();
        boolean isAdmin = authService.isCurrentUserAdmin();

        // 构建查询条件
        QueryWrapper<Asset> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("deleted", 0);

        // 非管理员只能搜索自己的资产
        if (!isAdmin) {
            queryWrapper.eq("user_id", currentUserId);
        }

        // 添加搜索条件
        if (request.getAssetCode() != null && !request.getAssetCode().trim().isEmpty()) {
            queryWrapper.like("asset_code", request.getAssetCode().trim());
        }
        if (request.getSerialNumber() != null && !request.getSerialNumber().trim().isEmpty()) {
            queryWrapper.like("serial_number", request.getSerialNumber().trim());
        }
        if (request.getProductModel() != null && !request.getProductModel().trim().isEmpty()) {
            queryWrapper.like("product_model", request.getProductModel().trim());
        }
        if (request.getProductType() != null && !request.getProductType().trim().isEmpty()) {
            queryWrapper.eq("product_type", request.getProductType().trim());
        }
        if (request.getBrand() != null && !request.getBrand().trim().isEmpty()) {
            queryWrapper.like("brand", request.getBrand().trim());
        }
        if (request.getStatus() != null) {
            queryWrapper.eq("status", request.getStatus());
        }
        if (request.getCurrentLocation() != null && !request.getCurrentLocation().trim().isEmpty()) {
            queryWrapper.like("current_location", request.getCurrentLocation().trim());
        }
        if (request.getSupplier() != null && !request.getSupplier().trim().isEmpty()) {
            queryWrapper.like("supplier", request.getSupplier().trim());
        }
        if (request.getReceiver() != null && !request.getReceiver().trim().isEmpty()) {
            queryWrapper.like("receiver", request.getReceiver().trim());
        }
        if (request.getInstaller() != null && !request.getInstaller().trim().isEmpty()) {
            queryWrapper.like("installer", request.getInstaller().trim());
        }
        if (request.getMinPrice() != null) {
            queryWrapper.ge("purchase_price", request.getMinPrice());
        }
        if (request.getMaxPrice() != null) {
            queryWrapper.le("purchase_price", request.getMaxPrice());
        }
        if (request.getPurchaseDateStart() != null) {
            queryWrapper.ge("purchase_date", request.getPurchaseDateStart());
        }
        if (request.getPurchaseDateEnd() != null) {
            queryWrapper.le("purchase_date", request.getPurchaseDateEnd());
        }
        if (request.getCreatedAtStart() != null) {
            queryWrapper.ge("created_at", request.getCreatedAtStart());
        }
        if (request.getCreatedAtEnd() != null) {
            queryWrapper.le("created_at", request.getCreatedAtEnd());
        }

        // 排序
        String sortBy = request.getSortBy();
        String sortOrder = request.getSortOrder();
        if (sortBy != null && !sortBy.trim().isEmpty()) {
            if ("asc".equalsIgnoreCase(sortOrder)) {
                queryWrapper.orderByAsc(convertSortField(sortBy));
            } else {
                queryWrapper.orderByDesc(convertSortField(sortBy));
            }
        } else {
            queryWrapper.orderByDesc("created_at");
        }

        Page<Asset> pageParam = new Page<>(request.getPage(), request.getSize());
        IPage<Asset> result = assetRepository.selectPage(pageParam, queryWrapper);

        List<AssetResponse> records = result.getRecords().stream()
                .map(AssetResponse::fromAsset)
                .collect(Collectors.toList());

        return PageResponse.of(records, result.getTotal(), result.getSize(), result.getCurrent());
    }

    /**
     * 转换排序字段名
     */
    private String convertSortField(String sortBy) {
        switch (sortBy) {
            case "assetCode":
                return "asset_code";
            case "purchaseDate":
                return "purchase_date";
            case "purchasePrice":
                return "purchase_price";
            case "createdAt":
                return "created_at";
            default:
                return "created_at";
        }
    }

    /**
     * 获取搜索选项数据
     */
    public java.util.Map<String, Object> getSearchOptions() {
        Long currentUserId = authService.isCurrentUserAdmin() ? null : authService.getCurrentUserId();
        
        java.util.Map<String, Object> options = new java.util.HashMap<>();
        options.put("productTypes", assetRepository.findDistinctProductTypes(currentUserId));
        options.put("brands", assetRepository.findDistinctBrands(currentUserId));
        options.put("locations", assetRepository.findDistinctLocations(currentUserId));
        options.put("suppliers", assetRepository.findDistinctSuppliers(currentUserId));
        options.put("installers", assetRepository.findDistinctInstallers(currentUserId));
        options.put("receivers", assetRepository.findDistinctReceivers(currentUserId));
        options.put("statuses", Asset.AssetStatus.values());
        
        return options;
    }

    /**
     * 获取库存统计详情
     */
    public java.util.Map<String, Object> getInventoryStatistics() {
        Long currentUserId = authService.isCurrentUserAdmin() ? null : authService.getCurrentUserId();
        
        java.util.Map<String, Object> result = new java.util.HashMap<>();
        
        // 按位置统计
        result.put("locationStatistics", assetRepository.getInventoryStatisticsByLocation(currentUserId));
        
        // 按类型统计
        result.put("typeStatistics", assetRepository.getInventoryStatisticsByType(currentUserId));
        
        // 资产价值统计
        result.put("valueStatistics", assetRepository.getAssetValueStatistics(currentUserId));
        
        // 状态统计
        result.put("statusStatistics", assetRepository.countAssetsByStatus(currentUserId));
        
        return result;
    }

    /**
     * 获取即将过保的资产
     */
    public List<AssetResponse> getAssetsNearWarrantyExpiry() {
        Long currentUserId = authService.isCurrentUserAdmin() ? null : authService.getCurrentUserId();
        
        List<Asset> assets = assetRepository.findAssetsNearWarrantyExpiry(currentUserId);
        
        return assets.stream()
                .map(AssetResponse::fromAsset)
                .collect(Collectors.toList());
    }

    /**
     * 根据时间范围统计资产变化
     */
    public List<Object> getAssetTrendsByDateRange(java.time.LocalDateTime startTime, 
                                                 java.time.LocalDateTime endTime) {
        Long currentUserId = authService.isCurrentUserAdmin() ? null : authService.getCurrentUserId();
        return assetRepository.countAssetsByDateRange(startTime, endTime, currentUserId);
    }

    /**
     * 将资产对象转换为JSON字符串
     */
    private String assetToJson(Asset asset) {
        try {
            return objectMapper.writeValueAsString(asset);
        } catch (Exception e) {
            return asset.toString();
        }
    }
}