package com.asset.dto;

import com.asset.entity.Asset;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class AssetSearchRequest {
    
    private String assetCode;
    private String serialNumber;
    private String productModel;
    private String productType;
    private String brand;
    private Asset.AssetStatus status;
    private String currentLocation;
    private String supplier;
    private String receiver;
    private String installer;
    
    private BigDecimal minPrice;
    private BigDecimal maxPrice;
    
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate purchaseDateStart;
    
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate purchaseDateEnd;
    
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime createdAtStart;
    
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime createdAtEnd;
    
    private String username;
    private String userRealName;
    
    private String sortBy = "createdAt";
    private String sortOrder = "desc";
    
    private int page = 1;
    private int size = 10;
}