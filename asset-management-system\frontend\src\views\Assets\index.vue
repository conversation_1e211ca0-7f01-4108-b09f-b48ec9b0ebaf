<template>
  <div class="assets-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-title">
        <h2>资产管理</h2>
        <p>管理和跟踪企业资产信息</p>
      </div>
      <div class="header-actions">
        <el-button 
          type="primary" 
          :icon="Plus" 
          @click="handleCreate"
          class="create-btn"
        >
          新增资产
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="资产编号、型号、品牌..."
            clearable
            @clear="handleSearch"
            @keyup.enter="handleSearch"
            class="search-input"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="状态">
          <el-select 
            v-model="searchForm.status" 
            placeholder="选择状态"
            clearable
            @change="handleSearch"
          >
            <el-option label="待处理" value="PENDING" />
            <el-option label="已入库" value="RECEIVED" />
            <el-option label="已安装" value="INSTALLED" />
            <el-option label="已出库" value="OUTBOUND" />
            <el-option label="已报废" value="SCRAPPED" />
          </el-select>
        </el-form-item>

        <el-form-item label="产品类型">
          <el-select 
            v-model="searchForm.productType" 
            placeholder="选择类型"
            clearable
            @change="handleSearch"
          >
            <el-option 
              v-for="type in productTypes" 
              :key="type" 
              :label="type" 
              :value="type" 
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">
            搜索
          </el-button>
          <el-button @click="handleReset" :icon="Refresh">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 批量操作栏 -->
    <div v-if="selectedAssets.length > 0" class="batch-actions">
      <el-alert
        :title="`已选择 ${selectedAssets.length} 项`"
        type="info"
        show-icon
        :closable="false"
      >
        <template #default>
          <div class="batch-buttons">
            <el-button 
              type="danger" 
              size="small" 
              @click="handleBatchDelete"
              :icon="Delete"
            >
              批量删除
            </el-button>
          </div>
        </template>
      </el-alert>
    </div>

    <!-- 资产列表 -->
    <el-card class="table-card" shadow="never">
      <el-table
        v-loading="loading"
        :data="assetList"
        @selection-change="handleSelectionChange"
        class="asset-table"
        empty-text="暂无数据"
      >
        <el-table-column type="selection" width="50" />
        
        <el-table-column prop="assetCode" label="资产编号" min-width="120">
          <template #default="{ row }">
            <el-link type="primary" @click="handleView(row)">
              {{ row.assetCode }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column prop="productModel" label="产品型号" min-width="150" />
        
        <el-table-column prop="productType" label="产品类型" min-width="120" />
        
        <el-table-column prop="brand" label="品牌" min-width="100" />
        
        <el-table-column prop="serialNumber" label="序列号" min-width="150" />
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="currentLocation" label="当前位置" min-width="120" />
        
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button 
                type="primary" 
                size="small" 
                @click="handleView(row)"
                :icon="View"
              >
                查看
              </el-button>
              <el-button 
                type="success" 
                size="small" 
                @click="handleEdit(row)"
                :icon="Edit"
              >
                编辑
              </el-button>
              <el-button 
                type="danger" 
                size="small" 
                @click="handleDelete(row)"
                :icon="Delete"
              >
                删除
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { getAssetList, deleteAsset, batchDeleteAssets, getProductTypes } from '@/api/asset'
import dayjs from 'dayjs'
import { Plus, Search, Refresh, Edit, Delete, View, Download } from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const assetList = ref([])
const selectedAssets = ref([])
const productTypes = ref([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: '',
  productType: ''
})

// 分页参数
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 计算属性
const statusMap = {
  PENDING: { text: '待处理', type: 'info' },
  RECEIVED: { text: '已入库', type: 'success' },
  INSTALLED: { text: '已安装', type: 'primary' },
  OUTBOUND: { text: '已出库', type: 'warning' },
  SCRAPPED: { text: '已报废', type: 'danger' }
}

const getStatusText = (status) => statusMap[status]?.text || status
const getStatusType = (status) => statusMap[status]?.type || 'info'

// 格式化日期时间
const formatDateTime = (dateTime) => {
  return dateTime ? dayjs(dateTime).format('YYYY-MM-DD HH:mm') : '-'
}

// 加载资产列表
const loadAssetList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    
    const { data } = await getAssetList(params)
    
    assetList.value = data.records
    pagination.total = data.total
    
  } catch (error) {
    ElMessage.error('加载资产列表失败')
  } finally {
    loading.value = false
  }
}

// 加载产品类型
const loadProductTypes = async () => {
  try {
    const { data } = await getProductTypes()
    productTypes.value = data
  } catch (error) {
    console.error('加载产品类型失败:', error)
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadAssetList()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: '',
    productType: ''
  })
  handleSearch()
}

// 页面大小变化
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  loadAssetList()
}

// 当前页变化
const handleCurrentChange = (current) => {
  pagination.current = current
  loadAssetList()
}

// 选择变化
const handleSelectionChange = (selection) => {
  selectedAssets.value = selection
}

// 新增资产
const handleCreate = () => {
  router.push('/assets/create')
}

// 查看资产
const handleView = (row) => {
  router.push(`/assets/detail/${row.id}`)
}

// 编辑资产
const handleEdit = (row) => {
  router.push(`/assets/edit/${row.id}`)
}

// 删除资产
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除资产 "${row.assetCode}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteAsset(row.id)
    ElMessage.success('删除成功')
    loadAssetList()
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedAssets.value.length} 个资产吗？`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const ids = selectedAssets.value.map(item => item.id)
    await batchDeleteAssets(ids)
    
    ElMessage.success('批量删除成功')
    selectedAssets.value = []
    loadAssetList()
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

// 初始化
onMounted(() => {
  loadAssetList()
  loadProductTypes()
})
</script>

<style scoped lang="scss">
.assets-page {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-title {
  h2 {
    margin: 0 0 4px 0;
    font-size: 24px;
    font-weight: 600;
    color: #333;
  }
  
  p {
    margin: 0;
    color: #666;
    font-size: 14px;
  }
}

.create-btn {
  height: 40px;
  padding: 0 20px;
  font-weight: 500;
}

.search-card {
  margin-bottom: 16px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-form {
  .el-form-item {
    margin-bottom: 0;
  }
}

.search-input {
  width: 240px;
}

.batch-actions {
  margin-bottom: 16px;
  
  .batch-buttons {
    margin-top: 8px;
  }
}

.table-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.asset-table {
  .el-table__header {
    th {
      background: #fafafa;
      color: #333;
      font-weight: 600;
    }
  }
  
  .el-table__row {
    &:hover {
      background: #f5f7fa;
    }
  }
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

// Element Plus 样式覆盖
:deep(.el-card__body) {
  padding: 24px;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 24px;
}

:deep(.el-button-group) {
  .el-button {
    padding: 8px 12px;
    
    &:first-child {
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
    }
    
    &:last-child {
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .search-form {
    .el-form-item {
      width: 100%;
      margin-right: 0;
      margin-bottom: 16px;
    }
    
    .search-input {
      width: 100%;
    }
  }
  
  .asset-table {
    .el-table__column {
      &:nth-child(n+6) {
        display: none;
      }
    }
  }
}
</style>