# 🏢 资产管理系统 - 构建和启动指南

## 📋 概述

本项目提供了多种构建和启动方式，适合不同的开发环境和使用场景。

## 🛠️ 系统要求

### 必需依赖
- **Java 8+** - 后端运行环境
- **Maven 3.6+** - 后端构建工具
- **Node.js 16+** - 前端运行环境
- **NPM 8+** - 前端包管理器
- **MySQL 8.0+** - 数据库

### 可选依赖
- **Python 3.7+** - 使用Python脚本时需要

## 🚀 启动方式

### 方式一：Python完整构建脚本 (推荐)

**功能最全面的启动方式，包含完整的错误检查和状态监控**

```bash
# 完整构建和启动
python build.py

# 显示帮助信息
python build.py help

# 显示版本信息
python build.py version
```

**特性：**
- ✅ 自动检测环境依赖
- ✅ 完整的编译流程
- ✅ 健康检查和状态监控
- ✅ 优雅的进程管理
- ✅ 彩色日志输出
- ✅ 错误诊断和提示

### 方式二：Python快速启动脚本

**适合已编译项目的快速启动**

```bash
# 启动完整系统
python start.py

# 仅启动后端
python start.py backend

# 仅启动前端
python start.py frontend

# 显示帮助
python start.py help
```

**特性：**
- ✅ 快速启动已编译项目
- ✅ 支持单独启动前端或后端
- ✅ 简洁的日志输出
- ✅ 自动服务检测

### 方式三：Windows批处理脚本

**Windows用户的原生解决方案**

```cmd
# 完整构建和启动
build.bat

# 仅清理项目
build.bat clean

# 仅编译后端
build.bat compile

# 仅启动后端
build.bat backend

# 仅启动前端
build.bat frontend

# 显示帮助
build.bat help
```

**特性：**
- ✅ Windows原生支持
- ✅ 彩色控制台输出
- ✅ 自动打开浏览器选项
- ✅ 独立窗口运行服务

### 方式四：手动启动

**传统的手动启动方式**

#### 启动后端
```bash
cd backend
mvn clean compile spring-boot:run
```

#### 启动前端
```bash
cd frontend
npm install  # 首次运行需要
npm run dev
```

## 🌐 访问地址

启动成功后，可以通过以下地址访问系统：

| 服务 | 地址 | 说明 |
|------|------|------|
| 🏠 系统首页 | http://localhost:3000 | 主要访问入口 |
| 📊 库存统计 | http://localhost:3000/statistics | 数据统计和可视化 |
| 📍 位置统计 | http://localhost:3000/statistics/by-location | 按位置统计 |
| 👥 用户管理 | http://localhost:3000/users | 用户管理功能 |
| 📦 资产管理 | http://localhost:3000/assets | 资产CRUD操作 |
| ⚙️ 产品类型管理 | http://localhost:3000/settings/product-types | 产品类型配置 |
| 🔧 后端API | http://localhost:8080/api | RESTful API接口 |

## 🔧 端口配置

| 服务 | 默认端口 | 配置文件 |
|------|----------|----------|
| 后端服务 | 8080 | `backend/src/main/resources/application.yml` |
| 前端服务 | 3000 | `frontend/vite.config.js` |
| MySQL数据库 | 3306 | `backend/src/main/resources/application.yml` |

## 📝 使用建议

### 开发环境
- 推荐使用 `python build.py` 进行完整构建
- 使用 `python start.py` 进行快速重启
- 开发时可以单独启动前端或后端进行调试

### 生产环境
- 使用 `mvn clean package` 构建JAR包
- 使用 `npm run build` 构建前端静态文件
- 配置Nginx反向代理和负载均衡

### Windows用户
- 推荐使用 `build.bat` 批处理脚本
- 脚本会自动检查依赖并提供友好的错误提示
- 支持在独立窗口中运行服务

## 🐛 常见问题

### 1. 端口被占用
```bash
# 查看端口占用情况
netstat -ano | findstr :8080
netstat -ano | findstr :3000

# 杀死占用进程 (Windows)
taskkill /PID <进程ID> /F

# 杀死占用进程 (Linux/Mac)
kill -9 <进程ID>
```

### 2. 依赖安装失败
```bash
# 清理Maven缓存
mvn dependency:purge-local-repository

# 清理NPM缓存
npm cache clean --force

# 重新安装依赖
cd frontend && npm install
```

### 3. 数据库连接失败
- 检查MySQL服务是否启动
- 确认数据库连接配置正确
- 检查防火墙设置

### 4. 编译错误
- 确保Java版本兼容 (Java 8+)
- 检查Maven版本 (3.6+)
- 清理项目后重新编译

## 📞 技术支持

如果遇到问题，请按以下顺序排查：

1. **检查系统要求** - 确保所有依赖已正确安装
2. **查看日志输出** - 脚本会提供详细的错误信息
3. **检查端口占用** - 确保8080和3000端口可用
4. **清理重建** - 使用clean命令清理后重新构建

## 🎯 最佳实践

### 开发流程
1. 首次启动使用 `python build.py` 进行完整构建
2. 日常开发使用 `python start.py` 快速启动
3. 代码修改后前端会自动热重载，后端需要重启
4. 定期使用clean命令清理构建缓存

### 性能优化
- 开发时可以关闭不需要的服务
- 使用SSD硬盘可以显著提升构建速度
- 配置足够的内存给JVM和Node.js

### 安全建议
- 生产环境修改默认端口
- 配置防火墙规则
- 定期更新依赖版本
- 使用HTTPS协议

---

**作者**: 全栈设计大师  
**版本**: 1.0.0  
**更新时间**: 2025-07-27
